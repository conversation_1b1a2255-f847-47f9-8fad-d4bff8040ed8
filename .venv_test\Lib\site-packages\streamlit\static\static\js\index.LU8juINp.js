const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./data-grid-overlay-editor.Ct51iCb_.js","./index.DKN5MVff.js","../css/index.CJVRHjQZ.css","./withFullScreenWrapper.C-gXt0Rl.js","./Toolbar.Dt4jIKlY.js","./FormClearHelper.DF4gFAOO.js","./mergeWith.GRNk8iwv.js","./sprintf.D7DtBTRn.js","./checkbox.Cgxgc0et.js","./createDownloadLinkElement.ZaXNnPK4.js","./toConsumableArray.BJvaP8gb.js","./possibleConstructorReturn.Bd4ImlQ9.js","./createSuper.siQeagI2.js","./FileDownload.esm.Bz9nxNC5.js","./number-overlay-editor.DXS2qb1U.js","./es6.CMaUdEZ5.js"])))=>i.map(i=>d[i]);
import{r as f,E as Ls,_ as Ne,d as fh,g as lr,i as Ua,e as uo,f as hh,h as gh,m as mh,n as Tc,o as Dc,R as Vt,p as ph,q as Oc,t as vh,v as bh,w as wh,x as yh,y as Ch,z as Sh,c as kr,A as As,B as xh,C as kh,D as He,G as Pc,H as lt,I as Yi,J as $r,K as qa,j as Ue,M as Mh,N as Rh,s as Un,O as Ih,Q as Ol,S as Eh,T as Th,U as Dh,V as Oh,W as _c,X as Ph,Y as _h,Z as Cs,$ as Fc,a0 as Lc,a1 as Fh,a2 as Lh,a3 as Ah,a4 as Hh,a5 as zh,a6 as Vh,a7 as $h,a8 as Nh,a9 as Bh,aa as Ac,ab as Wh,ac as Uh,ad as qh,ae as Gh,af as Xh,ag as Yh,ah as jh,ai as Kh,aj as Zh,ak as Jh,al as Qh,am as eg,an as tg,ao as ng,ap as Wr,aq as an,ar as wn,l as pa,as as ji,at as ur,au as _e,av as rg,b as Hc,aw as zc,k as ig,ax as ui,ay as og,az as ag,aA as Ga,F as Vc,aB as va,aC as $c,aD as ba,aE as vn,aF as Bn,aG as wa,aH as Nc,aI as sg,aJ as lg,aK as Pl,aL as ug,aM as cg,L as dg,aN as fg}from"./index.DKN5MVff.js";import{w as hg,E as gg}from"./withFullScreenWrapper.C-gXt0Rl.js";import{T as mg,a as si}from"./Toolbar.Dt4jIKlY.js";import{u as pg}from"./FormClearHelper.DF4gFAOO.js";import{m as vg}from"./mergeWith.GRNk8iwv.js";import{s as bg}from"./sprintf.D7DtBTRn.js";import{L as wg,S as yg,a as Cg}from"./checkbox.Cgxgc0et.js";import{c as Sg}from"./createDownloadLinkElement.ZaXNnPK4.js";import{_ as or,a as Hs,C as xg}from"./toConsumableArray.BJvaP8gb.js";import{_ as kg,a as Mg,b as Rg}from"./possibleConstructorReturn.Bd4ImlQ9.js";import{_ as Ig}from"./createSuper.siQeagI2.js";import{D as Eg,F as Tg}from"./FileDownload.esm.Bz9nxNC5.js";var Bc=f.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return f.createElement(Ls,Ne({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),f.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),f.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))});Bc.displayName="Add";var Wc=f.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return f.createElement(Ls,Ne({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),f.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),f.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))});Wc.displayName="Search";var Uc=f.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return f.createElement(Ls,Ne({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),f.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),f.createElement("path",{d:"M12 6a9.77 9.77 0 018.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0112 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 010 5 2.5 2.5 0 010-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"}))});Uc.displayName="Visibility";var Xa,_l;function Dg(){if(_l)return Xa;_l=1;var e=Object.prototype,t=e.hasOwnProperty;function n(r,i){return r!=null&&t.call(r,i)}return Xa=n,Xa}var Ya,Fl;function Og(){if(Fl)return Ya;Fl=1;var e=Dg(),t=fh();function n(r,i){return r!=null&&t(r,i,e)}return Ya=n,Ya}var Pg=Og();const _g=lr(Pg);function qc(e="This should not happen"){throw new Error(e)}function Dn(e,t="Assertion failed"){if(!e)return qc(t)}function no(e,t){return qc(t??"Hell froze over")}function Fg(e,t){try{return e()}catch{return t}}const Ll=Object.prototype.hasOwnProperty;function vi(e,t){let n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&vi(e[r],t[r]););return r===-1}if(!n||typeof e=="object"){r=0;for(n in e)if(Ll.call(e,n)&&++r&&!Ll.call(t,n)||!(n in t)||!vi(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}const ta=null,zs=void 0;var J;(function(e){e.Uri="uri",e.Text="text",e.Image="image",e.RowID="row-id",e.Number="number",e.Bubble="bubble",e.Boolean="boolean",e.Loading="loading",e.Markdown="markdown",e.Drilldown="drilldown",e.Protected="protected",e.Custom="custom"})(J||(J={}));var Al;(function(e){e.HeaderRowID="headerRowID",e.HeaderCode="headerCode",e.HeaderNumber="headerNumber",e.HeaderString="headerString",e.HeaderBoolean="headerBoolean",e.HeaderAudioUri="headerAudioUri",e.HeaderVideoUri="headerVideoUri",e.HeaderEmoji="headerEmoji",e.HeaderImage="headerImage",e.HeaderUri="headerUri",e.HeaderPhone="headerPhone",e.HeaderMarkdown="headerMarkdown",e.HeaderDate="headerDate",e.HeaderTime="headerTime",e.HeaderEmail="headerEmail",e.HeaderReference="headerReference",e.HeaderIfThenElse="headerIfThenElse",e.HeaderSingleValue="headerSingleValue",e.HeaderLookup="headerLookup",e.HeaderTextTemplate="headerTextTemplate",e.HeaderMath="headerMath",e.HeaderRollup="headerRollup",e.HeaderJoinStrings="headerJoinStrings",e.HeaderSplitString="headerSplitString",e.HeaderGeoDistance="headerGeoDistance",e.HeaderArray="headerArray",e.RowOwnerOverlay="rowOwnerOverlay",e.ProtectedColumnOverlay="protectedColumnOverlay"})(Al||(Al={}));var na;(function(e){e.Triangle="triangle",e.Dots="dots"})(na||(na={}));function Fo(e){return"width"in e&&typeof e.width=="number"}async function Hl(e){return typeof e=="object"?e:await e()}function ci(e){return!(e.kind===J.Loading||e.kind===J.Bubble||e.kind===J.RowID||e.kind===J.Protected||e.kind===J.Drilldown)}function hi(e){return e.kind===Hn.Marker||e.kind===Hn.NewRow}function di(e){if(!ci(e)||e.kind===J.Image)return!1;switch(e.kind){case J.Text:case J.Number:case J.Markdown:case J.Uri:case J.Custom:case J.Boolean:return e.readonly!==!0;default:no(e,"A cell was passed with an invalid kind")}}function Lg(e){return _g(e,"editor")}function Vs(e){return!(e.readonly??!1)}var Hn;(function(e){e.NewRow="new-row",e.Marker="marker"})(Hn||(Hn={}));function Ag(e){if(e.length===0)return[];const t=[...e],n=[];t.sort(function(r,i){return r[0]-i[0]}),n.push([...t[0]]);for(const r of t.slice(1)){const i=n[n.length-1];i[1]<r[0]?n.push([...r]):i[1]<r[1]&&(i[1]=r[1])}return n}let zl;class ot{items;constructor(t){this.items=t}static empty=()=>zl??(zl=new ot([]));static fromSingleSelection=t=>ot.empty().add(t);offset(t){if(t===0)return this;const n=this.items.map(r=>[r[0]+t,r[1]+t]);return new ot(n)}add(t){const n=typeof t=="number"?[t,t+1]:t,r=Ag([...this.items,n]);return new ot(r)}remove(t){const n=[...this.items],r=typeof t=="number"?t:t[0],i=typeof t=="number"?t+1:t[1];for(const[o,s]of n.entries()){const[a,l]=s;if(a<=i&&r<=l){const u=[];a<r&&u.push([a,r]),i<l&&u.push([i,l]),n.splice(o,1,...u)}}return new ot(n)}first(){if(this.items.length!==0)return this.items[0][0]}last(){if(this.items.length!==0)return this.items.slice(-1)[0][1]-1}hasIndex(t){for(let n=0;n<this.items.length;n++){const[r,i]=this.items[n];if(t>=r&&t<i)return!0}return!1}hasAll(t){for(let n=t[0];n<t[1];n++)if(!this.hasIndex(n))return!1;return!0}some(t){for(const n of this)if(t(n))return!0;return!1}equals(t){if(t===this)return!0;if(t.items.length!==this.items.length)return!1;for(let n=0;n<this.items.length;n++){const r=t.items[n],i=this.items[n];if(r[0]!==i[0]||r[1]!==i[1])return!1}return!0}toArray(){const t=[];for(const[n,r]of this.items)for(let i=n;i<r;i++)t.push(i);return t}get length(){let t=0;for(const[n,r]of this.items)t+=r-n;return t}*[Symbol.iterator](){for(const[t,n]of this.items)for(let r=t;r<n;r++)yield r}}var Hg=function(){const t=Array.prototype.slice.call(arguments).filter(Boolean),n={},r=[];t.forEach(o=>{(o?o.split(" "):[]).forEach(a=>{if(a.startsWith("atm_")){const[,l]=a.split("_");n[l]=a}else r.push(a)})});const i=[];for(const o in n)Object.prototype.hasOwnProperty.call(n,o)&&i.push(n[o]);return i.push(...r),i.join(" ")},Vl=Hg,zg=e=>e.toUpperCase()===e,Vg=e=>t=>e.indexOf(t)===-1,Gc=(e,t)=>{const n={};return Object.keys(e).filter(Vg(t)).forEach(r=>{n[r]=e[r]}),n};function $g(e,t,n){const r=Gc(t,n);if(!e){const i=typeof Ua=="function"?{default:Ua}:Ua;Object.keys(r).forEach(o=>{i.default(o)||delete r[o]})}return r}var Ng=(e,t)=>{};function Bg(e){let t="";return n=>{const r=(o,s)=>{const{as:a=e,class:l=t}=o,u=n.propsAsIs===void 0?!(typeof a=="string"&&a.indexOf("-")===-1&&!zg(a[0])):n.propsAsIs,c=$g(u,o,["as","class"]);c.ref=s,c.className=n.atomic?Vl(n.class,c.className||l):Vl(c.className||l,n.class);const{vars:d}=n;if(d){const g={};for(const p in d){const v=d[p],w=v[0],b=v[1]||"",x=typeof w=="function"?w(o):w;Ng(x,n.name),g[`--${p}`]=`${x}${b}`}const h=c.style||{},m=Object.keys(h);m.length>0&&m.forEach(p=>{g[p]=h[p]}),c.style=g}return e.__wyw_meta&&e!==a?(c.as=a,f.createElement(e,c)):f.createElement(a,c)},i=f.forwardRef?f.forwardRef(r):o=>{const s=Gc(o,["innerRef"]);return r(s,o.innerRef)};return i.displayName=n.name,i.__wyw_meta={className:n.class||t,extends:e},i}}var un=Bg;const Wg=un("div")({name:"ImageOverlayEditorStyle",class:"gdg-i2iowwq",propsAsIs:!1});var ja={},Ai={},Lo={},Ao={},$l;function Ug(){return $l||($l=1,function(e){(function(t,n){n(e,uo(),hh())})(Ao,function(t,n,r){Object.defineProperty(t,"__esModule",{value:!0}),t.setHasSupportToCaptureOption=m;var i=s(n),o=s(r);function s(b){return b&&b.__esModule?b:{default:b}}var a=Object.assign||function(b){for(var x=1;x<arguments.length;x++){var O=arguments[x];for(var R in O)Object.prototype.hasOwnProperty.call(O,R)&&(b[R]=O[R])}return b};function l(b,x){var O={};for(var R in b)x.indexOf(R)>=0||Object.prototype.hasOwnProperty.call(b,R)&&(O[R]=b[R]);return O}function u(b,x){if(!(b instanceof x))throw new TypeError("Cannot call a class as a function")}var c=function(){function b(x,O){for(var R=0;R<O.length;R++){var M=O[R];M.enumerable=M.enumerable||!1,M.configurable=!0,"value"in M&&(M.writable=!0),Object.defineProperty(x,M.key,M)}}return function(x,O,R){return O&&b(x.prototype,O),R&&b(x,R),x}}();function d(b,x){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return x&&(typeof x=="object"||typeof x=="function")?x:b}function g(b,x){if(typeof x!="function"&&x!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof x);b.prototype=Object.create(x&&x.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),x&&(Object.setPrototypeOf?Object.setPrototypeOf(b,x):b.__proto__=x)}var h=!1;function m(b){h=b}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){m(!0)}}))}catch{}function p(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{capture:!0};return h?b:b.capture}function v(b){if("touches"in b){var x=b.touches[0],O=x.pageX,R=x.pageY;return{x:O,y:R}}var M=b.screenX,_=b.screenY;return{x:M,y:_}}var w=function(b){g(x,b);function x(){var O;u(this,x);for(var R=arguments.length,M=Array(R),_=0;_<R;_++)M[_]=arguments[_];var I=d(this,(O=x.__proto__||Object.getPrototypeOf(x)).call.apply(O,[this].concat(M)));return I._handleSwipeStart=I._handleSwipeStart.bind(I),I._handleSwipeMove=I._handleSwipeMove.bind(I),I._handleSwipeEnd=I._handleSwipeEnd.bind(I),I._onMouseDown=I._onMouseDown.bind(I),I._onMouseMove=I._onMouseMove.bind(I),I._onMouseUp=I._onMouseUp.bind(I),I._setSwiperRef=I._setSwiperRef.bind(I),I}return c(x,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(R){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(R))}},{key:"_onMouseMove",value:function(R){this.mouseDown&&this._handleSwipeMove(R)}},{key:"_onMouseUp",value:function(R){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(R)}},{key:"_handleSwipeStart",value:function(R){var M=v(R),_=M.x,I=M.y;this.moveStart={x:_,y:I},this.props.onSwipeStart(R)}},{key:"_handleSwipeMove",value:function(R){if(this.moveStart){var M=v(R),_=M.x,I=M.y,k=_-this.moveStart.x,A=I-this.moveStart.y;this.moving=!0;var D=this.props.onSwipeMove({x:k,y:A},R);D&&R.cancelable&&R.preventDefault(),this.movePosition={deltaX:k,deltaY:A}}}},{key:"_handleSwipeEnd",value:function(R){this.props.onSwipeEnd(R);var M=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-M?this.props.onSwipeLeft(1,R):this.movePosition.deltaX>M&&this.props.onSwipeRight(1,R),this.movePosition.deltaY<-M?this.props.onSwipeUp(1,R):this.movePosition.deltaY>M&&this.props.onSwipeDown(1,R)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(R){this.swiper=R,this.props.innerRef(R)}},{key:"render",value:function(){var R=this.props;R.tagName;var M=R.className,_=R.style,I=R.children;R.allowMouseEvents,R.onSwipeUp,R.onSwipeDown,R.onSwipeLeft,R.onSwipeRight,R.onSwipeStart,R.onSwipeMove,R.onSwipeEnd,R.innerRef,R.tolerance;var k=l(R,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]);return i.default.createElement(this.props.tagName,a({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:M,style:_},k),I)}}]),x}(n.Component);w.displayName="ReactSwipe",w.propTypes={tagName:o.default.string,className:o.default.string,style:o.default.object,children:o.default.node,allowMouseEvents:o.default.bool,onSwipeUp:o.default.func,onSwipeDown:o.default.func,onSwipeLeft:o.default.func,onSwipeRight:o.default.func,onSwipeStart:o.default.func,onSwipeMove:o.default.func,onSwipeEnd:o.default.func,innerRef:o.default.func,tolerance:o.default.number.isRequired},w.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},t.default=w})}(Ao)),Ao}var Nl;function Xc(){return Nl||(Nl=1,function(e){(function(t,n){n(e,Ug())})(Lo,function(t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}t.default=r.default})}(Lo)),Lo}var Hi={},Bl;function Yc(){if(Bl)return Hi;Bl=1,Object.defineProperty(Hi,"__esModule",{value:!0}),Hi.default=void 0;var e=t(gh());function t(i){return i&&i.__esModule?i:{default:i}}function n(i,o,s){return o in i?Object.defineProperty(i,o,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[o]=s,i}var r={ROOT:function(o){return(0,e.default)(n({"carousel-root":!0},o||"",!!o))},CAROUSEL:function(o){return(0,e.default)({carousel:!0,"carousel-slider":o})},WRAPPER:function(o,s){return(0,e.default)({"thumbs-wrapper":!o,"slider-wrapper":o,"axis-horizontal":s==="horizontal","axis-vertical":s!=="horizontal"})},SLIDER:function(o,s){return(0,e.default)({thumbs:!o,slider:o,animated:!s})},ITEM:function(o,s,a){return(0,e.default)({thumb:!o,slide:o,selected:s,previous:a})},ARROW_PREV:function(o){return(0,e.default)({"control-arrow control-prev":!0,"control-disabled":o})},ARROW_NEXT:function(o){return(0,e.default)({"control-arrow control-next":!0,"control-disabled":o})},DOT:function(o){return(0,e.default)({dot:!0,selected:o})}};return Hi.default=r,Hi}var zi={},Vi={},Wl;function qg(){if(Wl)return Vi;Wl=1,Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.outerWidth=void 0;var e=function(n){var r=n.offsetWidth,i=getComputedStyle(n);return r+=parseInt(i.marginLeft)+parseInt(i.marginRight),r};return Vi.outerWidth=e,Vi}var $i={},Ul;function $s(){if(Ul)return $i;Ul=1,Object.defineProperty($i,"__esModule",{value:!0}),$i.default=void 0;var e=function(n,r,i){var o=n===0?n:n+r,s=i==="horizontal"?[o,0,0]:[0,o,0],a="translate3d",l="("+s.join(",")+")";return a+l};return $i.default=e,$i}var Ni={},ql;function jc(){if(ql)return Ni;ql=1,Object.defineProperty(Ni,"__esModule",{value:!0}),Ni.default=void 0;var e=function(){return window};return Ni.default=e,Ni}var Gl;function Kc(){if(Gl)return zi;Gl=1,Object.defineProperty(zi,"__esModule",{value:!0}),zi.default=void 0;var e=l(uo()),t=s(Yc()),n=qg(),r=s($s()),i=s(Xc()),o=s(jc());function s(I){return I&&I.__esModule?I:{default:I}}function a(){if(typeof WeakMap!="function")return null;var I=new WeakMap;return a=function(){return I},I}function l(I){if(I&&I.__esModule)return I;if(I===null||u(I)!=="object"&&typeof I!="function")return{default:I};var k=a();if(k&&k.has(I))return k.get(I);var A={},D=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var C in I)if(Object.prototype.hasOwnProperty.call(I,C)){var E=D?Object.getOwnPropertyDescriptor(I,C):null;E&&(E.get||E.set)?Object.defineProperty(A,C,E):A[C]=I[C]}return A.default=I,k&&k.set(I,A),A}function u(I){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?u=function(A){return typeof A}:u=function(A){return A&&typeof Symbol=="function"&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},u(I)}function c(){return c=Object.assign||function(I){for(var k=1;k<arguments.length;k++){var A=arguments[k];for(var D in A)Object.prototype.hasOwnProperty.call(A,D)&&(I[D]=A[D])}return I},c.apply(this,arguments)}function d(I,k){if(!(I instanceof k))throw new TypeError("Cannot call a class as a function")}function g(I,k){for(var A=0;A<k.length;A++){var D=k[A];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(I,D.key,D)}}function h(I,k,A){return k&&g(I.prototype,k),I}function m(I,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");I.prototype=Object.create(k&&k.prototype,{constructor:{value:I,writable:!0,configurable:!0}}),k&&p(I,k)}function p(I,k){return p=Object.setPrototypeOf||function(D,C){return D.__proto__=C,D},p(I,k)}function v(I){var k=x();return function(){var D=O(I),C;if(k){var E=O(this).constructor;C=Reflect.construct(D,arguments,E)}else C=D.apply(this,arguments);return w(this,C)}}function w(I,k){return k&&(u(k)==="object"||typeof k=="function")?k:b(I)}function b(I){if(I===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return I}function x(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function O(I){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(A){return A.__proto__||Object.getPrototypeOf(A)},O(I)}function R(I,k,A){return k in I?Object.defineProperty(I,k,{value:A,enumerable:!0,configurable:!0,writable:!0}):I[k]=A,I}var M=function(k){return k.hasOwnProperty("key")},_=function(I){m(A,I);var k=v(A);function A(D){var C;return d(this,A),C=k.call(this,D),R(b(C),"itemsWrapperRef",void 0),R(b(C),"itemsListRef",void 0),R(b(C),"thumbsRef",void 0),R(b(C),"setItemsWrapperRef",function(E){C.itemsWrapperRef=E}),R(b(C),"setItemsListRef",function(E){C.itemsListRef=E}),R(b(C),"setThumbsRef",function(E,T){C.thumbsRef||(C.thumbsRef=[]),C.thumbsRef[T]=E}),R(b(C),"updateSizes",function(){if(!(!C.props.children||!C.itemsWrapperRef||!C.thumbsRef)){var E=e.Children.count(C.props.children),T=C.itemsWrapperRef.clientWidth,S=C.props.thumbWidth?C.props.thumbWidth:(0,n.outerWidth)(C.thumbsRef[0]),B=Math.floor(T/S),X=B<E,Y=X?E-B:0;C.setState(function(ae,Q){return{itemSize:S,visibleItems:B,firstItem:X?C.getFirstItem(Q.selectedItem):0,lastPosition:Y,showArrows:X}})}}),R(b(C),"handleClickItem",function(E,T,S){if(!M(S)||S.key==="Enter"){var B=C.props.onSelectItem;typeof B=="function"&&B(E,T)}}),R(b(C),"onSwipeStart",function(){C.setState({swiping:!0})}),R(b(C),"onSwipeEnd",function(){C.setState({swiping:!1})}),R(b(C),"onSwipeMove",function(E){var T=E.x;if(!C.state.itemSize||!C.itemsWrapperRef||!C.state.visibleItems)return!1;var S=0,B=e.Children.count(C.props.children),X=-(C.state.firstItem*100)/C.state.visibleItems,Y=Math.max(B-C.state.visibleItems,0),ae=-Y*100/C.state.visibleItems;X===S&&T>0&&(T=0),X===ae&&T<0&&(T=0);var Q=C.itemsWrapperRef.clientWidth,ee=X+100/(Q/T);return C.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(re){C.itemsListRef.style[re]=(0,r.default)(ee,"%",C.props.axis)}),!0}),R(b(C),"slideRight",function(E){C.moveTo(C.state.firstItem-(typeof E=="number"?E:1))}),R(b(C),"slideLeft",function(E){C.moveTo(C.state.firstItem+(typeof E=="number"?E:1))}),R(b(C),"moveTo",function(E){E=E<0?0:E,E=E>=C.state.lastPosition?C.state.lastPosition:E,C.setState({firstItem:E})}),C.state={selectedItem:D.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},C}return h(A,[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(C){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==C.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(C){var E=C;return C>=this.state.lastPosition&&(E=this.state.lastPosition),C<this.state.firstItem+this.state.visibleItems&&(E=this.state.firstItem),C<this.state.firstItem&&(E=C),E}},{key:"renderItems",value:function(){var C=this;return this.props.children.map(function(E,T){var S=t.default.ITEM(!1,T===C.state.selectedItem),B={key:T,ref:function(Y){return C.setThumbsRef(Y,T)},className:S,onClick:C.handleClickItem.bind(C,T,C.props.children[T]),onKeyDown:C.handleClickItem.bind(C,T,C.props.children[T]),"aria-label":"".concat(C.props.labels.item," ").concat(T+1),style:{width:C.props.thumbWidth}};return e.default.createElement("li",c({},B,{role:"button",tabIndex:0}),E)})}},{key:"render",value:function(){var C=this;if(!this.props.children)return null;var E=e.Children.count(this.props.children)>1,T=this.state.showArrows&&this.state.firstItem>0,S=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,B={},X=-this.state.firstItem*(this.state.itemSize||0),Y=(0,r.default)(X,"px",this.props.axis),ae=this.props.transitionTime+"ms";return B={WebkitTransform:Y,MozTransform:Y,MsTransform:Y,OTransform:Y,transform:Y,msTransform:Y,WebkitTransitionDuration:ae,MozTransitionDuration:ae,MsTransitionDuration:ae,OTransitionDuration:ae,transitionDuration:ae,msTransitionDuration:ae},e.default.createElement("div",{className:t.default.CAROUSEL(!1)},e.default.createElement("div",{className:t.default.WRAPPER(!1),ref:this.setItemsWrapperRef},e.default.createElement("button",{type:"button",className:t.default.ARROW_PREV(!T),onClick:function(){return C.slideRight()},"aria-label":this.props.labels.leftArrow}),E?e.default.createElement(i.default,{tagName:"ul",className:t.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:B,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):e.default.createElement("ul",{className:t.default.SLIDER(!1,this.state.swiping),ref:function(ee){return C.setItemsListRef(ee)},style:B},this.renderItems()),e.default.createElement("button",{type:"button",className:t.default.ARROW_NEXT(!S),onClick:function(){return C.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}]),A}(e.Component);return zi.default=_,R(_,"displayName","Thumbs"),R(_,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350}),zi}var Bi={},Xl;function Gg(){if(Xl)return Bi;Xl=1,Object.defineProperty(Bi,"__esModule",{value:!0}),Bi.default=void 0;var e=function(){return document};return Bi.default=e,Bi}var Tn={},Yl;function Zc(){if(Yl)return Tn;Yl=1,Object.defineProperty(Tn,"__esModule",{value:!0}),Tn.setPosition=Tn.getPosition=Tn.isKeyboardEvent=Tn.defaultStatusFormatter=Tn.noop=void 0;var e=uo(),t=n($s());function n(l){return l&&l.__esModule?l:{default:l}}var r=function(){};Tn.noop=r;var i=function(u,c){return"".concat(u," of ").concat(c)};Tn.defaultStatusFormatter=i;var o=function(u){return u?u.hasOwnProperty("key"):!1};Tn.isKeyboardEvent=o;var s=function(u,c){if(c.infiniteLoop&&++u,u===0)return 0;var d=e.Children.count(c.children);if(c.centerMode&&c.axis==="horizontal"){var g=-u*c.centerSlidePercentage,h=d-1;return u&&(u!==h||c.infiniteLoop)?g+=(100-c.centerSlidePercentage)/2:u===h&&(g+=100-c.centerSlidePercentage),g}return-u*100};Tn.getPosition=s;var a=function(u,c){var d={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(g){d[g]=(0,t.default)(u,"%",c)}),d};return Tn.setPosition=a,Tn}var Nn={},jl;function Xg(){if(jl)return Nn;jl=1,Object.defineProperty(Nn,"__esModule",{value:!0}),Nn.fadeAnimationHandler=Nn.slideStopSwipingHandler=Nn.slideSwipeAnimationHandler=Nn.slideAnimationHandler=void 0;var e=uo(),t=r($s()),n=Zc();function r(d){return d&&d.__esModule?d:{default:d}}function i(d,g){var h=Object.keys(d);if(Object.getOwnPropertySymbols){var m=Object.getOwnPropertySymbols(d);g&&(m=m.filter(function(p){return Object.getOwnPropertyDescriptor(d,p).enumerable})),h.push.apply(h,m)}return h}function o(d){for(var g=1;g<arguments.length;g++){var h=arguments[g]!=null?arguments[g]:{};g%2?i(Object(h),!0).forEach(function(m){s(d,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(d,Object.getOwnPropertyDescriptors(h)):i(Object(h)).forEach(function(m){Object.defineProperty(d,m,Object.getOwnPropertyDescriptor(h,m))})}return d}function s(d,g,h){return g in d?Object.defineProperty(d,g,{value:h,enumerable:!0,configurable:!0,writable:!0}):d[g]=h,d}var a=function(g,h){var m={},p=h.selectedItem,v=p,w=e.Children.count(g.children)-1,b=g.infiniteLoop&&(p<0||p>w);if(b)return v<0?g.centerMode&&g.centerSlidePercentage&&g.axis==="horizontal"?m.itemListStyle=(0,n.setPosition)(-(w+2)*g.centerSlidePercentage-(100-g.centerSlidePercentage)/2,g.axis):m.itemListStyle=(0,n.setPosition)(-(w+2)*100,g.axis):v>w&&(m.itemListStyle=(0,n.setPosition)(0,g.axis)),m;var x=(0,n.getPosition)(p,g),O=(0,t.default)(x,"%",g.axis),R=g.transitionTime+"ms";return m.itemListStyle={WebkitTransform:O,msTransform:O,OTransform:O,transform:O},h.swiping||(m.itemListStyle=o(o({},m.itemListStyle),{},{WebkitTransitionDuration:R,MozTransitionDuration:R,OTransitionDuration:R,transitionDuration:R,msTransitionDuration:R})),m};Nn.slideAnimationHandler=a;var l=function(g,h,m,p){var v={},w=h.axis==="horizontal",b=e.Children.count(h.children),x=0,O=(0,n.getPosition)(m.selectedItem,h),R=h.infiniteLoop?(0,n.getPosition)(b-1,h)-100:(0,n.getPosition)(b-1,h),M=w?g.x:g.y,_=M;O===x&&M>0&&(_=0),O===R&&M<0&&(_=0);var I=O+100/(m.itemSize/_),k=Math.abs(M)>h.swipeScrollTolerance;return h.infiniteLoop&&k&&(m.selectedItem===0&&I>-100?I-=b*100:m.selectedItem===b-1&&I<-b*100&&(I+=b*100)),(!h.preventMovementUntilSwipeScrollTolerance||k||m.swipeMovementStarted)&&(m.swipeMovementStarted||p({swipeMovementStarted:!0}),v.itemListStyle=(0,n.setPosition)(I,h.axis)),k&&!m.cancelClick&&p({cancelClick:!0}),v};Nn.slideSwipeAnimationHandler=l;var u=function(g,h){var m=(0,n.getPosition)(h.selectedItem,g),p=(0,n.setPosition)(m,g.axis);return{itemListStyle:p}};Nn.slideStopSwipingHandler=u;var c=function(g,h){var m=g.transitionTime+"ms",p="ease-in-out",v={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:p,msTransitionTimingFunction:p,MozTransitionTimingFunction:p,WebkitTransitionTimingFunction:p,OTransitionTimingFunction:p};return h.swiping||(v=o(o({},v),{},{WebkitTransitionDuration:m,MozTransitionDuration:m,OTransitionDuration:m,transitionDuration:m,msTransitionDuration:m})),{slideStyle:v,selectedStyle:o(o({},v),{},{opacity:1,position:"relative"}),prevStyle:o({},v)}};return Nn.fadeAnimationHandler=c,Nn}var Kl;function Yg(){if(Kl)return Ai;Kl=1,Object.defineProperty(Ai,"__esModule",{value:!0}),Ai.default=void 0;var e=c(uo()),t=l(Xc()),n=l(Yc()),r=l(Kc()),i=l(Gg()),o=l(jc()),s=Zc(),a=Xg();function l(D){return D&&D.__esModule?D:{default:D}}function u(){if(typeof WeakMap!="function")return null;var D=new WeakMap;return u=function(){return D},D}function c(D){if(D&&D.__esModule)return D;if(D===null||d(D)!=="object"&&typeof D!="function")return{default:D};var C=u();if(C&&C.has(D))return C.get(D);var E={},T=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var S in D)if(Object.prototype.hasOwnProperty.call(D,S)){var B=T?Object.getOwnPropertyDescriptor(D,S):null;B&&(B.get||B.set)?Object.defineProperty(E,S,B):E[S]=D[S]}return E.default=D,C&&C.set(D,E),E}function d(D){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?d=function(E){return typeof E}:d=function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},d(D)}function g(){return g=Object.assign||function(D){for(var C=1;C<arguments.length;C++){var E=arguments[C];for(var T in E)Object.prototype.hasOwnProperty.call(E,T)&&(D[T]=E[T])}return D},g.apply(this,arguments)}function h(D,C){var E=Object.keys(D);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(D);C&&(T=T.filter(function(S){return Object.getOwnPropertyDescriptor(D,S).enumerable})),E.push.apply(E,T)}return E}function m(D){for(var C=1;C<arguments.length;C++){var E=arguments[C]!=null?arguments[C]:{};C%2?h(Object(E),!0).forEach(function(T){k(D,T,E[T])}):Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(E)):h(Object(E)).forEach(function(T){Object.defineProperty(D,T,Object.getOwnPropertyDescriptor(E,T))})}return D}function p(D,C){if(!(D instanceof C))throw new TypeError("Cannot call a class as a function")}function v(D,C){for(var E=0;E<C.length;E++){var T=C[E];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(D,T.key,T)}}function w(D,C,E){return C&&v(D.prototype,C),D}function b(D,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function");D.prototype=Object.create(C&&C.prototype,{constructor:{value:D,writable:!0,configurable:!0}}),C&&x(D,C)}function x(D,C){return x=Object.setPrototypeOf||function(T,S){return T.__proto__=S,T},x(D,C)}function O(D){var C=_();return function(){var T=I(D),S;if(C){var B=I(this).constructor;S=Reflect.construct(T,arguments,B)}else S=T.apply(this,arguments);return R(this,S)}}function R(D,C){return C&&(d(C)==="object"||typeof C=="function")?C:M(D)}function M(D){if(D===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return D}function _(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function I(D){return I=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)},I(D)}function k(D,C,E){return C in D?Object.defineProperty(D,C,{value:E,enumerable:!0,configurable:!0,writable:!0}):D[C]=E,D}var A=function(D){b(E,D);var C=O(E);function E(T){var S;p(this,E),S=C.call(this,T),k(M(S),"thumbsRef",void 0),k(M(S),"carouselWrapperRef",void 0),k(M(S),"listRef",void 0),k(M(S),"itemsRef",void 0),k(M(S),"timer",void 0),k(M(S),"animationHandler",void 0),k(M(S),"setThumbsRef",function(X){S.thumbsRef=X}),k(M(S),"setCarouselWrapperRef",function(X){S.carouselWrapperRef=X}),k(M(S),"setListRef",function(X){S.listRef=X}),k(M(S),"setItemsRef",function(X,Y){S.itemsRef||(S.itemsRef=[]),S.itemsRef[Y]=X}),k(M(S),"autoPlay",function(){e.Children.count(S.props.children)<=1||(S.clearAutoPlay(),S.props.autoPlay&&(S.timer=setTimeout(function(){S.increment()},S.props.interval)))}),k(M(S),"clearAutoPlay",function(){S.timer&&clearTimeout(S.timer)}),k(M(S),"resetAutoPlay",function(){S.clearAutoPlay(),S.autoPlay()}),k(M(S),"stopOnHover",function(){S.setState({isMouseEntered:!0},S.clearAutoPlay)}),k(M(S),"startOnLeave",function(){S.setState({isMouseEntered:!1},S.autoPlay)}),k(M(S),"isFocusWithinTheCarousel",function(){return S.carouselWrapperRef?!!((0,i.default)().activeElement===S.carouselWrapperRef||S.carouselWrapperRef.contains((0,i.default)().activeElement)):!1}),k(M(S),"navigateWithKeyboard",function(X){if(S.isFocusWithinTheCarousel()){var Y=S.props.axis,ae=Y==="horizontal",Q={ArrowUp:38,ArrowRight:39,ArrowDown:40,ArrowLeft:37},ee=ae?Q.ArrowRight:Q.ArrowDown,re=ae?Q.ArrowLeft:Q.ArrowUp;ee===X.keyCode?S.increment():re===X.keyCode&&S.decrement()}}),k(M(S),"updateSizes",function(){if(!(!S.state.initialized||!S.itemsRef||S.itemsRef.length===0)){var X=S.props.axis==="horizontal",Y=S.itemsRef[0];if(Y){var ae=X?Y.clientWidth:Y.clientHeight;S.setState({itemSize:ae}),S.thumbsRef&&S.thumbsRef.updateSizes()}}}),k(M(S),"setMountState",function(){S.setState({hasMount:!0}),S.updateSizes()}),k(M(S),"handleClickItem",function(X,Y){if(e.Children.count(S.props.children)!==0){if(S.state.cancelClick){S.setState({cancelClick:!1});return}S.props.onClickItem(X,Y),X!==S.state.selectedItem&&S.setState({selectedItem:X})}}),k(M(S),"handleOnChange",function(X,Y){e.Children.count(S.props.children)<=1||S.props.onChange(X,Y)}),k(M(S),"handleClickThumb",function(X,Y){S.props.onClickThumb(X,Y),S.moveTo(X)}),k(M(S),"onSwipeStart",function(X){S.setState({swiping:!0}),S.props.onSwipeStart(X)}),k(M(S),"onSwipeEnd",function(X){S.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),S.props.onSwipeEnd(X),S.clearAutoPlay(),S.state.autoPlay&&S.autoPlay()}),k(M(S),"onSwipeMove",function(X,Y){S.props.onSwipeMove(Y);var ae=S.props.swipeAnimationHandler(X,S.props,S.state,S.setState.bind(M(S)));return S.setState(m({},ae)),!!Object.keys(ae).length}),k(M(S),"decrement",function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;S.moveTo(S.state.selectedItem-(typeof X=="number"?X:1))}),k(M(S),"increment",function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;S.moveTo(S.state.selectedItem+(typeof X=="number"?X:1))}),k(M(S),"moveTo",function(X){if(typeof X=="number"){var Y=e.Children.count(S.props.children)-1;X<0&&(X=S.props.infiniteLoop?Y:0),X>Y&&(X=S.props.infiniteLoop?0:Y),S.selectItem({selectedItem:X}),S.state.autoPlay&&S.state.isMouseEntered===!1&&S.resetAutoPlay()}}),k(M(S),"onClickNext",function(){S.increment(1)}),k(M(S),"onClickPrev",function(){S.decrement(1)}),k(M(S),"onSwipeForward",function(){S.increment(1),S.props.emulateTouch&&S.setState({cancelClick:!0})}),k(M(S),"onSwipeBackwards",function(){S.decrement(1),S.props.emulateTouch&&S.setState({cancelClick:!0})}),k(M(S),"changeItem",function(X){return function(Y){(!(0,s.isKeyboardEvent)(Y)||Y.key==="Enter")&&S.moveTo(X)}}),k(M(S),"selectItem",function(X){S.setState(m({previousItem:S.state.selectedItem},X),function(){S.setState(S.animationHandler(S.props,S.state))}),S.handleOnChange(X.selectedItem,e.Children.toArray(S.props.children)[X.selectedItem])}),k(M(S),"getInitialImage",function(){var X=S.props.selectedItem,Y=S.itemsRef&&S.itemsRef[X],ae=Y&&Y.getElementsByTagName("img")||[];return ae[0]}),k(M(S),"getVariableItemHeight",function(X){var Y=S.itemsRef&&S.itemsRef[X];if(S.state.hasMount&&Y&&Y.children.length){var ae=Y.children[0].getElementsByTagName("img")||[];if(ae.length>0){var Q=ae[0];if(!Q.complete){var ee=function ue(){S.forceUpdate(),Q.removeEventListener("load",ue)};Q.addEventListener("load",ee)}}var re=ae[0]||Y.children[0],te=re.clientHeight;return te>0?te:null}return null});var B={initialized:!1,previousItem:T.selectedItem,selectedItem:T.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:T.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return S.animationHandler=typeof T.animationHandler=="function"&&T.animationHandler||T.animationHandler==="fade"&&a.fadeAnimationHandler||a.slideAnimationHandler,S.state=m(m({},B),S.animationHandler(T,B)),S}return w(E,[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(S,B){!S.children&&this.props.children&&!this.state.initialized&&this.setupCarousel(),!S.autoFocus&&this.props.autoFocus&&this.forceFocus(),B.swiping&&!this.state.swiping&&this.setState(m({},this.props.stopSwipingHandler(this.props,this.state))),(S.selectedItem!==this.props.selectedItem||S.centerMode!==this.props.centerMode)&&(this.updateSizes(),this.moveTo(this.props.selectedItem)),S.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var S=this;this.bindEvents(),this.state.autoPlay&&e.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},function(){var B=S.getInitialImage();B&&!B.complete?B.addEventListener("load",S.setMountState):S.setMountState()})}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var S=this.carouselWrapperRef;this.props.stopOnHover&&S&&(S.addEventListener("mouseenter",this.stopOnHover),S.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var S=this.carouselWrapperRef;this.props.stopOnHover&&S&&(S.removeEventListener("mouseenter",this.stopOnHover),S.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,i.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var S=this.getInitialImage();S&&S.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,i.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var S;(S=this.carouselWrapperRef)===null||S===void 0||S.focus()}},{key:"renderItems",value:function(S){var B=this;return this.props.children?e.Children.map(this.props.children,function(X,Y){var ae=Y===B.state.selectedItem,Q=Y===B.state.previousItem,ee=ae&&B.state.selectedStyle||Q&&B.state.prevStyle||B.state.slideStyle||{};B.props.centerMode&&B.props.axis==="horizontal"&&(ee=m(m({},ee),{},{minWidth:B.props.centerSlidePercentage+"%"})),B.state.swiping&&B.state.swipeMovementStarted&&(ee=m(m({},ee),{},{pointerEvents:"none"}));var re={ref:function(ue){return B.setItemsRef(ue,Y)},key:"itemKey"+Y+(S?"clone":""),className:n.default.ITEM(!0,Y===B.state.selectedItem,Y===B.state.previousItem),onClick:B.handleClickItem.bind(B,Y,X),style:ee};return e.default.createElement("li",re,B.props.renderItem(X,{isSelected:Y===B.state.selectedItem,isPrevious:Y===B.state.previousItem}))}):[]}},{key:"renderControls",value:function(){var S=this,B=this.props,X=B.showIndicators,Y=B.labels,ae=B.renderIndicator,Q=B.children;return X?e.default.createElement("ul",{className:"control-dots"},e.Children.map(Q,function(ee,re){return ae&&ae(S.changeItem(re),re===S.state.selectedItem,re,Y.item)})):null}},{key:"renderStatus",value:function(){return this.props.showStatus?e.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,e.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return!this.props.showThumbs||!this.props.children||e.Children.count(this.props.children)===0?null:e.default.createElement(r.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children))}},{key:"render",value:function(){var S=this;if(!this.props.children||e.Children.count(this.props.children)===0)return null;var B=this.props.swipeable&&e.Children.count(this.props.children)>1,X=this.props.axis==="horizontal",Y=this.props.showArrows&&e.Children.count(this.props.children)>1,ae=Y&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,Q=Y&&(this.state.selectedItem<e.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,ee=this.renderItems(!0),re=ee.shift(),te=ee.pop(),ue={className:n.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},fe={};if(X){if(ue.onSwipeLeft=this.onSwipeForward,ue.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var se=this.getVariableItemHeight(this.state.selectedItem);fe.height=se||"auto"}}else ue.onSwipeUp=this.props.verticalSwipe==="natural"?this.onSwipeBackwards:this.onSwipeForward,ue.onSwipeDown=this.props.verticalSwipe==="natural"?this.onSwipeForward:this.onSwipeBackwards,ue.style=m(m({},ue.style),{},{height:this.state.itemSize}),fe.height=this.state.itemSize;return e.default.createElement("div",{"aria-label":this.props.ariaLabel,className:n.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},e.default.createElement("div",{className:n.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,ae,this.props.labels.leftArrow),e.default.createElement("div",{className:n.default.WRAPPER(!0,this.props.axis),style:fe},B?e.default.createElement(t.default,g({tagName:"ul",innerRef:this.setListRef},ue,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&te,this.renderItems(),this.props.infiniteLoop&&re):e.default.createElement("ul",{className:n.default.SLIDER(!0,this.state.swiping),ref:function(P){return S.setListRef(P)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&te,this.renderItems(),this.props.infiniteLoop&&re)),this.props.renderArrowNext(this.onClickNext,Q,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}]),E}(e.default.Component);return Ai.default=A,k(A,"displayName","Carousel"),k(A,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:s.noop,onClickThumb:s.noop,onChange:s.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(C,E,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_PREV(!E),onClick:C})},renderArrowNext:function(C,E,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_NEXT(!E),onClick:C})},renderIndicator:function(C,E,T,S){return e.default.createElement("li",{className:n.default.DOT(E),onClick:C,onKeyDown:C,value:T,key:T,role:"button",tabIndex:0,"aria-label":"".concat(S," ").concat(T+1)})},renderItem:function(C){return C},renderThumbs:function(C){var E=e.Children.map(C,function(T){var S=T;if(T.type!=="img"&&(S=e.Children.toArray(T.props.children).find(function(B){return B.type==="img"})),!!S)return S});return E.filter(function(T){return T}).length===0?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):E},statusFormatter:s.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:a.slideSwipeAnimationHandler,stopSwipingHandler:a.slideStopSwipingHandler}),Ai}var Zl={},Jl;function jg(){return Jl||(Jl=1),Zl}var Ql;function Kg(){return Ql||(Ql=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Carousel",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"CarouselProps",{enumerable:!0,get:function(){return n.CarouselProps}}),Object.defineProperty(e,"Thumbs",{enumerable:!0,get:function(){return r.default}});var t=i(Yg()),n=jg(),r=i(Kc());function i(o){return o&&o.__esModule?o:{default:o}}}(ja)),ja}var Zg=Kg(),Ka,eu;function Jg(){if(eu)return Ka;eu=1;var e=mh(),t=function(){return e.Date.now()};return Ka=t,Ka}var Za,tu;function Jc(){if(tu)return Za;tu=1;var e=Tc(),t=Jg(),n=Dc(),r="Expected a function",i=Math.max,o=Math.min;function s(a,l,u){var c,d,g,h,m,p,v=0,w=!1,b=!1,x=!0;if(typeof a!="function")throw new TypeError(r);l=n(l)||0,e(u)&&(w=!!u.leading,b="maxWait"in u,g=b?i(n(u.maxWait)||0,l):g,x="trailing"in u?!!u.trailing:x);function O(E){var T=c,S=d;return c=d=void 0,v=E,h=a.apply(S,T),h}function R(E){return v=E,m=setTimeout(I,l),w?O(E):h}function M(E){var T=E-p,S=E-v,B=l-T;return b?o(B,g-S):B}function _(E){var T=E-p,S=E-v;return p===void 0||T>=l||T<0||b&&S>=g}function I(){var E=t();if(_(E))return k(E);m=setTimeout(I,M(E))}function k(E){return m=void 0,x&&c?O(E):(c=d=void 0,h)}function A(){m!==void 0&&clearTimeout(m),v=0,c=p=d=m=void 0}function D(){return m===void 0?h:k(t())}function C(){var E=t(),T=_(E);if(c=arguments,d=this,p=E,T){if(m===void 0)return R(p);if(b)return clearTimeout(m),m=setTimeout(I,l),O(p)}return m===void 0&&(m=setTimeout(I,l)),h}return C.cancel=A,C.flush=D,C}return Za=s,Za}var Qg=Jc();const Qc=lr(Qg);function bn(e,t,n,r,i=!1){const o=f.useRef();o.current=t,f.useEffect(()=>{if(n===null||n.addEventListener===void 0)return;const s=n,a=l=>{o.current?.call(s,l)};return s.addEventListener(e,a,{passive:r,capture:i}),()=>{s.removeEventListener(e,a,{capture:i})}},[e,n,r,i])}function Cr(e,t){return e===void 0?void 0:t}const em=Math.PI;function nu(e){return e*em/180}const ed=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),td=(e,t,n,r,i)=>{switch(e){case"left":return Math.floor(t)+r+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-i/2}},nd=(e,t,n)=>Math.min(e,t-n*2),rd=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,Ns=e=>{const t=e.fgColor??"currentColor";return f.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},f.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),f.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},tm=e=>{const t=e.fgColor??"currentColor";return f.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},f.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};function nm(e,t,n){const[r,i]=f.useState(e),o=f.useRef(!0);f.useEffect(()=>()=>{o.current=!1},[]);const s=f.useRef(Qc(a=>{o.current&&i(a)},n));return f.useLayoutEffect(()=>{o.current&&s.current(()=>e())},t),r}const rm="֑-߿יִ-﷽ﹰ-ﻼ",im="A-Za-zÀ-ÖØ-öø-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",om=new RegExp("^[^"+im+"]*["+rm+"]");function Bs(e){return om.test(e)?"rtl":"not-rtl"}let Ho;function Ss(){if(typeof document>"u")return 0;if(Ho!==void 0)return Ho;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Ho=n-r,Ho}const zr=Symbol();function am(e){const t=f.useRef([zr,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,r]=f.useState(e),[,i]=f.useState(),o=f.useCallback(a=>{const l=t.current[0];l!==zr&&(a=typeof a=="function"?a(l):a,a===l)||(l!==zr&&i({}),r(u=>typeof a=="function"?a(l===zr?u:l):a),t.current[0]=zr)},[]),s=f.useCallback(()=>{t.current[0]=zr,i({})},[]);return[t.current[0]===zr?n:t.current[0],o,s]}function id(e){if(e.length===0)return"";let t=0,n=0;for(const r of e){if(n+=r.length,n>1e4)break;t++}return e.slice(0,t).join(", ")}function sm(e){const t=f.useRef(e);return vi(e,t.current)||(t.current=e),t.current}const lm=e=>{const{urls:t,canWrite:n,onEditClick:r,renderImage:i}=e,o=t.filter(a=>a!=="");if(o.length===0)return null;const s=o.length>1;return f.createElement(Wg,{"data-testid":"GDG-default-image-overlay-editor"},f.createElement(Zg.Carousel,{showArrows:s,showThumbs:!1,swipeable:s,emulateTouch:s,infiniteLoop:s},o.map(a=>{const l=i?.(a)??f.createElement("img",{draggable:!1,src:a});return f.createElement("div",{className:"gdg-centering-container",key:a},l)})),n&&r&&f.createElement("button",{className:"gdg-edit-icon",onClick:r},f.createElement(Ns,null)))};function od(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Ur=od();function um(e){Ur=e}const ad=/[&<>"']/,cm=new RegExp(ad.source,"g"),sd=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,dm=new RegExp(sd.source,"g"),fm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ru=e=>fm[e];function gn(e,t){if(t){if(ad.test(e))return e.replace(cm,ru)}else if(sd.test(e))return e.replace(dm,ru);return e}const hm=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function ld(e){return e.replace(hm,(t,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const gm=/(^|[^\[])\^/g;function Lt(e,t){e=typeof e=="string"?e:e.source,t=t||"";const n={replace:(r,i)=>(i=i.source||i,i=i.replace(gm,"$1"),e=e.replace(r,i),n),getRegex:()=>new RegExp(e,t)};return n}const mm=/[^\w:]/g,pm=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function iu(e,t,n){if(e){let r;try{r=decodeURIComponent(ld(n)).replace(mm,"").toLowerCase()}catch{return null}if(r.indexOf("javascript:")===0||r.indexOf("vbscript:")===0||r.indexOf("data:")===0)return null}t&&!pm.test(n)&&(n=ym(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const zo={},vm=/^[^:]+:\/*[^/]*$/,bm=/^([^:]+:)[\s\S]*$/,wm=/^([^:]+:\/*[^/]*)[\s\S]*$/;function ym(e,t){zo[" "+e]||(vm.test(e)?zo[" "+e]=e+"/":zo[" "+e]=Ko(e,"/",!0)),e=zo[" "+e];const n=e.indexOf(":")===-1;return t.substring(0,2)==="//"?n?t:e.replace(bm,"$1")+t:t.charAt(0)==="/"?n?t:e.replace(wm,"$1")+t:e+t}const ra={exec:function(){}};function ou(e,t){const n=e.replace(/\|/g,(o,s,a)=>{let l=!1,u=s;for(;--u>=0&&a[u]==="\\";)l=!l;return l?"|":" |"}),r=n.split(/ \|/);let i=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(/\\\|/g,"|");return r}function Ko(e,t,n){const r=e.length;if(r===0)return"";let i=0;for(;i<r;){const o=e.charAt(r-i-1);if(o===t&&!n)i++;else if(o!==t&&n)i++;else break}return e.slice(0,r-i)}function Cm(e,t){if(e.indexOf(t[1])===-1)return-1;const n=e.length;let r=0,i=0;for(;i<n;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function Sm(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function au(e,t){if(t<1)return"";let n="";for(;t>1;)t&1&&(n+=e),t>>=1,e+=e;return n+e}function su(e,t,n,r){const i=t.href,o=t.title?gn(t.title):null,s=e[1].replace(/\\([\[\]])/g,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const a={type:"link",raw:n,href:i,title:o,text:s,tokens:r.inlineTokens(s)};return r.state.inLink=!1,a}return{type:"image",raw:n,href:i,title:o,text:gn(s)}}function xm(e,t){const n=e.match(/^(\s+)(?:```)/);if(n===null)return t;const r=n[1];return t.split(`
`).map(i=>{const o=i.match(/^\s+/);if(o===null)return i;const[s]=o;return s.length>=r.length?i.slice(r.length):i}).join(`
`)}class Ws{constructor(t){this.options=t||Ur}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:Ko(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],i=xm(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline._escapes,"$1"):n[2],text:i}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const i=Ko(r,"#");(this.options.pedantic||!i||/ $/.test(i))&&(r=i.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=n[0].replace(/^ *>[ \t]?/gm,""),i=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(r);return this.lexer.state.top=i,{type:"blockquote",raw:n[0],tokens:o,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r,i,o,s,a,l,u,c,d,g,h,m,p=n[1].trim();const v=p.length>1,w={type:"list",raw:"",ordered:v,start:v?+p.slice(0,-1):"",loose:!1,items:[]};p=v?`\\d{1,9}\\${p.slice(-1)}`:`\\${p}`,this.options.pedantic&&(p=v?p:"[*+-]");const b=new RegExp(`^( {0,3}${p})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;t&&(m=!1,!(!(n=b.exec(t))||this.rules.block.hr.test(t)));){if(r=n[0],t=t.substring(r.length),c=n[2].split(`
`,1)[0].replace(/^\t+/,O=>" ".repeat(3*O.length)),d=t.split(`
`,1)[0],this.options.pedantic?(s=2,h=c.trimLeft()):(s=n[2].search(/[^ ]/),s=s>4?1:s,h=c.slice(s),s+=n[1].length),l=!1,!c&&/^ *$/.test(d)&&(r+=d+`
`,t=t.substring(d.length+1),m=!0),!m){const O=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),R=new RegExp(`^ {0,${Math.min(3,s-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),M=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:\`\`\`|~~~)`),_=new RegExp(`^ {0,${Math.min(3,s-1)}}#`);for(;t&&(g=t.split(`
`,1)[0],d=g,this.options.pedantic&&(d=d.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(M.test(d)||_.test(d)||O.test(d)||R.test(t)));){if(d.search(/[^ ]/)>=s||!d.trim())h+=`
`+d.slice(s);else{if(l||c.search(/[^ ]/)>=4||M.test(c)||_.test(c)||R.test(c))break;h+=`
`+d}!l&&!d.trim()&&(l=!0),r+=g+`
`,t=t.substring(g.length+1),c=d.slice(s)}}w.loose||(u?w.loose=!0:/\n *\n *$/.test(r)&&(u=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(h),i&&(o=i[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),w.items.push({type:"list_item",raw:r,task:!!i,checked:o,loose:!1,text:h}),w.raw+=r}w.items[w.items.length-1].raw=r.trimRight(),w.items[w.items.length-1].text=h.trimRight(),w.raw=w.raw.trimRight();const x=w.items.length;for(a=0;a<x;a++)if(this.lexer.state.top=!1,w.items[a].tokens=this.lexer.blockTokens(w.items[a].text,[]),!w.loose){const O=w.items[a].tokens.filter(M=>M.type==="space"),R=O.length>0&&O.some(M=>/\n.*\n/.test(M.raw));w.loose=R}if(w.loose)for(a=0;a<x;a++)w.items[a].loose=!0;return w}}html(t){const n=this.rules.block.html.exec(t);if(n){const r={type:"html",raw:n[0],pre:!this.options.sanitizer&&(n[1]==="pre"||n[1]==="script"||n[1]==="style"),text:n[0]};if(this.options.sanitize){const i=this.options.sanitizer?this.options.sanitizer(n[0]):gn(n[0]);r.type="paragraph",r.text=i,r.tokens=this.lexer.inline(i)}return r}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),i=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",o=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline._escapes,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:i,title:o}}}table(t){const n=this.rules.block.table.exec(t);if(n){const r={type:"table",header:ou(n[1]).map(i=>({text:i})),align:n[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(r.header.length===r.align.length){r.raw=n[0];let i=r.align.length,o,s,a,l;for(o=0;o<i;o++)/^ *-+: *$/.test(r.align[o])?r.align[o]="right":/^ *:-+: *$/.test(r.align[o])?r.align[o]="center":/^ *:-+ *$/.test(r.align[o])?r.align[o]="left":r.align[o]=null;for(i=r.rows.length,o=0;o<i;o++)r.rows[o]=ou(r.rows[o],r.header.length).map(u=>({text:u}));for(i=r.header.length,s=0;s<i;s++)r.header[s].tokens=this.lexer.inline(r.header[s].text);for(i=r.rows.length,s=0;s<i;s++)for(l=r.rows[s],a=0;a<l.length;a++)l[a].tokens=this.lexer.inline(l[a].text);return r}}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:gn(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):gn(n[0]):n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const s=Ko(r.slice(0,-1),"\\");if((r.length-s.length)%2===0)return}else{const s=Cm(n[2],"()");if(s>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+s;n[2]=n[2].substring(0,s),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let i=n[2],o="";if(this.options.pedantic){const s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);s&&(i=s[1],o=s[3])}else o=n[3]?n[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(this.options.pedantic&&!/>$/.test(r)?i=i.slice(1):i=i.slice(1,-1)),su(n,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:o&&o.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){let i=(r[2]||r[1]).replace(/\s+/g," ");if(i=n[i.toLowerCase()],!i){const o=r[0].charAt(0);return{type:"text",raw:o,text:o}}return su(r,i,r[0],this.lexer)}}emStrong(t,n,r=""){let i=this.rules.inline.emStrong.lDelim.exec(t);if(!i||i[3]&&r.match(/[\p{L}\p{N}]/u))return;const o=i[1]||i[2]||"";if(!o||o&&(r===""||this.rules.inline.punctuation.exec(r))){const s=i[0].length-1;let a,l,u=s,c=0;const d=i[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(d.lastIndex=0,n=n.slice(-1*t.length+s);(i=d.exec(n))!=null;){if(a=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!a)continue;if(l=a.length,i[3]||i[4]){u+=l;continue}else if((i[5]||i[6])&&s%3&&!((s+l)%3)){c+=l;continue}if(u-=l,u>0)continue;l=Math.min(l,l+u+c);const g=t.slice(0,s+i.index+(i[0].length-a.length)+l);if(Math.min(s,l)%2){const m=g.slice(1,-1);return{type:"em",raw:g,text:m,tokens:this.lexer.inlineTokens(m)}}const h=g.slice(2,-2);return{type:"strong",raw:g,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const i=/[^ ]/.test(r),o=/^ /.test(r)&&/ $/.test(r);return i&&o&&(r=r.substring(1,r.length-1)),r=gn(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t,n){const r=this.rules.inline.autolink.exec(t);if(r){let i,o;return r[2]==="@"?(i=gn(this.options.mangle?n(r[1]):r[1]),o="mailto:"+i):(i=gn(r[1]),o=i),{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}url(t,n){let r;if(r=this.rules.inline.url.exec(t)){let i,o;if(r[2]==="@")i=gn(this.options.mangle?n(r[0]):r[0]),o="mailto:"+i;else{let s;do s=r[0],r[0]=this.rules.inline._backpedal.exec(r[0])[0];while(s!==r[0]);i=gn(r[0]),r[1]==="www."?o="http://"+r[0]:o=r[0]}return{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t,n){const r=this.rules.inline.text.exec(t);if(r){let i;return this.lexer.state.inRawBlock?i=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):gn(r[0]):r[0]:i=gn(this.options.smartypants?n(r[0]):r[0]),{type:"text",raw:r[0],text:i}}}}const ze={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:ra,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};ze._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;ze._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;ze.def=Lt(ze.def).replace("label",ze._label).replace("title",ze._title).getRegex();ze.bullet=/(?:[*+-]|\d{1,9}[.)])/;ze.listItemStart=Lt(/^( *)(bull) */).replace("bull",ze.bullet).getRegex();ze.list=Lt(ze.list).replace(/bull/g,ze.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+ze.def.source+")").getRegex();ze._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";ze._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;ze.html=Lt(ze.html,"i").replace("comment",ze._comment).replace("tag",ze._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();ze.paragraph=Lt(ze._paragraph).replace("hr",ze.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ze._tag).getRegex();ze.blockquote=Lt(ze.blockquote).replace("paragraph",ze.paragraph).getRegex();ze.normal={...ze};ze.gfm={...ze.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};ze.gfm.table=Lt(ze.gfm.table).replace("hr",ze.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ze._tag).getRegex();ze.gfm.paragraph=Lt(ze._paragraph).replace("hr",ze.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",ze.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ze._tag).getRegex();ze.pedantic={...ze.normal,html:Lt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ze._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ra,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Lt(ze.normal._paragraph).replace("hr",ze.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",ze.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const ye={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:ra,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:ra,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};ye._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";ye.punctuation=Lt(ye.punctuation).replace(/punctuation/g,ye._punctuation).getRegex();ye.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;ye.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;ye._comment=Lt(ze._comment).replace("(?:-->|$)","-->").getRegex();ye.emStrong.lDelim=Lt(ye.emStrong.lDelim).replace(/punct/g,ye._punctuation).getRegex();ye.emStrong.rDelimAst=Lt(ye.emStrong.rDelimAst,"g").replace(/punct/g,ye._punctuation).getRegex();ye.emStrong.rDelimUnd=Lt(ye.emStrong.rDelimUnd,"g").replace(/punct/g,ye._punctuation).getRegex();ye._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;ye._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;ye._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;ye.autolink=Lt(ye.autolink).replace("scheme",ye._scheme).replace("email",ye._email).getRegex();ye._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;ye.tag=Lt(ye.tag).replace("comment",ye._comment).replace("attribute",ye._attribute).getRegex();ye._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;ye._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;ye._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;ye.link=Lt(ye.link).replace("label",ye._label).replace("href",ye._href).replace("title",ye._title).getRegex();ye.reflink=Lt(ye.reflink).replace("label",ye._label).replace("ref",ze._label).getRegex();ye.nolink=Lt(ye.nolink).replace("ref",ze._label).getRegex();ye.reflinkSearch=Lt(ye.reflinkSearch,"g").replace("reflink",ye.reflink).replace("nolink",ye.nolink).getRegex();ye.normal={...ye};ye.pedantic={...ye.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:Lt(/^!?\[(label)\]\((.*?)\)/).replace("label",ye._label).getRegex(),reflink:Lt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ye._label).getRegex()};ye.gfm={...ye.normal,escape:Lt(ye.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};ye.gfm.url=Lt(ye.gfm.url,"i").replace("email",ye.gfm._extended_email).getRegex();ye.breaks={...ye.gfm,br:Lt(ye.br).replace("{2,}","*").getRegex(),text:Lt(ye.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function km(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function lu(e){let t="",n,r;const i=e.length;for(n=0;n<i;n++)r=e.charCodeAt(n),Math.random()>.5&&(r="x"+r.toString(16)),t+="&#"+r+";";return t}class Mr{constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Ur,this.options.tokenizer=this.options.tokenizer||new Ws,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:ze.normal,inline:ye.normal};this.options.pedantic?(n.block=ze.pedantic,n.inline=ye.pedantic):this.options.gfm&&(n.block=ze.gfm,this.options.breaks?n.inline=ye.breaks:n.inline=ye.gfm),this.tokenizer.rules=n}static get rules(){return{block:ze,inline:ye}}static lex(t,n){return new Mr(n).lex(t)}static lexInline(t,n){return new Mr(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);let n;for(;n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(t,n=[]){this.options.pedantic?t=t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t=t.replace(/^( *)(\t+)/gm,(a,l,u)=>l+"    ".repeat(u.length));let r,i,o,s;for(;t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>(r=a.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.space(t)){t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(r=this.tokenizer.fences(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let u;this.options.extensions.startBlock.forEach(function(c){u=c.call({lexer:this},l),typeof u=="number"&&u>=0&&(a=Math.min(a,u))}),a<1/0&&a>=0&&(o=t.substring(0,a+1))}if(this.state.top&&(r=this.tokenizer.paragraph(o))){i=n[n.length-1],s&&i.type==="paragraph"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r),s=o.length!==t.length,t=t.substring(r.raw.length);continue}if(r=this.tokenizer.text(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&i.type==="text"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,i,o,s=t,a,l,u;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(s))!=null;)c.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,a.index)+"["+au("a",a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(s))!=null;)s=s.slice(0,a.index)+"["+au("a",a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.escapedEmSt.exec(s))!=null;)s=s.slice(0,a.index+a[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;t;)if(l||(u=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(r=c.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.escape(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.tag(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.link(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.emStrong(t,s,u)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.codespan(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.br(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.del(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.autolink(t,lu)){t=t.substring(r.raw.length),n.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(t,lu))){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=t.slice(1);let g;this.options.extensions.startInline.forEach(function(h){g=h.call({lexer:this},d),typeof g=="number"&&g>=0&&(c=Math.min(c,g))}),c<1/0&&c>=0&&(o=t.substring(0,c+1))}if(r=this.tokenizer.inlineText(o,km)){t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(u=r.raw.slice(-1)),l=!0,i=n[n.length-1],i&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return n}}class Us{constructor(t){this.options=t||Ur}code(t,n,r){const i=(n||"").match(/\S*/)[0];if(this.options.highlight){const o=this.options.highlight(t,i);o!=null&&o!==t&&(r=!0,t=o)}return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="'+this.options.langPrefix+gn(i)+'">'+(r?t:gn(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:gn(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t){return t}heading(t,n,r,i){if(this.options.headerIds){const o=this.options.headerPrefix+i.slug(r);return`<h${n} id="${o}">${t}</h${n}>
`}return`<h${n}>${t}</h${n}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(t,n,r){const i=n?"ol":"ul",o=n&&r!==1?' start="'+r+'"':"";return"<"+i+o+`>
`+t+"</"+i+`>
`}listitem(t){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){if(t=iu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i='<a href="'+t+'"';return n&&(i+=' title="'+n+'"'),i+=">"+r+"</a>",i}image(t,n,r){if(t=iu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${n}"`),i+=this.options.xhtml?"/>":">",i}text(t){return t}}class ud{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class cd{constructor(){this.seen={}}serialize(t){return t.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(t,n){let r=t,i=0;if(this.seen.hasOwnProperty(r)){i=this.seen[t];do i++,r=t+"-"+i;while(this.seen.hasOwnProperty(r))}return n||(this.seen[t]=i,this.seen[r]=0),r}slug(t,n={}){const r=this.serialize(t);return this.getNextSafeSlug(r,n.dryrun)}}class Rr{constructor(t){this.options=t||Ur,this.options.renderer=this.options.renderer||new Us,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ud,this.slugger=new cd}static parse(t,n){return new Rr(n).parse(t)}static parseInline(t,n){return new Rr(n).parseInline(t)}parse(t,n=!0){let r="",i,o,s,a,l,u,c,d,g,h,m,p,v,w,b,x,O,R,M;const _=t.length;for(i=0;i<_;i++){if(h=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[h.type]&&(M=this.options.extensions.renderers[h.type].call({parser:this},h),M!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(h.type))){r+=M||"";continue}switch(h.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{r+=this.renderer.heading(this.parseInline(h.tokens),h.depth,ld(this.parseInline(h.tokens,this.textRenderer)),this.slugger);continue}case"code":{r+=this.renderer.code(h.text,h.lang,h.escaped);continue}case"table":{for(d="",c="",a=h.header.length,o=0;o<a;o++)c+=this.renderer.tablecell(this.parseInline(h.header[o].tokens),{header:!0,align:h.align[o]});for(d+=this.renderer.tablerow(c),g="",a=h.rows.length,o=0;o<a;o++){for(u=h.rows[o],c="",l=u.length,s=0;s<l;s++)c+=this.renderer.tablecell(this.parseInline(u[s].tokens),{header:!1,align:h.align[s]});g+=this.renderer.tablerow(c)}r+=this.renderer.table(d,g);continue}case"blockquote":{g=this.parse(h.tokens),r+=this.renderer.blockquote(g);continue}case"list":{for(m=h.ordered,p=h.start,v=h.loose,a=h.items.length,g="",o=0;o<a;o++)b=h.items[o],x=b.checked,O=b.task,w="",b.task&&(R=this.renderer.checkbox(x),v?b.tokens.length>0&&b.tokens[0].type==="paragraph"?(b.tokens[0].text=R+" "+b.tokens[0].text,b.tokens[0].tokens&&b.tokens[0].tokens.length>0&&b.tokens[0].tokens[0].type==="text"&&(b.tokens[0].tokens[0].text=R+" "+b.tokens[0].tokens[0].text)):b.tokens.unshift({type:"text",text:R}):w+=R),w+=this.parse(b.tokens,v),g+=this.renderer.listitem(w,O,x);r+=this.renderer.list(g,m,p);continue}case"html":{r+=this.renderer.html(h.text);continue}case"paragraph":{r+=this.renderer.paragraph(this.parseInline(h.tokens));continue}case"text":{for(g=h.tokens?this.parseInline(h.tokens):h.text;i+1<_&&t[i+1].type==="text";)h=t[++i],g+=`
`+(h.tokens?this.parseInline(h.tokens):h.text);r+=n?this.renderer.paragraph(g):g;continue}default:{const I='Token with "'+h.type+'" type was not found.';if(this.options.silent){console.error(I);return}else throw new Error(I)}}}return r}parseInline(t,n){n=n||this.renderer;let r="",i,o,s;const a=t.length;for(i=0;i<a;i++){if(o=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]&&(s=this.options.extensions.renderers[o.type].call({parser:this},o),s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type))){r+=s||"";continue}switch(o.type){case"escape":{r+=n.text(o.text);break}case"html":{r+=n.html(o.text);break}case"link":{r+=n.link(o.href,o.title,this.parseInline(o.tokens,n));break}case"image":{r+=n.image(o.href,o.title,o.text);break}case"strong":{r+=n.strong(this.parseInline(o.tokens,n));break}case"em":{r+=n.em(this.parseInline(o.tokens,n));break}case"codespan":{r+=n.codespan(o.text);break}case"br":{r+=n.br();break}case"del":{r+=n.del(this.parseInline(o.tokens,n));break}case"text":{r+=n.text(o.text);break}default:{const l='Token with "'+o.type+'" type was not found.';if(this.options.silent){console.error(l);return}else throw new Error(l)}}}return r}}class xs{constructor(t){this.options=t||Ur}static passThroughHooks=new Set(["preprocess","postprocess"]);preprocess(t){return t}postprocess(t){return t}}function Mm(e,t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+gn(r.message+"",!0)+"</pre>";if(t)return Promise.resolve(i);if(n){n(null,i);return}return i}if(t)return Promise.reject(r);if(n){n(r);return}throw r}}function dd(e,t){return(n,r,i)=>{typeof r=="function"&&(i=r,r=null);const o={...r};r={...Be.defaults,...o};const s=Mm(r.silent,r.async,i);if(typeof n>"u"||n===null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(Sm(r),r.hooks&&(r.hooks.options=r),i){const a=r.highlight;let l;try{r.hooks&&(n=r.hooks.preprocess(n)),l=e(n,r)}catch(d){return s(d)}const u=function(d){let g;if(!d)try{r.walkTokens&&Be.walkTokens(l,r.walkTokens),g=t(l,r),r.hooks&&(g=r.hooks.postprocess(g))}catch(h){d=h}return r.highlight=a,d?s(d):i(null,g)};if(!a||a.length<3||(delete r.highlight,!l.length))return u();let c=0;Be.walkTokens(l,function(d){d.type==="code"&&(c++,setTimeout(()=>{a(d.text,d.lang,function(g,h){if(g)return u(g);h!=null&&h!==d.text&&(d.text=h,d.escaped=!0),c--,c===0&&u()})},0))}),c===0&&u();return}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then(a=>e(a,r)).then(a=>r.walkTokens?Promise.all(Be.walkTokens(a,r.walkTokens)).then(()=>a):a).then(a=>t(a,r)).then(a=>r.hooks?r.hooks.postprocess(a):a).catch(s);try{r.hooks&&(n=r.hooks.preprocess(n));const a=e(n,r);r.walkTokens&&Be.walkTokens(a,r.walkTokens);let l=t(a,r);return r.hooks&&(l=r.hooks.postprocess(l)),l}catch(a){return s(a)}}}function Be(e,t,n){return dd(Mr.lex,Rr.parse)(e,t,n)}Be.options=Be.setOptions=function(e){return Be.defaults={...Be.defaults,...e},um(Be.defaults),Be};Be.getDefaults=od;Be.defaults=Ur;Be.use=function(...e){const t=Be.defaults.extensions||{renderers:{},childTokens:{}};e.forEach(n=>{const r={...n};if(r.async=Be.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if(i.renderer){const o=t.renderers[i.name];o?t.renderers[i.name]=function(...s){let a=i.renderer.apply(this,s);return a===!1&&(a=o.apply(this,s)),a}:t.renderers[i.name]=i.renderer}if(i.tokenizer){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");t[i.level]?t[i.level].unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){const i=Be.defaults.renderer||new Us;for(const o in n.renderer){const s=i[o];i[o]=(...a)=>{let l=n.renderer[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.renderer=i}if(n.tokenizer){const i=Be.defaults.tokenizer||new Ws;for(const o in n.tokenizer){const s=i[o];i[o]=(...a)=>{let l=n.tokenizer[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.tokenizer=i}if(n.hooks){const i=Be.defaults.hooks||new xs;for(const o in n.hooks){const s=i[o];xs.passThroughHooks.has(o)?i[o]=a=>{if(Be.defaults.async)return Promise.resolve(n.hooks[o].call(i,a)).then(u=>s.call(i,u));const l=n.hooks[o].call(i,a);return s.call(i,l)}:i[o]=(...a)=>{let l=n.hooks[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.hooks=i}if(n.walkTokens){const i=Be.defaults.walkTokens;r.walkTokens=function(o){let s=[];return s.push(n.walkTokens.call(this,o)),i&&(s=s.concat(i.call(this,o))),s}}Be.setOptions(r)})};Be.walkTokens=function(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(Be,r)),r.type){case"table":{for(const i of r.header)n=n.concat(Be.walkTokens(i.tokens,t));for(const i of r.rows)for(const o of i)n=n.concat(Be.walkTokens(o.tokens,t));break}case"list":{n=n.concat(Be.walkTokens(r.items,t));break}default:Be.defaults.extensions&&Be.defaults.extensions.childTokens&&Be.defaults.extensions.childTokens[r.type]?Be.defaults.extensions.childTokens[r.type].forEach(function(i){n=n.concat(Be.walkTokens(r[i],t))}):r.tokens&&(n=n.concat(Be.walkTokens(r.tokens,t)))}return n};Be.parseInline=dd(Mr.lexInline,Rr.parseInline);Be.Parser=Rr;Be.parser=Rr.parse;Be.Renderer=Us;Be.TextRenderer=ud;Be.Lexer=Mr;Be.lexer=Mr.lex;Be.Tokenizer=Ws;Be.Slugger=cd;Be.Hooks=xs;Be.parse=Be;Be.options;Be.setOptions;Be.use;Be.walkTokens;Be.parseInline;Rr.parse;Mr.lex;const Rm=un("div")({name:"MarkdownContainer",class:"gdg-mnuv029",propsAsIs:!1});class Im extends Vt.PureComponent{targetElement=null;renderMarkdownIntoDiv(){const{targetElement:t,props:n}=this;if(t===null)return;const{contents:r,createNode:i}=n,o=Be(r),s=document.createRange();s.selectNodeContents(t),s.deleteContents();let a=i?.(o);if(a===void 0){const u=document.createElement("template");u.innerHTML=o,a=u.content}t.append(a);const l=t.getElementsByTagName("a");for(const u of l)u.target="_blank",u.rel="noreferrer noopener"}containerRefHook=t=>{this.targetElement=t,this.renderMarkdownIntoDiv()};render(){return this.renderMarkdownIntoDiv(),Vt.createElement(Rm,{ref:this.containerRefHook})}}const Em=un("textarea")({name:"InputBox",class:"gdg-izpuzkl",propsAsIs:!1}),Tm=un("div")({name:"ShadowBox",class:"gdg-s69h75o",propsAsIs:!1}),Dm=un("div")({name:"GrowingEntryStyle",class:"gdg-g1y0xocz",propsAsIs:!1});let uu=0;const qr=e=>{const{placeholder:t,value:n,onKeyDown:r,highlight:i,altNewline:o,validatedSelection:s,...a}=e,{onChange:l,className:u}=a,c=f.useRef(null),d=n??"";Dn(l!==void 0,"GrowingEntry must be a controlled input area");const[g]=f.useState(()=>"input-box-"+(uu=(uu+1)%1e7));f.useEffect(()=>{const m=c.current;if(m===null||m.disabled)return;const p=d.toString().length;m.focus(),m.setSelectionRange(i?0:p,p)},[]),f.useLayoutEffect(()=>{if(s!==void 0){const m=typeof s=="number"?[s,null]:s;c.current?.setSelectionRange(m[0],m[1])}},[s]);const h=f.useCallback(m=>{m.key==="Enter"&&m.shiftKey&&o===!0||r?.(m)},[o,r]);return f.createElement(Dm,{className:"gdg-growing-entry"},f.createElement(Tm,{className:u},d+`
`),f.createElement(Em,{...a,className:(u??"")+" gdg-input",id:g,ref:c,onKeyDown:h,value:d,placeholder:t,dir:"auto"}))},Ja={};let wr=null;function Om(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}function ia(e){const t=e.toLowerCase().trim();if(Ja[t]!==void 0)return Ja[t];wr=wr||Om(),wr.style.color="#000",wr.style.color=t;const n=getComputedStyle(wr).color;wr.style.color="#fff",wr.style.color=t;const r=getComputedStyle(wr).color;if(r!==n)return[0,0,0,1];let i=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return i.length<4&&i.push(1),i=i.map(o=>Number.isNaN(o)?0:o),Ja[t]=i,i}function Br(e,t){const[n,r,i]=ia(e);return`rgba(${n}, ${r}, ${i}, ${t})`}const cu=new Map;function du(e,t){const n=`${e}-${t}`,r=cu.get(n);if(r!==void 0)return r;const i=On(e,t);return cu.set(n,i),i}function On(e,t){if(t===void 0)return e;const[n,r,i,o]=ia(e);if(o===1)return e;const[s,a,l,u]=ia(t),c=o+u*(1-o),d=(o*n+u*s*(1-o))/c,g=(o*r+u*a*(1-o))/c,h=(o*i+u*l*(1-o))/c;return`rgba(${d}, ${g}, ${h}, ${c})`}var fi=new Map,gi=new Map,ks=new Map;function Pm(){fi.clear(),ks.clear(),gi.clear()}function _m(e,t,n,r,i){var o,s,a;let l=0,u={};for(let d of e)l+=(o=n.get(d))!=null?o:i,u[d]=((s=u[d])!=null?s:0)+1;let c=t-l;for(let d of Object.keys(u)){let g=u[d],h=(a=n.get(d))!=null?a:i,m=h*g/l,p=c*m*r/g,v=h+p;n.set(d,v)}}function Fm(e,t){var n;let r=new Map,i=0;for(let u of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let c=e.measureText(u).width;r.set(u,c),i+=c}let o=i/r.size,s=3,a=(t/o+s)/(s+1),l=r.keys();for(let u of l)r.set(u,((n=r.get(u))!=null?n:o)*a);return r}function Zi(e,t,n,r){var i,o;let s=gi.get(n);if(r&&s!==void 0&&s.count>2e4){let u=ks.get(n);if(u===void 0&&(u=Fm(e,s.size),ks.set(n,u)),s.count>5e5){let d=0;for(let g of t)d+=(i=u.get(g))!=null?i:s.size;return d*1.01}let c=e.measureText(t);return _m(t,c.width,u,Math.max(.05,1-s.count/2e5),s.size),gi.set(n,{count:s.count+t.length,size:s.size}),c.width}let a=e.measureText(t),l=a.width/t.length;if(((o=s?.count)!=null?o:0)>2e4)return a.width;if(s===void 0)gi.set(n,{count:t.length,size:l});else{let u=l-s.size,c=t.length/(s.count+t.length),d=s.size+u*c;gi.set(n,{count:s.count+t.length,size:d})}return a.width}function Lm(e,t,n,r,i,o,s,a){if(t.length<=1)return t.length;if(i<n)return-1;let l=Math.floor(n/i*o),u=Zi(e,t.slice(0,Math.max(0,l)),r,s);if(u!==n)if(u<n){for(;u<n;)l++,u=Zi(e,t.slice(0,Math.max(0,l)),r,s);l--}else for(;u>n;){let c=t.lastIndexOf(" ",l-1);c>0?l=c:l--,u=Zi(e,t.slice(0,Math.max(0,l)),r,s)}if(t[l]!==" "){let c=0;c=t.lastIndexOf(" ",l),c>0&&(l=c)}return l}function Am(e,t,n,r,i,o){let s=`${t}_${n}_${r}px`,a=fi.get(s);if(a!==void 0)return a;if(r<=0)return[];let l=[],u=t.split(`
`),c=gi.get(n),d=c===void 0?t.length:r/c.size*1.5,g=i&&c!==void 0&&c.count>2e4;for(let h of u){let m=Zi(e,h.slice(0,Math.max(0,d)),n,g),p=Math.min(h.length,d);if(m<=r)l.push(h);else{for(;m>r;){let v=Lm(e,h,r,n,m,p,g),w=h.slice(0,Math.max(0,v));h=h.slice(w.length),l.push(w),m=Zi(e,h.slice(0,Math.max(0,d)),n,g),p=Math.min(h.length,d)}m>0&&l.push(h)}}return l=l.map((h,m)=>m===0?h.trimEnd():h.trim()),fi.set(s,l),fi.size>500&&fi.delete(fi.keys().next().value),l}function Hm(e,t){return Vt.useMemo(()=>e.map((n,r)=>({group:n.group,grow:n.grow,hasMenu:n.hasMenu,icon:n.icon,id:n.id,menuIcon:n.menuIcon,overlayIcon:n.overlayIcon,sourceIndex:r,sticky:r<t,indicatorIcon:n.indicatorIcon,style:n.style,themeOverride:n.themeOverride,title:n.title,trailingRowOptions:n.trailingRowOptions,width:n.width,growOffset:n.growOffset,rowMarker:n.rowMarker,rowMarkerChecked:n.rowMarkerChecked,headerRowMarkerTheme:n.headerRowMarkerTheme,headerRowMarkerAlwaysVisible:n.headerRowMarkerAlwaysVisible,headerRowMarkerDisabled:n.headerRowMarkerDisabled})),[e,t])}function zm(e,t){const[n,r]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(r))return!0;if(e.current!==void 0){if(Ji(e.current.cell,t))return!0;const i=[e.current.range,...e.current.rangeStack];for(const o of i)if(n>=o.x&&n<o.x+o.width&&r>=o.y&&r<o.y+o.height)return!0}return!1}function ro(e,t){return(e??"")===(t??"")}function Vm(e,t,n){return n.current===void 0||e[1]!==n.current.cell[1]?!1:t.span===void 0?n.current.cell[0]===e[0]:n.current.cell[0]>=t.span[0]&&n.current.cell[0]<=t.span[1]}function fd(e,t){const[n,r]=e;return n>=t.x&&n<t.x+t.width&&r>=t.y&&r<t.y+t.height}function Ji(e,t){return e?.[0]===t?.[0]&&e?.[1]===t?.[1]}function hd(e){return[e.x+e.width-1,e.y+e.height-1]}function fu(e,t,n){const r=n.x,i=n.x+n.width-1,o=n.y,s=n.y+n.height-1,[a,l]=e;if(l<o||l>s)return!1;if(t.span===void 0)return a>=r&&a<=i;const[u,c]=t.span;return u>=r&&u<=i||c>=r&&u<=i||u<r&&c>i}function $m(e,t,n,r){let i=0;if(n.current===void 0)return i;const o=n.current.range;(r||o.height*o.width>1)&&fu(e,t,o)&&i++;for(const s of n.current.rangeStack)fu(e,t,s)&&i++;return i}function gd(e,t){let n=e;if(t!==void 0){let r=[...e];const i=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,i)):(r.splice(t.dest+1,0,i),r.splice(t.src,1)),r=r.map((o,s)=>({...o,sticky:e[s].sticky})),n=r}return n}function bi(e,t){let n=0;const r=gd(e,t);for(let i=0;i<r.length;i++){const o=r[i];if(o.sticky)n+=o.width;else break}return n}function Gr(e,t,n){if(typeof n=="number")return t*n;{let r=0;for(let i=e-t;i<e;i++)r+=n(i);return r}}function Ms(e,t,n,r,i){const o=gd(e,r),s=[];for(const u of o)if(u.sticky)s.push(u);else break;if(s.length>0)for(const u of s)n-=u.width;let a=t,l=i??0;for(;l<=n&&a<o.length;)l+=o[a].width,a++;for(let u=t;u<a;u++){const c=o[u];c.sticky||s.push(c)}return s}function Nm(e,t,n){let r=0;for(const i of t){const o=i.sticky?r:r+(n??0);if(e<=o+i.width)return i.sourceIndex;r+=i.width}return-1}function Bm(e,t,n,r,i,o,s,a,l,u){const c=r+i;if(n&&e<=i)return-2;if(e<=c)return-1;let d=t;for(let m=0;m<u;m++){const p=o-1-m,v=typeof s=="number"?s:s(p);if(d-=v,e>=d)return p}const g=o-u,h=e-(l??0);if(typeof s=="number"){const m=Math.floor((h-c)/s)+a;return m>=g?void 0:m}else{let m=c;for(let p=a;p<g;p++){const v=s(p);if(h<=m+v)return p;m+=v}return}}let Zo=0,Qi={};const Wm=typeof window>"u";async function Um(){Wm||document?.fonts?.ready===void 0||(await document.fonts.ready,Zo=0,Qi={},Pm())}Um();function md(e,t,n,r){return`${e}_${r??t?.font}_${n}`}function Xr(e,t,n,r="middle"){const i=md(e,t,r,n);let o=Qi[i];return o===void 0&&(o=t.measureText(e),Qi[i]=o,Zo++),Zo>1e4&&(Qi={},Zo=0),o}function pd(e,t){const n=md(e,void 0,"middle",t);return Qi[n]}function Qn(e,t){return typeof t!="string"&&(t=t.baseFontFull),qm(e,t)}function hu(e,t){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ";e.save(),e.textBaseline=t;const r=e.measureText(n);return e.restore(),r}const gu=[];function qm(e,t){for(const o of gu)if(o.key===t)return o.val;const n=hu(e,"alphabetic"),i=-(hu(e,"middle").actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return gu.push({key:t,val:i}),i}function Gm(e,t,n,r,i,o){const{ctx:s,rect:a,theme:l}=e;let u=Number.MAX_SAFE_INTEGER;const c=500;if(t!==void 0&&(u=n-t,u<c)){const d=1-u/c;s.globalAlpha=d,s.fillStyle=l.bgSearchResult,s.fillRect(a.x+1,a.y+1,a.width-(i?2:1),a.height-(o?2:1)),s.globalAlpha=1,r!==void 0&&(r.fillStyle=l.bgSearchResult)}return u<c}function co(e,t,n){const{ctx:r,theme:i}=e,o=t??{},s=n??i.textDark;return s!==o.fillStyle&&(r.fillStyle=s,o.fillStyle=s),o}function qs(e,t,n,r,i){const{rect:o,ctx:s,theme:a}=e;s.fillStyle=a.textDark,Zn({ctx:s,rect:o,theme:a},t,n,r,i)}function vd(e,t,n,r,i,o,s,a,l){l==="right"?e.fillText(t,n+i-(a.cellHorizontalPadding+.5),r+o/2+s):l==="center"?e.fillText(t,n+i/2,r+o/2+s):e.fillText(t,n+a.cellHorizontalPadding+.5,r+o/2+s)}function Gs(e,t){const n=Xr("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function Xm(e,t){e.includes(`
`)&&(e=e.split(/\r?\n/,1)[0]);const n=t/4;return e.length>n&&(e=e.slice(0,n)),e}function Ym(e,t,n,r,i,o,s,a,l,u){const c=a.baseFontFull,d=Am(e,t,c,i-a.cellHorizontalPadding*2,u??!1),g=Gs(e,c),h=a.lineHeight*g,m=g+h*(d.length-1),p=m+a.cellVerticalPadding>o;p&&(e.save(),e.rect(n,r,i,o),e.clip());const v=r+o/2-m/2;let w=Math.max(r+a.cellVerticalPadding,v);for(const b of d)if(vd(e,b,n,w,i,g,s,a,l),w+=h,w>r+o)break;p&&e.restore()}function Zn(e,t,n,r,i){const{ctx:o,rect:s,theme:a}=e,{x:l,y:u,width:c,height:d}=s;r=r??!1,r||(t=Xm(t,c));const g=Qn(o,a),h=Bs(t)==="rtl";if(n===void 0&&h&&(n="right"),h&&(o.direction="rtl"),t.length>0){let m=!1;n==="right"?(o.textAlign="right",m=!0):n!==void 0&&n!=="left"&&(o.textAlign=n,m=!0),r?Ym(o,t,l,u,c,d,g,a,n,i):vd(o,t,l,u,c,d,g,a,n),m&&(o.textAlign="start"),h&&(o.direction="inherit")}}function Jn(e,t,n,r,i,o){typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.max(0,Math.min(o.tl,i/2,r/2)),tr:Math.max(0,Math.min(o.tr,i/2,r/2)),bl:Math.max(0,Math.min(o.bl,i/2,r/2)),br:Math.max(0,Math.min(o.br,i/2,r/2))},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}function jm(e,t,n){e.arc(t,n-1.25*3.5,1.25,0,2*Math.PI,!1),e.arc(t,n,1.25,0,2*Math.PI,!1),e.arc(t,n******3.5,1.25,0,2*Math.PI,!1)}function Km(e,t,n){const r=function(a,l){const u=l.x-a.x,c=l.y-a.y,d=Math.sqrt(u*u+c*c),g=u/d,h=c/d;return{x:u,y:l.y-a.y,len:d,nx:g,ny:h,ang:Math.atan2(h,g)}};let i;const o=t.length;let s=t[o-1];for(let a=0;a<o;a++){let l=t[a%o];const u=t[(a+1)%o],c=r(l,s),d=r(l,u),g=c.nx*d.ny-c.ny*d.nx,h=c.nx*d.nx-c.ny*-d.ny;let m=Math.asin(g<-1?-1:g>1?1:g),p=1,v=!1;h<0?m<0?m=Math.PI+m:(m=Math.PI-m,p=-1,v=!0):m>0&&(p=-1,v=!0),i=l.radius!==void 0?l.radius:n;const w=m/2;let b=Math.abs(Math.cos(w)*i/Math.sin(w)),x;b>Math.min(c.len/2,d.len/2)?(b=Math.min(c.len/2,d.len/2),x=Math.abs(b*Math.sin(w)/Math.cos(w))):x=i;let O=l.x+d.nx*b,R=l.y+d.ny*b;O+=-d.ny*x*p,R+=d.nx*x*p,e.arc(O,R,x,c.ang+Math.PI/2*p,d.ang-Math.PI/2*p,v),s=l,l=u}e.closePath()}function Rs(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m){const p={x:0,y:o+u,width:0,height:0};if(e>=h.length||t>=c||t<-2||e<0)return p;const v=o-i;if(e>=d){const w=s>e?-1:1,b=bi(h);p.x+=b+l;for(let x=s;x!==e;x+=w)p.x+=h[w===1?x:x-1].width*w}else for(let w=0;w<e;w++)p.x+=h[w].width;if(p.width=h[e].width+1,t===-1)p.y=i,p.height=v;else if(t===-2){p.y=0,p.height=i;let w=e;const b=h[e].group,x=h[e].sticky;for(;w>0&&ro(h[w-1].group,b)&&h[w-1].sticky===x;){const R=h[w-1];p.x-=R.width,p.width+=R.width,w--}let O=e;for(;O+1<h.length&&ro(h[O+1].group,b)&&h[O+1].sticky===x;){const R=h[O+1];p.width+=R.width,O++}if(!x){const R=bi(h),M=p.x-R;M<0&&(p.x-=M,p.width+=M),p.x+p.width>n&&(p.width=n-p.x)}}else if(t>=c-g){let w=c-t;for(p.y=r;w>0;){const b=t+w-1;p.height=typeof m=="number"?m:m(b),p.y-=p.height,w--}p.height+=1}else{const w=a>t?-1:1;if(typeof m=="number"){const b=t-a;p.y+=b*m}else for(let b=a;b!==t;b+=w)p.y+=m(b)*w;p.height=(typeof m=="number"?m:m(t))+1}return p}const Xs=1<<21;function Wn(e,t){return(t+2)*Xs+e}function bd(e){return e%Xs}function Ys(e){return Math.floor(e/Xs)-2}function js(e){const t=bd(e),n=Ys(e);return[t,n]}class wd{visibleWindow={x:0,y:0,width:0,height:0};freezeCols=0;freezeRows=[];isInWindow=t=>{const n=bd(t),r=Ys(t),i=this.visibleWindow,o=n>=i.x&&n<=i.x+i.width||n<this.freezeCols,s=r>=i.y&&r<=i.y+i.height||this.freezeRows.includes(r);return o&&s};setWindow(t,n,r){this.visibleWindow.x===t.x&&this.visibleWindow.y===t.y&&this.visibleWindow.width===t.width&&this.visibleWindow.height===t.height&&this.freezeCols===n&&vi(this.freezeRows,r)||(this.visibleWindow=t,this.freezeCols=n,this.freezeRows=r,this.clearOutOfWindow())}}class Zm extends wd{cache=new Map;setValue=(t,n)=>{this.cache.set(Wn(t[0],t[1]),n)};getValue=t=>this.cache.get(Wn(t[0],t[1]));clearOutOfWindow=()=>{for(const[t]of this.cache.entries())this.isInWindow(t)||this.cache.delete(t)}}class eo{cells;constructor(t=[]){this.cells=new Set(t.map(n=>Wn(n[0],n[1])))}add(t){this.cells.add(Wn(t[0],t[1]))}has(t){return t===void 0?!1:this.cells.has(Wn(t[0],t[1]))}remove(t){this.cells.delete(Wn(t[0],t[1]))}clear(){this.cells.clear()}get size(){return this.cells.size}hasHeader(){for(const t of this.cells)if(Ys(t)<0)return!0;return!1}hasItemInRectangle(t){for(let n=t.y;n<t.y+t.height;n++)for(let r=t.x;r<t.x+t.width;r++)if(this.cells.has(Wn(r,n)))return!0;return!1}hasItemInRegion(t){for(const n of t)if(this.hasItemInRectangle(n))return!0;return!1}*values(){for(const t of this.cells)yield js(t)}}function Jm(e){return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":e.textGroupHeader??e.textHeader,"--gdg-bg-group-header":e.bgGroupHeader??e.bgHeader,"--gdg-bg-group-header-hovered":e.bgGroupHeaderHovered??e.bgHeaderHovered,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bubble-height":`${e.bubbleHeight}px`,"--gdg-bubble-padding":`${e.bubblePadding}px`,"--gdg-bubble-margin":`${e.bubbleMargin}px`,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":e.horizontalBorderColor??e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":`${e.cellHorizontalPadding}px`,"--gdg-cell-vertical-padding":`${e.cellVerticalPadding}px`,"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize,"--gdg-checkbox-max-size":`${e.checkboxMaxSize}px`,...e.resizeIndicatorColor===void 0?{}:{"--gdg-resize-indicator-color":e.resizeIndicatorColor},...e.headerBottomBorderColor===void 0?{}:{"--gdg-header-bottom-border-color":e.headerBottomBorderColor},...e.roundingRadius===void 0?{}:{"--gdg-rounding-radius":`${e.roundingRadius}px`}}}const yd={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bubbleHeight:20,bubblePadding:6,bubbleMargin:4,bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#353fb5",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4,checkboxMaxSize:18};function Cd(){return yd}const Sd=Vt.createContext(yd);function Qm(){return Vt.useContext(Sd)}function ar(e,...t){const n={...e};for(const r of t)if(r!==void 0)for(const i in r)r.hasOwnProperty(i)&&(i==="bgCell"?n[i]=On(r[i],n[i]):n[i]=r[i]);return(n.headerFontFull===void 0||e.fontFamily!==n.fontFamily||e.headerFontStyle!==n.headerFontStyle)&&(n.headerFontFull=`${n.headerFontStyle} ${n.fontFamily}`),(n.baseFontFull===void 0||e.fontFamily!==n.fontFamily||e.baseFontStyle!==n.baseFontStyle)&&(n.baseFontFull=`${n.baseFontStyle} ${n.fontFamily}`),(n.markerFontFull===void 0||e.fontFamily!==n.fontFamily||e.markerFontStyle!==n.markerFontStyle)&&(n.markerFontFull=`${n.markerFontStyle} ${n.fontFamily}`),n}const Is=150;function ep(e,t,n,r){return r(t)?.measure?.(e,t,n)??Is}function xd(e,t,n,r,i,o,s,a,l){let u=0;const c=i===void 0?[]:i.map(h=>{const m=ep(e,h[r],t,l);return u=Math.max(u,m),m});if(c.length>5&&a){u=0;let h=0;for(const p of c)h+=p;const m=h/c.length;for(let p=0;p<c.length;p++)c[p]>=m*2?c[p]=0:u=Math.max(u,c[p])}const d=e.font;e.font=t.headerFontFull,u=Math.max(u,e.measureText(n.title).width+t.cellHorizontalPadding*2+(n.icon===void 0?0:28)),e.font=d;const g=Math.max(Math.ceil(o),Math.min(Math.floor(s),Math.ceil(u)));return{...n,width:g}}function tp(e,t,n,r,i,o,s,a,l){const u=f.useRef(t),c=f.useRef(n),d=f.useRef(s);u.current=t,c.current=n,d.current=s;const[g,h]=f.useMemo(()=>{if(typeof window>"u")return[null,null];const b=document.createElement("canvas");return b.style.display="none",b.style.opacity="0",b.style.position="fixed",[b,b.getContext("2d",{alpha:!1})]},[]);f.useLayoutEffect(()=>(g&&document.documentElement.append(g),()=>{g?.remove()}),[g]);const m=f.useRef({}),p=f.useRef(),[v,w]=f.useState();return f.useLayoutEffect(()=>{const b=c.current;if(b===void 0||e.every(Fo))return;let x=Math.max(1,10-Math.floor(e.length/1e4)),O=0;x<u.current&&x>1&&(x--,O=1);const R={x:0,y:0,width:e.length,height:Math.min(u.current,x)},M={x:0,y:u.current-1,width:e.length,height:1};(async()=>{const I=b(R,l.signal),k=O>0?b(M,l.signal):void 0;let A;typeof I=="object"?A=I:A=await Hl(I),k!==void 0&&(typeof k=="object"?A=[...A,...k]:A=[...A,...await Hl(k)]),p.current=e,w(A)})()},[l.signal,e]),f.useMemo(()=>{let x=e.every(Fo)?e:h===null?e.map(_=>Fo(_)?_:{..._,width:Is}):(h.font=d.current.baseFontFull,e.map((_,I)=>{if(Fo(_))return _;if(m.current[_.id]!==void 0)return{..._,width:m.current[_.id]};if(v===void 0||p.current!==e||_.id===void 0)return{..._,width:Is};const k=xd(h,s,_,I,v,i,o,!0,a);return m.current[_.id]=k.width,k})),O=0,R=0;const M=[];for(const[_,I]of x.entries())O+=I.width,I.grow!==void 0&&I.grow>0&&(R+=I.grow,M.push(_));if(O<r&&M.length>0){const _=[...x],I=r-O;let k=I;for(let A=0;A<M.length;A++){const D=M[A],C=(x[D].grow??0)/R,E=A===M.length-1?k:Math.min(k,Math.floor(I*C));_[D]={...x[D],growOffset:E,width:x[D].width+E},k-=E}x=_}return{sizedColumns:x,nonGrowWidth:O}},[r,e,h,v,s,i,o,a])}var Qa,mu;function np(){if(mu)return Qa;mu=1;function e(t,n,r){return t===t&&(r!==void 0&&(t=t<=r?t:r),n!==void 0&&(t=t>=n?t:n)),t}return Qa=e,Qa}var es,pu;function rp(){if(pu)return es;pu=1;var e=np(),t=Dc();function n(r,i,o){return o===void 0&&(o=i,i=void 0),o!==void 0&&(o=t(o),o=o===o?o:0),i!==void 0&&(i=t(i),i=i===i?i:0),e(t(r),i,o)}return es=n,es}var ip=rp();const Ln=lr(ip);var ts,vu;function op(){if(vu)return ts;vu=1;function e(){}return ts=e,ts}var ns,bu;function ap(){if(bu)return ns;bu=1;var e=ph(),t=op(),n=Oc(),r=1/0,i=e&&1/n(new e([,-0]))[1]==r?function(o){return new e(o)}:t;return ns=i,ns}var rs,wu;function sp(){if(wu)return rs;wu=1;var e=vh(),t=bh(),n=yh(),r=wh(),i=ap(),o=Oc(),s=200;function a(l,u,c){var d=-1,g=t,h=l.length,m=!0,p=[],v=p;if(c)m=!1,g=n;else if(h>=s){var w=u?null:i(l);if(w)return o(w);m=!1,g=r,v=new e}else v=u?[]:p;e:for(;++d<h;){var b=l[d],x=u?u(b):b;if(b=c||b!==0?b:0,m&&x===x){for(var O=v.length;O--;)if(v[O]===x)continue e;u&&v.push(x),p.push(b)}else g(v,x,c)||(v!==p&&v.push(x),p.push(b))}return p}return rs=a,rs}var is,yu;function lp(){if(yu)return is;yu=1;var e=sp();function t(n){return n&&n.length?e(n):[]}return is=t,is}var up=lp();const cp=lr(up);var dp=Ch();const Cu=lr(dp),$t='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',fp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}<rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="${t}"/></svg>`},hp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}<rect x="2" y="2" width="16" height="16" rx="4" fill="${n}"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="${t}"/></svg>`},gp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="${t}"/>
  </svg>`},mp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="${t}"/>
</svg>`},pp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
    <path
        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"
        fill="${n}"
    />
    <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"
        fill="${t}"
    />
</svg>`},kd=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="${t}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="${t}"/>
</svg>
  `},vp=e=>{const t=e.bgColor;return`${$t}
    <path stroke="${t}" stroke-width="2" d="M12 3v14"/>
    <path stroke="${t}" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>
    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="${t}"/>
  </svg>
`},bp=kd,wp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="${t}"/>
</svg>`},yp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="${t}"/>
    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="${t}"/>
    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
  </svg>`},Cp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="${t}"/>
</svg>`},Sp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path fill="${t}" d="M3 3h14v14H3z"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="${n}"/>
  </svg>`},xp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="${t}"/>
  </svg>`},kp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="${t}"/>
</svg>`},Mp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="${t}"/>
    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="${t}"/>
  </svg>`},Rp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="${t}"/>
</svg>`},Ip=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="8" width="10" height="8" rx="2" fill="${n}"/>
    <rect x="8" y="4" width="10" height="8" rx="2" fill="${n}"/>
    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="${t}"/>
  </svg>`},Ep=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path fill="${t}" d="M4 3h12v14H4z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="${n}"/>
</svg>`},Tp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="${t}"/>
  </svg>`},Dp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="${t}"/>
  </svg>`},Op=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="${t}"/>
</svg>`},Pp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="${t}"/>
  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="${t}"/>
</svg>`},_p=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="${t}"/>
  </svg>`},Fp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="${t}"/>
</svg>`},Lp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${$t}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="${t}"/>
  </svg>`},Ap=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="${t}"/>
</svg>`},Hp=e=>{const t=e.fgColor,n=e.bgColor;return`${$t}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="${t}"/>
</svg>`},zp=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>`},Vp=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>
`},$p={headerRowID:fp,headerNumber:gp,headerCode:hp,headerString:mp,headerBoolean:pp,headerAudioUri:bp,headerVideoUri:wp,headerEmoji:yp,headerImage:Cp,headerUri:kd,headerPhone:Sp,headerMarkdown:xp,headerDate:kp,headerTime:Mp,headerEmail:Rp,headerReference:Ip,headerIfThenElse:Ep,headerSingleValue:Tp,headerLookup:Dp,headerTextTemplate:Op,headerMath:Pp,headerRollup:_p,headerJoinStrings:Fp,headerSplitString:Lp,headerGeoDistance:Ap,headerArray:Hp,rowOwnerOverlay:zp,protectedColumnOverlay:Vp,renameIcon:vp};function Np(e,t){return e==="normal"?[t.bgIconHeader,t.fgIconHeader]:e==="selected"?["white",t.accentColor]:[t.accentColor,t.bgHeader]}class Bp{onSettled;spriteMap=new Map;headerIcons;inFlight=0;constructor(t,n){this.onSettled=n,this.headerIcons=t??{}}drawSprite(t,n,r,i,o,s,a,l=1){const[u,c]=Np(n,a),d=s*Math.ceil(window.devicePixelRatio),g=`${u}_${c}_${d}_${t}`;let h=this.spriteMap.get(g);if(h===void 0){const m=this.headerIcons[t];if(m===void 0)return;h=document.createElement("canvas");const p=h.getContext("2d");if(p===null)return;const v=new Image;v.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(m({fgColor:c,bgColor:u}))}`,this.spriteMap.set(g,h);const w=v.decode();if(w===void 0)return;this.inFlight++,w.then(()=>{p.drawImage(v,0,0,d,d)}).finally(()=>{this.inFlight--,this.inFlight===0&&this.onSettled()})}else l<1&&(r.globalAlpha=l),r.drawImage(h,0,0,d,d,i,o,s,s),l<1&&(r.globalAlpha=1)}}function Md(e){if(e.length===0)return;let t;for(const n of e)t=Math.min(t??n.y,n.y)}function ya(e,t,n,r,i,o,s,a,l){a=a??t;let u=t,c=e;const d=r-o;let g=!1;for(;u<n&&c<d;){const h=i(c);if(u+h>a&&l(u,c,h,!1,s&&c===r-1)===!0){g=!0;break}u+=h,c++}if(!g){u=n;for(let h=0;h<o;h++){c=r-1-h;const m=i(c);u-=m,l(u,c,m,!0,s&&c===r-1)}}}function Ir(e,t,n,r,i,o){let s=0,a=0;const l=i+r;for(const u of e){const c=u.sticky?a:s+n;if(o(u,c,l,u.sticky?0:a,t)===!0)break;s+=u.width,a+=u.sticky?u.width:0}}function Rd(e,t,n,r,i){let o=0,s=0;for(let a=0;a<e.length;a++){const l=e[a];let u=a+1,c=l.width;for(l.sticky&&(s+=c);u<e.length&&ro(e[u].group,l.group)&&e[u].sticky===e[a].sticky;){const p=e[u];c+=p.width,u++,a++,p.sticky&&(s+=p.width)}const d=l.sticky?0:n,g=o+d,h=l.sticky?0:Math.max(0,s-g),m=Math.min(c-h,t-(g+h));i([l.sourceIndex,e[u-1].sourceIndex],l.group??"",g+h,0,m,r),o+=c}}function Id(e,t,n,r,i,o,s){const[a,l]=e;let u,c;const d=s.find(g=>!g.sticky)?.sourceIndex??0;if(l>d){const g=Math.max(a,d);let h=t,m=r;for(let p=o.sourceIndex-1;p>=g;p--)h-=s[p].width,m+=s[p].width;for(let p=o.sourceIndex+1;p<=l;p++)m+=s[p].width;c={x:h,y:n,width:m,height:i}}if(d>a){const g=Math.min(l,d-1);let h=t,m=r;for(let p=o.sourceIndex-1;p>=a;p--)h-=s[p].width,m+=s[p].width;for(let p=o.sourceIndex+1;p<=g;p++)m+=s[p].width;u={x:h,y:n,width:m,height:i}}return[u,c]}function Wp(e,t,n,r){if(r==="any")return Ed(e,{x:t,y:n,width:1,height:1});if(r==="vertical"&&(t=e.x),r==="horizontal"&&(n=e.y),fd([t,n],e))return;const i=t-e.x,o=e.x+e.width-t,s=n-e.y+1,a=e.y+e.height-n,l=Math.min(r==="vertical"?Number.MAX_SAFE_INTEGER:i,r==="vertical"?Number.MAX_SAFE_INTEGER:o,r==="horizontal"?Number.MAX_SAFE_INTEGER:s,r==="horizontal"?Number.MAX_SAFE_INTEGER:a);return l===a?{x:e.x,y:e.y+e.height,width:e.width,height:n-e.y-e.height+1}:l===s?{x:e.x,y:n,width:e.width,height:e.y-n}:l===o?{x:e.x+e.width,y:e.y,width:t-e.x-e.width+1,height:e.height}:{x:t,y:e.y,width:e.x-t,height:e.height}}function io(e,t,n,r,i,o,s,a){return e<=i+s&&i<=e+n&&t<=o+a&&o<=t+r}function Nr(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function Ed(e,t){const n=Math.min(e.x,t.x),r=Math.min(e.y,t.y),i=Math.max(e.x+e.width,t.x+t.width)-n,o=Math.max(e.y+e.height,t.y+t.height)-r;return{x:n,y:r,width:i,height:o}}function Up(e,t){return e.x<=t.x&&e.y<=t.y&&e.x+e.width>=t.x+t.width&&e.y+e.height>=t.y+t.height}function qp(e,t,n,r){if(e.x>t||e.y>n||e.x<0&&e.y<0&&e.x+e.width>t&&e.y+e.height>n)return;if(e.x>=0&&e.y>=0&&e.x+e.width<=t&&e.y+e.height<=n)return e;const i=-4,o=-4,s=t+4,a=n+4,l=i-e.x,u=e.x+e.width-s,c=o-e.y,d=e.y+e.height-a,g=l>0?e.x+Math.floor(l/r)*r:e.x,h=u>0?e.x+e.width-Math.floor(u/r)*r:e.x+e.width,m=c>0?e.y+Math.floor(c/r)*r:e.y,p=d>0?e.y+e.height-Math.floor(d/r)*r:e.y+e.height;return{x:g,y:m,width:h-g,height:p-m}}function Gp(e,t,n,r,i){const[o,s,a,l]=t,[u,c,d,g]=i,{x:h,y:m,width:p,height:v}=e,w=[];if(p<=0||v<=0)return w;const b=h+p,x=m+v,O=h<o,R=m<s,M=h+p>a,_=m+v>l,I=h>=o&&h<a||b>o&&b<=a||h<o&&b>a,k=m>=s&&m<l||x>s&&x<=l||m<s&&x>l;if(I&&k){const D=Math.max(h,o),C=Math.max(m,s),E=Math.min(b,a),T=Math.min(x,l);w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:u,y:c,width:d-u+1,height:g-c+1}})}if(O&&R){const D=h,C=m,E=Math.min(b,o),T=Math.min(x,s);w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:0,y:0,width:u+1,height:c+1}})}if(R&&I){const D=Math.max(h,o),C=m,E=Math.min(b,a),T=Math.min(x,s);w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:u,y:0,width:d-u+1,height:c+1}})}if(R&&M){const D=Math.max(h,a),C=m,E=b,T=Math.min(x,s);w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:d,y:0,width:n-d+1,height:c+1}})}if(O&&k){const D=h,C=Math.max(m,s),E=Math.min(b,o),T=Math.min(x,l);w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:0,y:c,width:u+1,height:g-c+1}})}if(M&&k){const D=Math.max(h,a),C=Math.max(m,s),E=b,T=Math.min(x,l);w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:d,y:c,width:n-d+1,height:g-c+1}})}if(O&&_){const D=h,C=Math.max(m,l),E=Math.min(b,o),T=x;w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:0,y:g,width:u+1,height:r-g+1}})}if(_&&I){const D=Math.max(h,o),C=Math.max(m,l),E=Math.min(b,a),T=x;w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:u,y:g,width:d-u+1,height:r-g+1}})}if(M&&_){const D=Math.max(h,a),C=Math.max(m,l),E=b,T=x;w.push({rect:{x:D,y:C,width:E-D,height:T-C},clip:{x:d,y:g,width:n-d+1,height:r-g+1}})}return w}const Xp={kind:J.Loading,allowOverlay:!1};function Su(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v,w,b,x,O,R,M,_,I,k,A,D,C,E,T,S,B,X,Y){let ae=x?.size??Number.MAX_SAFE_INTEGER;const Q=performance.now();let ee=E.baseFontFull;e.font=ee;const re={ctx:e},te=[0,0],ue=v>0?Gr(l,v,u):0;let fe,se;const H=Md(b);return Ir(t,a,o,s,i,(P,G,ce,he,Ie)=>{const me=Math.max(0,he-G),Qe=G+me,xe=i+1,At=P.width-me,Xe=r-i-1;if(b.length>0){let Ee=!1;for(let De=0;De<b.length;De++){const tt=b[De];if(io(Qe,xe,At,Xe,tt.x,tt.y,tt.width,tt.height)){Ee=!0;break}}if(!Ee)return}const qe=()=>{e.save(),e.beginPath(),e.rect(Qe,xe,At,Xe),e.clip()},ne=O.columns.hasIndex(P.sourceIndex),ge=d(P.group??"").overrideTheme,Ce=P.themeOverride===void 0&&ge===void 0?E:ar(E,ge,P.themeOverride),de=Ce.baseFontFull;de!==ee&&(ee=de,e.font=de),qe();let Se;return ya(Ie,ce,r,l,u,v,w,H,(Ee,De,tt,Oe,Fe)=>{if(De<0||(te[0]=P.sourceIndex,te[1]=De,x!==void 0&&!x.has(te)))return;if(b.length>0){let Pe=!1;for(let Dt=0;Dt<b.length;Dt++){const Rt=b[Dt];if(io(G,Ee,P.width,tt,Rt.x,Rt.y,Rt.width,Rt.height)){Pe=!0;break}}if(!Pe)return}const Ve=O.rows.hasIndex(De),Te=h.hasIndex(De),Me=De<l?c(te):Xp;let pt=G,ht=P.width,Ye=!1,xt=!1;if(Me.span!==void 0){const[Pe,Dt]=Me.span,Rt=`${De},${Pe},${Dt},${P.sticky}`;if(se===void 0&&(se=new Set),se.has(Rt)){ae--;return}else{const Mn=Id(Me.span,G,Ee,P.width,tt,P,n),qt=P.sticky?Mn[0]:Mn[1];if(!P.sticky&&Mn[0]!==void 0&&(xt=!0),qt!==void 0){pt=qt.x,ht=qt.width,se.add(Rt),e.restore(),Se=void 0,e.save(),e.beginPath();const dn=Math.max(0,he-qt.x);e.rect(qt.x+dn,Ee,qt.width-dn,tt),fe===void 0&&(fe=[]),fe.push({x:qt.x+dn,y:Ee,width:qt.width-dn,height:tt}),e.clip(),Ye=!0}}}const Yt=g?.(De),Ht=Fe&&P.trailingRowOptions?.themeOverride!==void 0?P.trailingRowOptions?.themeOverride:void 0,Mt=Me.themeOverride===void 0&&Yt===void 0&&Ht===void 0?Ce:ar(Ce,Yt,Ht,Me.themeOverride);e.beginPath();const cn=Vm(te,Me,O);let Ut=$m(te,Me,O,p);const tn=Me.span!==void 0&&O.columns.some(Pe=>Me.span!==void 0&&Pe>=Me.span[0]&&Pe<=Me.span[1]);cn&&!m&&p?Ut=0:cn&&p&&(Ut=Math.max(Ut,1)),tn&&Ut++,cn||(Ve&&Ut++,ne&&!Fe&&Ut++);const Tt=Me.kind===J.Protected?Mt.bgCellMedium:Mt.bgCell;let yt;if((Oe||Tt!==E.bgCell)&&(yt=On(Tt,yt)),Ut>0||Te){Te&&(yt=On(Mt.bgHeader,yt));for(let Pe=0;Pe<Ut;Pe++)yt=On(Mt.accentLight,yt)}else if(R!==void 0){for(const Pe of R)if(Pe[0]===P.sourceIndex&&Pe[1]===De){yt=On(Mt.bgSearchResult,yt);break}}if(M!==void 0)for(let Pe=0;Pe<M.length;Pe++){const Dt=M[Pe],Rt=Dt.range;Dt.style!=="solid-outline"&&Rt.x<=P.sourceIndex&&P.sourceIndex<Rt.x+Rt.width&&Rt.y<=De&&De<Rt.y+Rt.height&&(yt=On(Dt.color,yt))}let nn=!1;if(x!==void 0){const Pe=Ee+1,Rt=(Oe?Pe+tt-1:Math.min(Pe+tt-1,r-ue))-Pe;(Rt!==tt-1||pt+1<=he)&&(nn=!0,e.save(),e.beginPath(),e.rect(pt+1,Pe,ht-1,Rt),e.clip()),yt=yt===void 0?Mt.bgCell:On(yt,Mt.bgCell)}const ut=P.sourceIndex===n.length-1,rn=De===l-1;yt!==void 0&&(e.fillStyle=yt,Se!==void 0&&(Se.fillStyle=yt),x!==void 0?e.fillRect(pt+1,Ee+1,ht-(ut?2:1),tt-(rn?2:1)):e.fillRect(pt,Ee,ht,tt)),Me.style==="faded"&&(e.globalAlpha=.6);let Nt;for(let Pe=0;Pe<k.length;Pe++){const Dt=k[Pe];if(Dt.item[0]===P.sourceIndex&&Dt.item[1]===De){Nt=Dt;break}}if(ht>Y&&!xt){const Pe=Mt.baseFontFull;Pe!==ee&&(e.font=Pe,ee=Pe),Se=Td(e,Me,P.sourceIndex,De,ut,rn,pt,Ee,ht,tt,Ut>0,Mt,yt??Mt.bgCell,_,I,Nt?.hoverAmount??0,A,C,Q,D,Se,T,S,B,X)}return nn&&e.restore(),Me.style==="faded"&&(e.globalAlpha=1),ae--,Ye&&(e.restore(),Se?.deprep?.(re),Se=void 0,qe(),ee=de,e.font=de),ae<=0}),e.restore(),ae<=0}),fe}const Wi=[0,0],Ui={x:0,y:0,width:0,height:0},os=[void 0,()=>{}];let Es=!1;function Yp(){Es=!0}function Td(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v,w,b,x,O,R,M,_,I){let k,A;v!==void 0&&v[0][0]===n&&v[0][1]===r&&(k=v[1][0],A=v[1][1]);let D;Wi[0]=n,Wi[1]=r,Ui.x=s,Ui.y=a,Ui.width=l,Ui.height=u,os[0]=M.getValue(Wi),os[1]=S=>M.setValue(Wi,S),Es=!1;const C={ctx:e,theme:d,col:n,row:r,cell:t,rect:Ui,highlighted:c,cellFillColor:g,hoverAmount:p,frameTime:b,hoverX:k,drawState:os,hoverY:A,imageLoader:h,spriteManager:m,hyperWrapping:w,overrideCursor:k!==void 0?I:void 0,requestAnimationFrame:Yp},E=Gm(C,t.lastUpdated,b,O,i,o),T=_(t);if(T!==void 0){O?.renderer!==T&&(O?.deprep?.(C),O=void 0);const S=T.drawPrep?.(C,O);x!==void 0&&!hi(C.cell)?x(C,()=>T.draw(C,t)):T.draw(C,t),D=S===void 0?void 0:{deprep:S?.deprep,fillStyle:S?.fillStyle,font:S?.font,renderer:T}}return(E||Es)&&R?.(Wi),D}function Ks(e,t,n,r,i,o,s,a,l=-20,u=-20,c=void 0,d="center",g="square"){const h=Math.floor(i+s/2),m=g==="circle"?1e4:t.roundingRadius??4;let p=nd(c??t.checkboxMaxSize,s,t.cellVerticalPadding),v=p/2;const w=td(d,r,o,t.cellHorizontalPadding,p),b=ed(w,h,p),x=rd(r+l,i+u,b);switch(n){case!0:{e.beginPath(),Jn(e,w-p/2,h-p/2,p,p,m),g==="circle"&&(v*=.8,p*=.8),e.fillStyle=a?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(w-v+p/4.23,h-v+p/1.97),e.lineTo(w-v+p/2.42,h-v+p/1.44),e.lineTo(w-v+p/1.29,h-v+p/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break}case ta:case!1:{e.beginPath(),Jn(e,w-p/2+.5,h-p/2+.5,p-1,p-1,m),e.lineWidth=1,e.strokeStyle=x?t.textDark:t.textMedium,e.stroke();break}case zs:{e.beginPath(),Jn(e,w-p/2,h-p/2,p,p,m),e.fillStyle=x?t.textMedium:t.textLight,e.fill(),g==="circle"&&(v*=.8,p*=.8),e.beginPath(),e.moveTo(w-p/3,h),e.lineTo(w+p/3,h),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break}default:no()}}function jp(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v,w,b){const x=s+a;if(x<=0)return;e.fillStyle=d.bgHeader,e.fillRect(0,0,i,x);const O=r?.[0]?.[0],R=r?.[0]?.[1],M=r?.[1]?.[0],_=r?.[1]?.[1],I=d.headerFontFull;e.font=I,Ir(t,0,o,0,x,(k,A,D,C)=>{if(v!==void 0&&!v.has([k.sourceIndex,-1]))return;const E=Math.max(0,C-A);e.save(),e.beginPath(),e.rect(A+E,a,k.width-E,s),e.clip();const T=p(k.group??"").overrideTheme,S=k.themeOverride===void 0&&T===void 0?d:ar(d,T,k.themeOverride);S.bgHeader!==d.bgHeader&&(e.fillStyle=S.bgHeader,e.fill()),S!==d&&(e.font=S.headerFontFull);const B=c.columns.hasIndex(k.sourceIndex),X=l!==void 0||u||k.headerRowMarkerDisabled===!0,Y=!X&&R===-1&&O===k.sourceIndex,ae=X?0:h.find(ue=>ue.item[0]===k.sourceIndex&&ue.item[1]===-1)?.hoverAmount??0,Q=c?.current!==void 0&&c.current.cell[0]===k.sourceIndex,ee=B?S.accentColor:Q?S.bgHeaderHasFocus:S.bgHeader,re=n?a:0,te=k.sourceIndex===0?0:1;B?(e.fillStyle=ee,e.fillRect(A+te,re,k.width-te,s)):(Q||ae>0)&&(e.beginPath(),e.rect(A+te,re,k.width-te,s),Q&&(e.fillStyle=S.bgHeaderHasFocus,e.fill()),ae>0&&(e.globalAlpha=ae,e.fillStyle=S.bgHeaderHovered,e.fill(),e.globalAlpha=1)),Pd(e,A,re,k.width,s,k,B,S,Y,Y?M:void 0,Y?_:void 0,Q,ae,g,w,b),e.restore()}),n&&Kp(e,t,i,o,a,r,d,g,h,m,p,v)}function Kp(e,t,n,r,i,o,s,a,l,u,c,d){const[h,m]=o?.[0]??[];let p=0;Rd(t,n,r,i,(v,w,b,x,O,R)=>{if(d!==void 0&&!d.hasItemInRectangle({x:v[0],y:-2,width:v[1]-v[0]+1,height:1}))return;e.save(),e.beginPath(),e.rect(b,x,O,R),e.clip();const M=c(w),_=M?.overrideTheme===void 0?s:ar(s,M.overrideTheme),I=m===-2&&h!==void 0&&h>=v[0]&&h<=v[1],k=I?_.bgGroupHeaderHovered??_.bgHeaderHovered:_.bgGroupHeader??_.bgHeader;if(k!==s.bgHeader&&(e.fillStyle=k,e.fill()),e.fillStyle=_.textGroupHeader??_.textHeader,M!==void 0){let A=b;if(M.icon!==void 0&&(a.drawSprite(M.icon,"normal",e,A+8,(i-20)/2,20,_),A+=26),e.fillText(M.name,A+8,i/2+Qn(e,s.headerFontFull)),M.actions!==void 0&&I){const D=Dd({x:b,y:x,width:O,height:R},M.actions);e.beginPath();const C=D[0].x-10,E=b+O-C;e.rect(C,0,E,i);const T=e.createLinearGradient(C,0,C+E,0),S=Br(k,0);T.addColorStop(0,S),T.addColorStop(10/E,k),T.addColorStop(1,k),e.fillStyle=T,e.fill(),e.globalAlpha=.6;const[B,X]=o?.[1]??[-1,-1];for(let Y=0;Y<M.actions.length;Y++){const ae=M.actions[Y],Q=D[Y],ee=Nr(Q,B+b,X);ee&&(e.globalAlpha=1),a.drawSprite(ae.icon,"normal",e,Q.x+Q.width/2-10,Q.y+Q.height/2-10,20,_),ee&&(e.globalAlpha=.6)}e.globalAlpha=1}}b!==0&&u(v[0])&&(e.beginPath(),e.moveTo(b+.5,0),e.lineTo(b+.5,i),e.strokeStyle=s.borderColor,e.lineWidth=1,e.stroke()),e.restore(),p=b+O}),e.beginPath(),e.moveTo(p+.5,0),e.lineTo(p+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=s.borderColor,e.lineWidth=1,e.stroke()}const Vo=30;function Zp(e,t,n,r,i){return{x:e+n-Vo,y:Math.max(t,t+r/2-Vo/2),width:Vo,height:Math.min(Vo,r)}}function Dd(e,t){const n=[];let r=e.x+e.width-26*t.length;const i=e.y+e.height/2-13,o=26,s=26;for(let a=0;a<t.length;a++)n.push({x:r,y:i,width:s,height:o}),r+=26;return n}function qi(e,t,n){return!n||e===void 0||(e.x=t-(e.x-t)-e.width),e}function Od(e,t,n,r,i,o,s,a){const l=s.cellHorizontalPadding,u=s.headerIconSize,c=Zp(n,r,i,o);let d=n+l;const g=t.icon===void 0?void 0:{x:d,y:r+(o-u)/2,width:u,height:u},h=g===void 0||t.overlayIcon===void 0?void 0:{x:g.x+9,y:g.y+6,width:18,height:18};g!==void 0&&(d+=Math.ceil(u*1.3));const m={x:d,y:r,width:i-d,height:o};let p;if(t.indicatorIcon!==void 0){const w=e===void 0?pd(t.title,s.headerFontFull)?.width??0:Xr(t.title,e,s.headerFontFull).width;m.width=w,d+=w+l,p={x:d,y:r+(o-u)/2,width:u,height:u}}const v=n+i/2;return{menuBounds:qi(c,v,a),iconBounds:qi(g,v,a),iconOverlayBounds:qi(h,v,a),textBounds:qi(m,v,a),indicatorIconBounds:qi(p,v,a)}}function xu(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p){if(o.rowMarker!==void 0&&o.headerRowMarkerDisabled!==!0){const b=o.rowMarkerChecked;b!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=d);const x=o.headerRowMarkerTheme!==void 0?ar(a,o.headerRowMarkerTheme):a;Ks(e,x,b,t,n,r,i,!1,void 0,void 0,a.checkboxMaxSize,"center",o.rowMarker),b!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=1);return}const v=s?a.textHeaderSelected:a.textHeader,w=o.hasMenu===!0&&(l||h&&s)&&p.menuBounds!==void 0;if(o.icon!==void 0&&p.iconBounds!==void 0){let b=s?"selected":"normal";o.style==="highlight"&&(b=s?"selected":"special"),g.drawSprite(o.icon,b,e,p.iconBounds.x,p.iconBounds.y,p.iconBounds.width,a),o.overlayIcon!==void 0&&p.iconOverlayBounds!==void 0&&g.drawSprite(o.overlayIcon,s?"selected":"special",e,p.iconOverlayBounds.x,p.iconOverlayBounds.y,p.iconOverlayBounds.width,a)}if(w&&r>35){const x=m?35:r-35,O=m?35*.7:r-35*.7,R=x/r,M=O/r,_=e.createLinearGradient(t,0,t+r,0),I=Br(v,0);_.addColorStop(m?1:0,v),_.addColorStop(R,v),_.addColorStop(M,I),_.addColorStop(m?0:1,I),e.fillStyle=_}else e.fillStyle=v;if(m&&(e.textAlign="right"),p.textBounds!==void 0&&e.fillText(o.title,m?p.textBounds.x+p.textBounds.width:p.textBounds.x,n+i/2+Qn(e,a.headerFontFull)),m&&(e.textAlign="left"),o.indicatorIcon!==void 0&&p.indicatorIconBounds!==void 0&&(!w||!io(p.menuBounds.x,p.menuBounds.y,p.menuBounds.width,p.menuBounds.height,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,p.indicatorIconBounds.height))){let b=s?"selected":"normal";o.style==="highlight"&&(b=s?"selected":"special"),g.drawSprite(o.indicatorIcon,b,e,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,a)}if(w&&p.menuBounds!==void 0){const b=p.menuBounds,x=u!==void 0&&c!==void 0&&Nr(b,u+t,c+n);if(x||(e.globalAlpha=.7),o.menuIcon===void 0||o.menuIcon===na.Triangle){e.beginPath();const O=b.x+b.width/2-5.5,R=b.y+b.height/2-3;Km(e,[{x:O,y:R},{x:O+11,y:R},{x:O+5.5,y:R+6}],1),e.fillStyle=v,e.fill()}else if(o.menuIcon===na.Dots){e.beginPath();const O=b.x+b.width/2,R=b.y+b.height/2;jm(e,O,R),e.fillStyle=v,e.fill()}else{const O=b.x+(b.width-a.headerIconSize)/2,R=b.y+(b.height-a.headerIconSize)/2;g.drawSprite(o.menuIcon,"normal",e,O,R,a.headerIconSize,a)}x||(e.globalAlpha=1)}}function Pd(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p){const v=Bs(o.title)==="rtl",w=Od(e,o,t,n,r,i,a,v);m!==void 0?m({ctx:e,theme:a,rect:{x:t,y:n,width:r,height:i},column:o,columnIndex:o.sourceIndex,isSelected:s,hoverAmount:g,isHovered:l,hasSelectedCell:d,spriteManager:h,menuBounds:w?.menuBounds??{x:0,y:0,height:0,width:0},hoverX:u,hoverY:c},()=>xu(e,t,n,r,i,o,s,a,l,u,c,g,h,p,v,w)):xu(e,t,n,r,i,o,s,a,l,u,c,g,h,p,v,w)}function Jp(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v,w,b){if(w!==void 0||t[t.length-1]!==n[t.length-1])return;const x=Md(v);Ir(t,l,s,a,o,(O,R,M,_,I)=>{if(O!==t[t.length-1])return;R+=O.width;const k=Math.max(R,_);k>r||(e.save(),e.beginPath(),e.rect(k,o+1,1e4,i-o-1),e.clip(),ya(I,M,i,u,c,m,p,x,(A,D,C,E)=>{if(!E&&v.length>0&&!v.some(Y=>io(R,A,1e4,C,Y.x,Y.y,Y.width,Y.height)))return;const T=g.hasIndex(D),S=h.hasIndex(D);e.beginPath();const B=d?.(D),X=B===void 0?b:ar(b,B);X.bgCell!==b.bgCell&&(e.fillStyle=X.bgCell,e.fillRect(R,A,1e4,C)),S&&(e.fillStyle=X.bgHeader,e.fillRect(R,A,1e4,C)),T&&(e.fillStyle=X.accentLight,e.fillRect(R,A,1e4,C))}),e.restore())})}function Qp(e,t,n,r,i,o,s,a,l){let u=!1;for(const m of t)if(!m.sticky){u=s(m.sourceIndex);break}const c=l.horizontalBorderColor??l.borderColor,d=l.borderColor,g=u?bi(t):0;let h;if(g!==0&&(h=du(d,l.bgCell),e.beginPath(),e.moveTo(g+.5,0),e.lineTo(g+.5,r),e.strokeStyle=h,e.stroke()),i>0){const m=d===c&&h!==void 0?h:du(c,l.bgCell),p=Gr(o,i,a);e.beginPath(),e.moveTo(0,r-p+.5),e.lineTo(n,r-p+.5),e.strokeStyle=m,e.stroke()}}const _d=(e,t,n)=>{let r=0,i=t,o=0,s=n;if(e!==void 0&&e.length>0){r=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;for(const a of e)r=Math.min(r,a.x-1),i=Math.max(i,a.x+a.width+1),o=Math.min(o,a.y-1),s=Math.max(s,a.y+a.height+1)}return{minX:r,maxX:i,minY:o,maxY:s}};function e0(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m){const p=m.bgCell,{minX:v,maxX:w,minY:b,maxY:x}=_d(a,o,s),O=[],R=s-Gr(h,g,u);let M=l,_=n,I=0;for(;M+i<R;){const C=M+i,E=u(_);if(C>=b&&C<=x-1){const S=c?.(_)?.bgCell;S!==void 0&&S!==p&&_>=h-g&&O.push({x:v,y:C,w:w-v,h:E,color:S})}M+=E,_<h-g&&(I=M),_++}let k=0;const A=Math.min(R,x)-I;if(A>0)for(let C=0;C<t.length;C++){const E=t[C];if(E.width===0)continue;const T=E.sticky?k:k+r,S=E.themeOverride?.bgCell;S!==void 0&&S!==p&&T>=v&&T<=w&&d(C+1)&&O.push({x:T,y:I,w:E.width,h:A,color:S}),k+=E.width}if(O.length===0)return;let D;e.beginPath();for(let C=O.length-1;C>=0;C--){const E=O[C];D===void 0?D=E.color:E.color!==D&&(e.fillStyle=D,e.fill(),e.beginPath(),D=E.color),e.rect(E.x,E.y,E.w,E.h)}D!==void 0&&(e.fillStyle=D,e.fill()),e.beginPath()}function ku(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v,w=!1){if(l!==void 0){e.beginPath(),e.save(),e.rect(0,0,o,s);for(const C of l)e.rect(C.x+1,C.y+1,C.width-1,C.height-1);e.clip("evenodd")}const b=v.horizontalBorderColor??v.borderColor,x=v.borderColor,{minX:O,maxX:R,minY:M,maxY:_}=_d(a,o,s),I=[];e.beginPath();let k=.5;for(let C=0;C<t.length;C++){const E=t[C];if(E.width===0)continue;k+=E.width;const T=E.sticky?k:k+r;T>=O&&T<=R&&h(C+1)&&I.push({x1:T,y1:Math.max(u,M),x2:T,y2:Math.min(s,_),color:x})}let A=s+.5;for(let C=p-m;C<p;C++){const E=d(C);A-=E,I.push({x1:O,y1:A,x2:R,y2:A,color:b})}if(w!==!0){let C=c+.5,E=n;const T=A;for(;C+i<T;){const S=C+i;if(S>=M&&S<=_-1){const B=g?.(E);I.push({x1:O,y1:S,x2:R,y2:S,color:B?.horizontalBorderColor??B?.borderColor??b})}C+=d(E),E++}}const D=Sh(I,C=>C.color);for(const C of Object.keys(D)){e.strokeStyle=C;for(const E of D[C])e.moveTo(E.x1,E.y1),e.lineTo(E.x2,E.y2);e.stroke(),e.beginPath()}l!==void 0&&e.restore()}function t0(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v,w,b){const x=[];e.imageSmoothingEnabled=!1;const O=Math.min(i.cellYOffset,s),R=Math.max(i.cellYOffset,s);let M=0;if(typeof w=="number")M+=(R-O)*w;else for(let T=O;T<R;T++)M+=w(T);s>i.cellYOffset&&(M=-M),M+=l-i.translateY;const _=Math.min(i.cellXOffset,o),I=Math.max(i.cellXOffset,o);let k=0;for(let T=_;T<I;T++)k+=p[T].width;o>i.cellXOffset&&(k=-k),k+=a-i.translateX;const A=bi(v);if(k!==0&&M!==0)return{regions:[]};const D=u>0?Gr(g,u,w):0,C=c-A-Math.abs(k),E=d-h-D-Math.abs(M)-1;if(C>150&&E>150){const T={sx:0,sy:0,sw:c*m,sh:d*m,dx:0,dy:0,dw:c*m,dh:d*m};if(M>0?(T.sy=(h+1)*m,T.sh=E*m,T.dy=(M+h+1)*m,T.dh=E*m,x.push({x:0,y:h,width:c,height:M+1})):M<0&&(T.sy=(-M+h+1)*m,T.sh=E*m,T.dy=(h+1)*m,T.dh=E*m,x.push({x:0,y:d+M-D,width:c,height:-M+D})),k>0?(T.sx=A*m,T.sw=C*m,T.dx=(k+A)*m,T.dw=C*m,x.push({x:A-1,y:0,width:k+2,height:d})):k<0&&(T.sx=(A-k)*m,T.sw=C*m,T.dx=A*m,T.dw=C*m,x.push({x:c+k,y:0,width:-k,height:d})),e.setTransform(1,0,0,1,0,0),b){if(A>0&&k!==0&&M===0&&(r===void 0||n?.[1]!==!1)){const S=A*m,B=d*m;e.drawImage(t,0,0,S,B,0,0,S,B)}if(D>0&&k===0&&M!==0&&(r===void 0||n?.[0]!==!1)){const S=(d-D)*m,B=c*m,X=D*m;e.drawImage(t,0,S,B,X,0,S,B,X)}}e.drawImage(t,T.sx,T.sy,T.sw,T.sh,T.dx,T.dy,T.dw,T.dh),e.scale(m,m)}return e.imageSmoothingEnabled=!0,{regions:x}}function n0(e,t,n,r,i,o,s,a,l,u){const c=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||i!==e.translateY||Ir(l,n,r,i,a,(d,g,h,m)=>{if(d.sourceIndex===u){const p=Math.max(g,m)+1;return c.push({x:p,y:0,width:o-p,height:s}),!0}}),c}function r0(e,t){if(t===void 0||e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.freezeColumns!==t.freezeColumns||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.maxScaleFactor!==t.maxScaleFactor)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let r=0;r<e.mappedColumns.length;r++){const i=e.mappedColumns[r],o=t.mappedColumns[r];if(vi(i,o))continue;if(n!==void 0||i.width===o.width)return!1;const{width:s,...a}=i,{width:l,...u}=o;if(!vi(a,u))return!1;n=r}return n===void 0?!0:n}return!0}function Mu(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p){const v=m?.filter(_=>_.style!=="no-outline");if(v===void 0||v.length===0)return;const w=bi(a),b=Gr(h,g,d),x=[l,0,a.length,h-g],O=[w,0,t,n-b],R=v.map(_=>{const I=_.range,k=_.style??"dashed";return Gp(I,x,t,n,O).map(A=>{const D=A.rect,C=Rs(D.x,D.y,t,n,c,u+c,r,i,o,s,h,l,g,a,d),E=D.width===1&&D.height===1?C:Rs(D.x+D.width-1,D.y+D.height-1,t,n,c,u+c,r,i,o,s,h,l,g,a,d);return D.x+D.width>=a.length&&(E.width-=1),D.y+D.height>=h&&(E.height-=1),{color:_.color,style:k,clip:A.clip,rect:qp({x:C.x,y:C.y,width:E.x+E.width-C.x,height:E.y+E.height-C.y},t,n,8)}})}),M=()=>{e.lineWidth=1;let _=!1;for(const I of R)for(const k of I)if(k?.rect!==void 0&&io(0,0,t,n,k.rect.x,k.rect.y,k.rect.width,k.rect.height)){const A=_,D=!Up(k.clip,k.rect);e.beginPath(),D&&(e.save(),e.rect(k.clip.x,k.clip.y,k.clip.width,k.clip.height),e.clip()),k.style==="dashed"&&!_?(e.setLineDash([5,3]),_=!0):(k.style==="solid"||k.style==="solid-outline")&&_&&(e.setLineDash([]),_=!1),e.strokeStyle=k.style==="solid-outline"?On(On(k.color,p.borderColor),p.bgCell):Br(k.color,1),e.closePath(),e.strokeRect(k.rect.x+.5,k.rect.y+.5,k.rect.width-1,k.rect.height-1),D&&(e.restore(),_=A)}_&&e.setLineDash([])};return M(),M}function Ru(e,t,n,r,i){e.beginPath(),e.moveTo(t,n),e.lineTo(t,r),e.lineWidth=2,e.strokeStyle=i,e.stroke(),e.globalAlpha=1}function as(e,t,n,r,i,o,s,a,l,u,c,d,g,h,m,p,v){if(c.current===void 0)return;const w=c.current.range,b=c.current.cell,x=[w.x+w.width-1,w.y+w.height-1];if(b[1]>=v&&x[1]>=v||!s.some(T=>T.sourceIndex===b[0]||T.sourceIndex===x[0]))return;const[R,M]=c.current.cell,_=g(c.current.cell),I=_.span??[R,R],k=M>=v-h,A=h>0&&!k?Gr(v,h,d)-1:0,D=x[1];let C;if(Ir(s,r,i,o,u,(T,S,B,X,Y)=>{if(T.sticky&&R>T.sourceIndex)return;const ae=T.sourceIndex<I[0],Q=T.sourceIndex>I[1],ee=T.sourceIndex===x[0];if(!(!ee&&(ae||Q)))return ya(Y,B,n,v,d,h,m,void 0,(re,te,ue)=>{if(te!==M&&te!==D)return;let fe=S,se=T.width;if(_.span!==void 0){const P=Id(_.span,S,re,T.width,ue,T,a),G=T.sticky?P[0]:P[1];G!==void 0&&(fe=G.x,se=G.width)}return te===D&&ee&&p&&(C=()=>{X>fe&&!T.sticky&&(e.beginPath(),e.rect(X,0,t-X,n),e.clip()),e.beginPath(),e.rect(fe+se-4,re+ue-4,4,4),e.fillStyle=T.themeOverride?.accentColor??l.accentColor,e.fill()}),C!==void 0}),C!==void 0}),C===void 0)return;const E=()=>{e.save(),e.beginPath(),e.rect(0,u,t,n-u-A),e.clip(),C?.(),e.restore()};return E(),E}function i0(e,t,n,r,i,o,s,a,l){l===void 0||l.size===0||(e.beginPath(),Rd(t,n,o,r,(u,c,d,g,h,m)=>{l.hasItemInRectangle({x:u[0],y:-2,width:u[1]-u[0]+1,height:1})&&e.rect(d,g,h,m)}),Ir(t,a,o,s,i,(u,c,d,g)=>{const h=Math.max(0,g-c),m=c+h+1,p=u.width-h-1;l.has([u.sourceIndex,-1])&&e.rect(m,r,p,i-r)}),e.clip())}function o0(e,t,n,r,i,o,s,a,l,u){let c=0;return Ir(e,o,r,i,n,(d,g,h,m,p)=>(ya(p,h,t,s,a,l,u,void 0,(v,w,b,x)=>{x||(c=Math.max(w,c))}),!0)),c}function Iu(e,t){const{canvasCtx:n,headerCanvasCtx:r,width:i,height:o,cellXOffset:s,cellYOffset:a,translateX:l,translateY:u,mappedColumns:c,enableGroups:d,freezeColumns:g,dragAndDropState:h,theme:m,drawFocus:p,headerHeight:v,groupHeaderHeight:w,disabledRows:b,rowHeight:x,verticalBorder:O,overrideCursor:R,isResizing:M,selection:_,fillHandle:I,freezeTrailingRows:k,rows:A,getCellContent:D,getGroupDetails:C,getRowThemeOverride:E,isFocused:T,drawHeaderCallback:S,prelightCells:B,drawCellCallback:X,highlightRegions:Y,resizeCol:ae,imageLoader:Q,lastBlitData:ee,hoverValues:re,hyperWrapping:te,hoverInfo:ue,spriteManager:fe,maxScaleFactor:se,hasAppendRow:H,touchMode:P,enqueue:G,renderStateProvider:ce,getCellRenderer:he,renderStrategy:Ie,bufferACtx:me,bufferBCtx:Qe,damage:xe,minimumCellWidth:At,resizeIndicator:Xe}=e;if(i===0||o===0)return;const qe=Ie==="double-buffer",ne=Math.min(se,Math.ceil(window.devicePixelRatio??1)),ge=Ie!=="direct"&&r0(e,t),Ce=n.canvas;(Ce.width!==i*ne||Ce.height!==o*ne)&&(Ce.width=i*ne,Ce.height=o*ne,Ce.style.width=i+"px",Ce.style.height=o+"px");const de=r.canvas,Se=d?w+v:v,Ee=Se+1;(de.width!==i*ne||de.height!==Ee*ne)&&(de.width=i*ne,de.height=Ee*ne,de.style.width=i+"px",de.style.height=Ee+"px");const De=me.canvas,tt=Qe.canvas;qe&&(De.width!==i*ne||De.height!==o*ne)&&(De.width=i*ne,De.height=o*ne,ee.current!==void 0&&(ee.current.aBufferScroll=void 0)),qe&&(tt.width!==i*ne||tt.height!==o*ne)&&(tt.width=i*ne,tt.height=o*ne,ee.current!==void 0&&(ee.current.bBufferScroll=void 0));const Oe=ee.current;if(ge===!0&&s===Oe?.cellXOffset&&a===Oe?.cellYOffset&&l===Oe?.translateX&&u===Oe?.translateY)return;let Fe=null;qe&&(Fe=n);const Ve=r;let Te;qe?xe!==void 0?Te=Oe?.lastBuffer==="b"?Qe:me:Te=Oe?.lastBuffer==="b"?me:Qe:Te=n;const Me=Te.canvas,pt=qe?Me===De?tt:De:Ce,ht=typeof x=="number"?()=>x:x;Ve.save(),Te.save(),Ve.beginPath(),Te.beginPath(),Ve.textBaseline="middle",Te.textBaseline="middle",ne!==1&&(Ve.scale(ne,ne),Te.scale(ne,ne));const Ye=Ms(c,s,i,h,l);let xt=[];const Yt=p&&_.current?.cell[1]===a&&u===0;let Ht=!1;if(Y!==void 0){for(const ut of Y)if(ut.style!=="no-outline"&&ut.range.y===a&&u===0){Ht=!0;break}}const Mt=()=>{jp(Ve,Ye,d,ue,i,l,v,w,h,M,_,m,fe,re,O,C,xe,S,P),ku(Ve,Ye,a,l,u,i,o,void 0,void 0,w,Se,ht,E,O,k,A,m,!0),Ve.beginPath(),Ve.moveTo(0,Ee-.5),Ve.lineTo(i,Ee-.5),Ve.strokeStyle=On(m.headerBottomBorderColor??m.horizontalBorderColor??m.borderColor,m.bgHeader),Ve.stroke(),Ht&&Mu(Ve,i,o,s,a,l,u,c,g,v,w,x,k,A,Y,m),Yt&&as(Ve,i,o,a,l,u,Ye,c,m,Se,_,ht,D,k,H,I,A)};if(xe!==void 0){const ut=Ye[Ye.length-1].sourceIndex+1,rn=xe.hasItemInRegion([{x:s,y:-2,width:ut,height:2},{x:s,y:a,width:ut,height:300},{x:0,y:a,width:g,height:300},{x:0,y:-2,width:g,height:2},{x:s,y:A-k,width:ut,height:k,when:k>0}]),Nt=Pe=>{Su(Pe,Ye,c,o,Se,l,u,a,A,ht,D,C,E,b,T,p,k,H,xt,xe,_,B,Y,Q,fe,re,ue,X,te,m,G,ce,he,R,At);const Dt=_.current;I&&p&&Dt!==void 0&&xe.has(hd(Dt.range))&&as(Pe,i,o,a,l,u,Ye,c,m,Se,_,ht,D,k,H,I,A)};rn&&(Nt(Te),Fe!==null&&(Fe.save(),Fe.scale(ne,ne),Fe.textBaseline="middle",Nt(Fe),Fe.restore()),xe.hasHeader()&&(i0(Ve,Ye,i,w,Se,l,u,a,xe),Mt())),Te.restore(),Ve.restore();return}if((ge!==!0||s!==Oe?.cellXOffset||l!==Oe?.translateX||Yt!==Oe?.mustDrawFocusOnHeader||Ht!==Oe?.mustDrawHighlightRingsOnHeader)&&Mt(),ge===!0){Dn(pt!==void 0&&Oe!==void 0);const{regions:ut}=t0(Te,pt,pt===De?Oe.aBufferScroll:Oe.bBufferScroll,pt===De?Oe.bBufferScroll:Oe.aBufferScroll,Oe,s,a,l,u,k,i,o,A,Se,ne,c,Ye,x,qe);xt=ut}else ge!==!1&&(Dn(Oe!==void 0),xt=n0(Oe,s,a,l,u,i,o,Se,Ye,ge));Qp(Te,Ye,i,o,k,A,O,ht,m);const cn=Mu(Te,i,o,s,a,l,u,c,g,v,w,x,k,A,Y,m),Ut=p?as(Te,i,o,a,l,u,Ye,c,m,Se,_,ht,D,k,H,I,A):void 0;if(Te.fillStyle=m.bgCell,xt.length>0){Te.beginPath();for(const ut of xt)Te.rect(ut.x,ut.y,ut.width,ut.height);Te.clip(),Te.fill(),Te.beginPath()}else Te.fillRect(0,0,i,o);const tn=Su(Te,Ye,c,o,Se,l,u,a,A,ht,D,C,E,b,T,p,k,H,xt,xe,_,B,Y,Q,fe,re,ue,X,te,m,G,ce,he,R,At);Jp(Te,Ye,c,i,o,Se,l,u,a,A,ht,E,_.rows,b,k,H,xt,xe,m),e0(Te,Ye,a,l,u,i,o,xt,Se,ht,E,O,k,A,m),ku(Te,Ye,a,l,u,i,o,xt,tn,w,Se,ht,E,O,k,A,m),cn?.(),Ut?.(),M&&Xe!=="none"&&Ir(Ye,0,l,0,Se,(ut,rn)=>ut.sourceIndex===ae?(Ru(Ve,rn+ut.width,0,Se+1,On(m.resizeIndicatorColor??m.accentLight,m.bgHeader)),Xe==="full"&&Ru(Te,rn+ut.width,Se,o,On(m.resizeIndicatorColor??m.accentLight,m.bgCell)),!0):!1),Fe!==null&&(Fe.fillStyle=m.bgCell,Fe.fillRect(0,0,i,o),Fe.drawImage(Te.canvas,0,0));const Tt=o0(Ye,o,Se,l,u,a,A,ht,k,H);Q?.setWindow({x:s,y:a,width:Ye.length,height:Tt-a},g,Array.from({length:k},(ut,rn)=>A-1-rn));const yt=Oe!==void 0&&(s!==Oe.cellXOffset||l!==Oe.translateX),nn=Oe!==void 0&&(a!==Oe.cellYOffset||u!==Oe.translateY);ee.current={cellXOffset:s,cellYOffset:a,translateX:l,translateY:u,mustDrawFocusOnHeader:Yt,mustDrawHighlightRingsOnHeader:Ht,lastBuffer:qe?Me===De?"a":"b":void 0,aBufferScroll:Me===De?[yt,nn]:Oe?.aBufferScroll,bBufferScroll:Me===tt?[yt,nn]:Oe?.bBufferScroll},Te.restore(),Ve.restore()}const a0=80;function s0(e){const t=e-1;return t*t*t+1}class l0{callback;constructor(t){this.callback=t}currentHoveredItem=void 0;leavingItems=[];lastAnimationTime;addToLeavingItems=t=>{this.leavingItems.some(r=>Ji(r.item,t.item))||this.leavingItems.push(t)};removeFromLeavingItems=t=>{const n=this.leavingItems.find(r=>Ji(r.item,t));return this.leavingItems=this.leavingItems.filter(r=>r!==n),n?.hoverAmount??0};cleanUpLeavingElements=()=>{this.leavingItems=this.leavingItems.filter(t=>t.hoverAmount>0)};shouldStep=()=>{const t=this.leavingItems.length>0,n=this.currentHoveredItem!==void 0&&this.currentHoveredItem.hoverAmount<1;return t||n};getAnimatingItems=()=>this.currentHoveredItem!==void 0?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map(t=>({...t,hoverAmount:s0(t.hoverAmount)}));step=t=>{if(this.lastAnimationTime===void 0)this.lastAnimationTime=t;else{const r=(t-this.lastAnimationTime)/a0;for(const o of this.leavingItems)o.hoverAmount=Ln(o.hoverAmount-r,0,1);this.currentHoveredItem!==void 0&&(this.currentHoveredItem.hoverAmount=Ln(this.currentHoveredItem.hoverAmount+r,0,1));const i=this.getAnimatingItems();this.callback(i),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=t,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0};setHovered=t=>{if(!Ji(this.currentHoveredItem?.item,t)){if(this.currentHoveredItem!==void 0&&this.addToLeavingItems(this.currentHoveredItem),t!==void 0){const n=this.removeFromLeavingItems(t);this.currentHoveredItem={item:t,hoverAmount:n}}else this.currentHoveredItem=void 0;this.lastAnimationTime===void 0&&window.requestAnimationFrame(this.step)}}}class u0{fn;val;constructor(t){this.fn=t}get value(){return this.val??(this.val=this.fn())}}function Zs(e){return new u0(e)}const c0=Zs(()=>window.navigator.userAgent.includes("Firefox")),oa=Zs(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome")),aa=Zs(()=>window.navigator.platform.toLowerCase().startsWith("mac"));function d0(e){const t=f.useRef([]),n=f.useRef(0),r=f.useRef(e);r.current=e;const i=f.useCallback(()=>{const o=()=>window.requestAnimationFrame(s),s=()=>{const a=t.current.map(js);t.current=[],r.current(new eo(a)),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?o:s)},[]);return f.useCallback(o=>{t.current.length===0&&i();const s=Wn(o[0],o[1]);t.current.includes(s)||t.current.push(s)},[i])}const Sr="header",An="group-header",sa="out-of-bounds";var mi;(function(e){e[e.Start=-2]="Start",e[e.StartPadding=-1]="StartPadding",e[e.Center=0]="Center",e[e.EndPadding=1]="EndPadding",e[e.End=2]="End"})(mi||(mi={}));function Fd(e,t){return e===t?!0:e?.kind==="out-of-bounds"?e?.kind===t?.kind&&e?.location[0]===t?.location[0]&&e?.location[1]===t?.location[1]&&e?.region[0]===t?.region[0]&&e?.region[1]===t?.region[1]:e?.kind===t?.kind&&e?.location[0]===t?.location[0]&&e?.location[1]===t?.location[1]}const Eu=6,f0=(e,t)=>e.kind===J.Custom?e.copyData:t?.(e)?.getAccessibilityString(e)??"",h0=(e,t)=>{const{width:n,height:r,accessibilityHeight:i,columns:o,cellXOffset:s,cellYOffset:a,headerHeight:l,fillHandle:u=!1,groupHeaderHeight:c,rowHeight:d,rows:g,getCellContent:h,getRowThemeOverride:m,onHeaderMenuClick:p,onHeaderIndicatorClick:v,enableGroups:w,isFilling:b,onCanvasFocused:x,onCanvasBlur:O,isFocused:R,selection:M,freezeColumns:_,onContextMenu:I,freezeTrailingRows:k,fixedShadowX:A=!0,fixedShadowY:D=!0,drawFocusRing:C,onMouseDown:E,onMouseUp:T,onMouseMoveRaw:S,onMouseMove:B,onItemHovered:X,dragAndDropState:Y,firstColAccessible:ae,onKeyDown:Q,onKeyUp:ee,highlightRegions:re,canvasRef:te,onDragStart:ue,onDragEnd:fe,eventTargetRef:se,isResizing:H,resizeColumn:P,isDragging:G,isDraggable:ce=!1,allowResize:he,disabledRows:Ie,hasAppendRow:me,getGroupDetails:Qe,theme:xe,prelightCells:At,headerIcons:Xe,verticalBorder:qe,drawCell:ne,drawHeader:ge,onCellFocused:Ce,onDragOverCell:de,onDrop:Se,onDragLeave:Ee,imageWindowLoader:De,smoothScrollX:tt=!1,smoothScrollY:Oe=!1,experimental:Fe,getCellRenderer:Ve,resizeIndicator:Te="full"}=e,Me=e.translateX??0,pt=e.translateY??0,ht=Math.max(_,Math.min(o.length-1,s)),Ye=f.useRef(null),xt=f.useRef(Fe?.eventTarget??window),Yt=xt.current,Ht=De,Mt=f.useRef(),[cn,Ut]=f.useState(!1),tn=f.useRef([]),Tt=f.useRef(),[yt,nn]=f.useState(),[ut,rn]=f.useState(),Nt=f.useRef(null),[Pe,Dt]=f.useState(),[Rt,Mn]=f.useState(!1),qt=f.useRef(Rt);qt.current=Rt;const dn=f.useMemo(()=>new Bp(Xe,()=>{sn.current=void 0,jt.current()}),[Xe]),Rn=w?c+l:l,Zt=f.useRef(-1),Jt=(Fe?.enableFirefoxRescaling??!1)&&c0.value,We=(Fe?.enableSafariRescaling??!1)&&oa.value;f.useLayoutEffect(()=>{window.devicePixelRatio===1||!Jt&&!We||(Zt.current!==-1&&Ut(!0),window.clearTimeout(Zt.current),Zt.current=window.setTimeout(()=>{Ut(!1),Zt.current=-1},200))},[a,ht,Me,pt,Jt,We]);const Ot=Hm(o,_),yn=f.useMemo(()=>A?bi(Ot,Y):0,[Ot,Y,A]),vt=f.useCallback((z,ie,ke)=>{const Re=z.getBoundingClientRect();if(ie>=Ot.length||ke>=g)return;const oe=Re.width/n,ve=Rs(ie,ke,n,r,c,Rn,ht,a,Me,pt,g,_,k,Ot,d);return oe!==1&&(ve.x*=oe,ve.y*=oe,ve.width*=oe,ve.height*=oe),ve.x+=Re.x,ve.y+=Re.y,ve},[n,r,c,Rn,ht,a,Me,pt,g,_,k,Ot,d]),_t=f.useCallback((z,ie,ke,Re)=>{const oe=z.getBoundingClientRect(),ve=oe.width/n,je=(ie-oe.left)/ve,we=(ke-oe.top)/ve,Ke=5,ct=Ms(Ot,ht,n,void 0,Me);let Qt=0,ft=0;const It=typeof PointerEvent<"u"&&Re instanceof PointerEvent&&Re.pointerType==="mouse"||typeof MouseEvent<"u"&&Re instanceof MouseEvent,nt=typeof PointerEvent<"u"&&Re instanceof PointerEvent&&Re.pointerType==="touch"||typeof TouchEvent<"u"&&Re instanceof TouchEvent;It&&(Qt=Re.button,ft=Re.buttons);const et=Nm(je,ct,Me),zt=Bm(we,r,w,l,c,g,d,a,pt,k),Bt=Re?.shiftKey===!0,Or=Re?.ctrlKey===!0,en=Re?.metaKey===!0,on=[je<0?-1:n<je?1:0,we<Rn?-1:r<we?1:0];let gr;if(et===-1||we<0||je<0||zt===void 0||je>n||we>r){const Pt=je>n?1:je<0?-1:0,kn=we>r?1:we<0?-1:0;let In=Pt*2,mr=kn*2;Pt===0&&(In=et===-1?mi.EndPadding:mi.Center),kn===0&&(mr=zt===void 0?mi.EndPadding:mi.Center);let rt=!1;if(et===-1&&zt===-1){const Yn=vt(z,Ot.length-1,-1);Dn(Yn!==void 0),rt=ie<Yn.x+Yn.width+Ke}const ln=je>n&&je<n+Ss()||we>r&&we<r+Ss();gr={kind:sa,location:[et!==-1?et:je<0?0:Ot.length-1,zt??g-1],region:[In,mr],shiftKey:Bt,ctrlKey:Or,metaKey:en,isEdge:rt,isTouch:nt,button:Qt,buttons:ft,scrollEdge:on,isMaybeScrollbar:ln}}else if(zt<=-1){let Pt=vt(z,et,zt);Dn(Pt!==void 0);let kn=Pt!==void 0&&Pt.x+Pt.width-ie<=Ke;const In=et-1;ie-Pt.x<=Ke&&In>=0?(kn=!0,Pt=vt(z,In,zt),Dn(Pt!==void 0),gr={kind:w&&zt===-2?An:Sr,location:[In,zt],bounds:Pt,group:Ot[In].group??"",isEdge:kn,shiftKey:Bt,ctrlKey:Or,metaKey:en,isTouch:nt,localEventX:ie-Pt.x,localEventY:ke-Pt.y,button:Qt,buttons:ft,scrollEdge:on}):gr={kind:w&&zt===-2?An:Sr,group:Ot[et].group??"",location:[et,zt],bounds:Pt,isEdge:kn,shiftKey:Bt,ctrlKey:Or,metaKey:en,isTouch:nt,localEventX:ie-Pt.x,localEventY:ke-Pt.y,button:Qt,buttons:ft,scrollEdge:on}}else{const Pt=vt(z,et,zt);Dn(Pt!==void 0);const kn=Pt!==void 0&&Pt.x+Pt.width-ie<Ke;let In=!1;if(u&&M.current!==void 0){const mr=hd(M.current.range),rt=vt(z,mr[0],mr[1]);if(rt!==void 0){const ln=rt.x+rt.width-2,Yn=rt.y+rt.height-2;In=Math.abs(ln-ie)<Eu&&Math.abs(Yn-ke)<Eu}}gr={kind:"cell",location:[et,zt],bounds:Pt,isEdge:kn,shiftKey:Bt,ctrlKey:Or,isFillHandle:In,metaKey:en,isTouch:nt,localEventX:ie-Pt.x,localEventY:ke-Pt.y,button:Qt,buttons:ft,scrollEdge:on}}return gr},[n,Ot,ht,Me,r,w,l,c,g,d,a,pt,k,vt,u,M,Rn]),[Cn]=yt??[],er=f.useRef(()=>{}),zn=f.useRef(yt);zn.current=yt;const[$,$e]=f.useMemo(()=>{const z=document.createElement("canvas"),ie=document.createElement("canvas");return z.style.display="none",z.style.opacity="0",z.style.position="fixed",ie.style.display="none",ie.style.opacity="0",ie.style.position="fixed",[z.getContext("2d",{alpha:!1}),ie.getContext("2d",{alpha:!1})]},[]);f.useLayoutEffect(()=>{if(!($===null||$e===null))return document.documentElement.append($.canvas),document.documentElement.append($e.canvas),()=>{$.canvas.remove(),$e.canvas.remove()}},[$,$e]);const Ge=f.useMemo(()=>new Zm,[]),Ct=Jt&&cn?1:We&&cn?2:5,mn=Fe?.disableMinimumCellWidth===!0?1:10,sn=f.useRef(),be=f.useRef(null),dt=f.useRef(null),bt=f.useCallback(()=>{const z=Ye.current,ie=Nt.current;if(z===null||ie===null||(be.current===null&&(be.current=z.getContext("2d",{alpha:!1}),z.width=0,z.height=0),dt.current===null&&(dt.current=ie.getContext("2d",{alpha:!1}),ie.width=0,ie.height=0),be.current===null||dt.current===null||$===null||$e===null))return;let ke=!1;const Re=je=>{ke=!0,Dt(je)},oe=sn.current,ve={headerCanvasCtx:dt.current,canvasCtx:be.current,bufferACtx:$,bufferBCtx:$e,width:n,height:r,cellXOffset:ht,cellYOffset:a,translateX:Math.round(Me),translateY:Math.round(pt),mappedColumns:Ot,enableGroups:w,freezeColumns:_,dragAndDropState:Y,theme:xe,headerHeight:l,groupHeaderHeight:c,disabledRows:Ie??ot.empty(),rowHeight:d,verticalBorder:qe,isResizing:H,resizeCol:P,isFocused:R,selection:M,fillHandle:u,drawCellCallback:ne,hasAppendRow:me,overrideCursor:Re,maxScaleFactor:Ct,freezeTrailingRows:k,rows:g,drawFocus:C,getCellContent:h,getGroupDetails:Qe??(je=>({name:je})),getRowThemeOverride:m,drawHeaderCallback:ge,prelightCells:At,highlightRegions:re,imageLoader:Ht,lastBlitData:Tt,damage:Mt.current,hoverValues:tn.current,hoverInfo:zn.current,spriteManager:dn,scrolling:cn,hyperWrapping:Fe?.hyperWrapping??!1,touchMode:Rt,enqueue:er.current,renderStateProvider:Ge,renderStrategy:Fe?.renderStrategy??(oa.value?"double-buffer":"single-buffer"),getCellRenderer:Ve,minimumCellWidth:mn,resizeIndicator:Te};ve.damage===void 0?(sn.current=ve,Iu(ve,oe)):Iu(ve,void 0),!ke&&(ve.damage===void 0||ve.damage.has(zn?.current?.[0]))&&Dt(void 0)},[$,$e,n,r,ht,a,Me,pt,Ot,w,_,Y,xe,l,c,Ie,d,qe,H,me,P,R,M,u,k,g,C,Ct,h,Qe,m,ne,ge,At,re,Ht,dn,cn,Fe?.hyperWrapping,Fe?.renderStrategy,Rt,Ge,Ve,mn,Te]),jt=f.useRef(bt);f.useLayoutEffect(()=>{bt(),jt.current=bt},[bt]),f.useLayoutEffect(()=>{(async()=>{document?.fonts?.ready!==void 0&&(await document.fonts.ready,sn.current=void 0,jt.current())})()},[]);const Gt=f.useCallback(z=>{Mt.current=z,jt.current(),Mt.current=void 0},[]),qn=d0(Gt);er.current=qn;const ho=f.useCallback(z=>{Gt(new eo(z.map(ie=>ie.cell)))},[Gt]);Ht.setCallback(Gt);const[ka,go]=f.useState(!1),[Er,cr]=Cn??[],Ma=Er!==void 0&&cr===-1&&o[Er].headerRowMarkerDisabled!==!0,wi=Er!==void 0&&cr===-2;let mo=!1,yi=!1,Tr=Pe;if(Tr===void 0&&Er!==void 0&&cr!==void 0&&cr>-1&&cr<g){const z=h([Er,cr],!0);mo=z.kind===Hn.NewRow||z.kind===Hn.Marker&&z.markerKind!=="number",yi=z.kind===J.Boolean&&Vs(z),Tr=z.cursor}const Ci=G?"grabbing":(ut??!1)||H?"col-resize":ka||b?"crosshair":Tr!==void 0?Tr:Ma||mo||yi||wi?"pointer":"default",Pn=f.useMemo(()=>({contain:"strict",display:"block",cursor:Ci}),[Ci]),Sn=f.useRef("default"),Si=se?.current;Si!=null&&Sn.current!==Pn.cursor&&(Si.style.cursor=Sn.current=Pn.cursor);const tr=f.useCallback((z,ie,ke,Re)=>{if(Qe===void 0)return;const oe=Qe(z);if(oe.actions!==void 0){const ve=Dd(ie,oe.actions);for(const[je,we]of ve.entries())if(Nr(we,ke+ie.x,Re+we.y))return oe.actions[je]}},[Qe]),nr=f.useCallback((z,ie,ke,Re)=>{const oe=Ot[ie];if(!G&&!H&&!(ut??!1)){const ve=vt(z,ie,-1);Dn(ve!==void 0);const je=Od(void 0,oe,ve.x,ve.y,ve.width,ve.height,xe,Bs(oe.title)==="rtl");if(oe.hasMenu===!0&&je.menuBounds!==void 0&&Nr(je.menuBounds,ke,Re))return{area:"menu",bounds:je.menuBounds};if(oe.indicatorIcon!==void 0&&je.indicatorIconBounds!==void 0&&Nr(je.indicatorIconBounds,ke,Re))return{area:"indicator",bounds:je.indicatorIconBounds}}},[Ot,vt,ut,G,H,xe]),Yr=f.useRef(0),dr=f.useRef(),Dr=f.useRef(!1),jr=f.useCallback(z=>{const ie=Ye.current,ke=se?.current;if(ie===null||z.target!==ie&&z.target!==ke)return;Dr.current=!0;const Re=z.clientX,oe=z.clientY;if(z.target===ke&&ke!==null){const je=ke.getBoundingClientRect();if(Re>je.right||oe>je.bottom)return}const ve=_t(ie,Re,oe,z);dr.current=ve.location,ve.isTouch&&(Yr.current=Date.now()),qt.current!==ve.isTouch&&Mn(ve.isTouch),!(ve.kind===Sr&&nr(ie,ve.location[0],Re,oe)!==void 0)&&(ve.kind===An&&tr(ve.group,ve.bounds,ve.localEventX,ve.localEventY)!==void 0||(E?.(ve),!ve.isTouch&&ce!==!0&&ce!==ve.kind&&ve.button<3&&ve.button!==1&&z.preventDefault()))},[se,ce,_t,tr,nr,E]);bn("pointerdown",jr,Yt,!1);const rr=f.useRef(0),Kr=f.useCallback(z=>{const ie=rr.current;rr.current=Date.now();const ke=Ye.current;if(Dr.current=!1,T===void 0||ke===null)return;const Re=se?.current,oe=z.target!==ke&&z.target!==Re,ve=z.clientX,je=z.clientY,we=z.pointerType==="mouse"?z.button<3:!0;let Ke=_t(ke,ve,je,z);Ke.isTouch&&Yr.current!==0&&Date.now()-Yr.current>500&&(Ke={...Ke,isLongTouch:!0}),ie!==0&&Date.now()-ie<(Ke.isTouch?1e3:500)&&(Ke={...Ke,isDoubleClick:!0}),qt.current!==Ke.isTouch&&Mn(Ke.isTouch),!oe&&z.cancelable&&we&&z.preventDefault();const[ct]=Ke.location,Qt=nr(ke,ct,ve,je);if(Ke.kind===Sr&&Qt!==void 0){(Ke.button!==0||dr.current?.[0]!==ct||dr.current?.[1]!==-1)&&T(Ke,!0);return}else if(Ke.kind===An){const ft=tr(Ke.group,Ke.bounds,Ke.localEventX,Ke.localEventY);if(ft!==void 0){Ke.button===0&&ft.onClick(Ke);return}}T(Ke,oe)},[T,se,_t,nr,tr]);bn("pointerup",Kr,Yt,!1);const Ra=f.useCallback(z=>{const ie=Ye.current;if(ie===null)return;const ke=se?.current,Re=z.target!==ie&&z.target!==ke;let oe,ve,je=!0;z instanceof MouseEvent?(oe=z.clientX,ve=z.clientY,je=z.button<3):(oe=z.changedTouches[0].clientX,ve=z.changedTouches[0].clientY);const we=_t(ie,oe,ve,z);qt.current!==we.isTouch&&Mn(we.isTouch),!Re&&z.cancelable&&je&&z.preventDefault();const[Ke]=we.location;if(we.kind===Sr){const ct=nr(ie,Ke,oe,ve);ct!==void 0&&we.button===0&&dr.current?.[0]===Ke&&dr.current?.[1]===-1&&(ct.area==="menu"?p?.(Ke,ct.bounds):ct.area==="indicator"&&v?.(Ke,ct.bounds))}else if(we.kind===An){const ct=tr(we.group,we.bounds,we.localEventX,we.localEventY);ct!==void 0&&we.button===0&&ct.onClick(we)}},[se,_t,nr,p,v,tr]);bn("click",Ra,Yt,!1);const po=f.useCallback(z=>{const ie=Ye.current,ke=se?.current;if(ie===null||z.target!==ie&&z.target!==ke||I===void 0)return;const Re=_t(ie,z.clientX,z.clientY,z);I(Re,()=>{z.cancelable&&z.preventDefault()})},[se,_t,I]);bn("contextmenu",po,se?.current??null,!1);const Le=f.useCallback(z=>{Mt.current=new eo(z.map(ie=>ie.item)),tn.current=z,jt.current(),Mt.current=void 0},[]),xi=f.useMemo(()=>new l0(Le),[Le]),vo=f.useRef(xi);vo.current=xi,f.useLayoutEffect(()=>{const z=vo.current;if(Cn===void 0||Cn[1]<0){z.setHovered(Cn);return}const ie=h(Cn,!0),ke=Ve(ie),Re=ke===void 0&&ie.kind===J.Custom||ke?.needsHover!==void 0&&(typeof ke.needsHover=="boolean"?ke.needsHover:ke.needsHover(ie));z.setHovered(Re?Cn:void 0)},[h,Ve,Cn]);const fr=f.useRef(),Ia=f.useCallback(z=>{const ie=Ye.current;if(ie===null)return;const ke=se?.current,Re=z.target!==ie&&z.target!==ke,oe=_t(ie,z.clientX,z.clientY,z);if(oe.kind!=="out-of-bounds"&&Re&&!Dr.current&&!oe.isTouch)return;const ve=(we,Ke)=>{nn(ct=>ct===we||ct?.[0][0]===we?.[0][0]&&ct?.[0][1]===we?.[0][1]&&(ct?.[1][0]===we?.[1][0]&&ct?.[1][1]===we?.[1][1]||!Ke)?ct:we)};if(!Fd(oe,fr.current))Dt(void 0),X?.(oe),ve(oe.kind===sa?void 0:[oe.location,[oe.localEventX,oe.localEventY]],!0),fr.current=oe;else if(oe.kind==="cell"||oe.kind===Sr||oe.kind===An){let we=!1,Ke=!0;if(oe.kind==="cell"){const Qt=h(oe.location);Ke=Ve(Qt)?.needsHoverPosition??Qt.kind===J.Custom,we=Ke}else we=!0;const ct=[oe.location,[oe.localEventX,oe.localEventY]];ve(ct,Ke),zn.current=ct,we&&Gt(new eo([oe.location]))}const je=oe.location[0]>=(ae?0:1);rn(oe.kind===Sr&&oe.isEdge&&je&&he===!0),go(oe.kind==="cell"&&oe.isFillHandle),S?.(z),B(oe)},[se,_t,ae,he,S,B,X,h,Ve,Gt]);bn("pointermove",Ia,Yt,!0);const Gn=f.useCallback(z=>{const ie=Ye.current;if(ie===null)return;let ke,Re;M.current!==void 0&&(ke=vt(ie,M.current.cell[0],M.current.cell[1]),Re=M.current.cell),Q?.({bounds:ke,stopPropagation:()=>z.stopPropagation(),preventDefault:()=>z.preventDefault(),cancel:()=>{},ctrlKey:z.ctrlKey,metaKey:z.metaKey,shiftKey:z.shiftKey,altKey:z.altKey,key:z.key,keyCode:z.keyCode,rawEvent:z,location:Re})},[Q,M,vt]),ki=f.useCallback(z=>{const ie=Ye.current;if(ie===null)return;let ke,Re;M.current!==void 0&&(ke=vt(ie,M.current.cell[0],M.current.cell[1]),Re=M.current.cell),ee?.({bounds:ke,stopPropagation:()=>z.stopPropagation(),preventDefault:()=>z.preventDefault(),cancel:()=>{},ctrlKey:z.ctrlKey,metaKey:z.metaKey,shiftKey:z.shiftKey,altKey:z.altKey,key:z.key,keyCode:z.keyCode,rawEvent:z,location:Re})},[ee,M,vt]),bo=f.useCallback(z=>{if(Ye.current=z,te!==void 0&&(te.current=z),Fe?.eventTarget)xt.current=Fe.eventTarget;else if(z===null)xt.current=window;else{const ie=z.getRootNode();ie===document&&(xt.current=window),xt.current=ie}},[te,Fe?.eventTarget]),wo=f.useCallback(z=>{const ie=Ye.current;if(ie===null||ce===!1||H){z.preventDefault();return}let ke,Re;const oe=_t(ie,z.clientX,z.clientY);if(ce!==!0&&oe.kind!==ce){z.preventDefault();return}const ve=(ft,It)=>{ke=ft,Re=It};let je,we,Ke;const ct=(ft,It,nt)=>{je=ft,we=It,Ke=nt};let Qt=!1;if(ue?.({...oe,setData:ve,setDragImage:ct,preventDefault:()=>Qt=!0,defaultPrevented:()=>Qt}),!Qt&&ke!==void 0&&Re!==void 0&&z.dataTransfer!==null)if(z.dataTransfer.setData(ke,Re),z.dataTransfer.effectAllowed="copyLink",je!==void 0&&we!==void 0&&Ke!==void 0)z.dataTransfer.setDragImage(je,we,Ke);else{const[ft,It]=oe.location;if(It!==void 0){const nt=document.createElement("canvas"),et=vt(ie,ft,It);Dn(et!==void 0);const zt=Math.ceil(window.devicePixelRatio??1);nt.width=et.width*zt,nt.height=et.height*zt;const Bt=nt.getContext("2d");Bt!==null&&(Bt.scale(zt,zt),Bt.textBaseline="middle",It===-1?(Bt.font=xe.headerFontFull,Bt.fillStyle=xe.bgHeader,Bt.fillRect(0,0,nt.width,nt.height),Pd(Bt,0,0,et.width,et.height,Ot[ft],!1,xe,!1,void 0,void 0,!1,0,dn,ge,!1)):(Bt.font=xe.baseFontFull,Bt.fillStyle=xe.bgCell,Bt.fillRect(0,0,nt.width,nt.height),Td(Bt,h([ft,It]),0,It,!1,!1,0,0,et.width,et.height,!1,xe,xe.bgCell,Ht,dn,1,void 0,!1,0,void 0,void 0,void 0,Ge,Ve,()=>{}))),nt.style.left="-100%",nt.style.position="absolute",nt.style.width=`${et.width}px`,nt.style.height=`${et.height}px`,document.body.append(nt),z.dataTransfer.setDragImage(nt,et.width/2,et.height/2),window.setTimeout(()=>{nt.remove()},0)}}else z.preventDefault()},[ce,H,_t,ue,vt,xe,Ot,dn,ge,h,Ht,Ge,Ve]);bn("dragstart",wo,se?.current??null,!1,!1);const Mi=f.useRef(),Ea=f.useCallback(z=>{const ie=Ye.current;if(Se!==void 0&&z.preventDefault(),ie===null||de===void 0)return;const ke=_t(ie,z.clientX,z.clientY),[Re,oe]=ke.location,ve=Re-(ae?0:1),[je,we]=Mi.current??[];(je!==ve||we!==oe)&&(Mi.current=[ve,oe],de([ve,oe],z.dataTransfer))},[ae,_t,de,Se]);bn("dragover",Ea,se?.current??null,!1,!1);const Xn=f.useCallback(()=>{Mi.current=void 0,fe?.()},[fe]);bn("dragend",Xn,se?.current??null,!1,!1);const hr=f.useCallback(z=>{const ie=Ye.current;if(ie===null||Se===void 0)return;z.preventDefault();const ke=_t(ie,z.clientX,z.clientY),[Re,oe]=ke.location,ve=Re-(ae?0:1);Se([ve,oe],z.dataTransfer)},[ae,_t,Se]);bn("drop",hr,se?.current??null,!1,!1);const _n=f.useCallback(()=>{Ee?.()},[Ee]);bn("dragleave",_n,se?.current??null,!1,!1);const U=f.useRef(M);U.current=M;const Ft=f.useRef(null),ir=f.useCallback(z=>{Ye.current===null||!Ye.current.contains(document.activeElement)||(z===null&&U.current.current!==void 0?te?.current?.focus({preventScroll:!0}):z!==null&&z.focus({preventScroll:!0}),Ft.current=z)},[te]);f.useImperativeHandle(t,()=>({focus:()=>{const z=Ft.current;z===null||!document.contains(z)?te?.current?.focus({preventScroll:!0}):z.focus({preventScroll:!0})},getBounds:(z,ie)=>{if(!(te===void 0||te.current===null))return vt(te.current,z??0,ie??-1)},damage:ho,getMouseArgsForPosition:(z,ie,ke)=>{if(!(te===void 0||te.current===null))return _t(te.current,z,ie,ke)}}),[te,ho,vt]);const Ri=f.useRef(),yo=nm(()=>{if(n<50||Fe?.disableAccessibilityTree===!0)return null;let z=Ms(Ot,ht,n,Y,Me);const ie=ae?0:-1;!ae&&z[0]?.sourceIndex===0&&(z=z.slice(1));const[ke,Re]=M.current?.cell??[],oe=M.current?.range,ve=z.map(we=>we.sourceIndex),je=kr(a,Math.min(g,a+i));return ke!==void 0&&Re!==void 0&&!(ve.includes(ke)&&je.includes(Re))&&ir(null),f.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":g+1,"aria-multiselectable":"true","aria-colcount":Ot.length+ie},f.createElement("thead",{role:"rowgroup"},f.createElement("tr",{role:"row","aria-rowindex":1},z.map(we=>f.createElement("th",{role:"columnheader","aria-selected":M.columns.hasIndex(we.sourceIndex),"aria-colindex":we.sourceIndex+1+ie,tabIndex:-1,onFocus:Ke=>{if(Ke.target!==Ft.current)return Ce?.([we.sourceIndex,-1])},key:we.sourceIndex},we.title)))),f.createElement("tbody",{role:"rowgroup"},je.map(we=>f.createElement("tr",{role:"row","aria-selected":M.rows.hasIndex(we),key:we,"aria-rowindex":we+2},z.map(Ke=>{const ct=Ke.sourceIndex,Qt=Wn(ct,we),ft=ke===ct&&Re===we,It=oe!==void 0&&ct>=oe.x&&ct<oe.x+oe.width&&we>=oe.y&&we<oe.y+oe.height,nt=`glide-cell-${ct}-${we}`,et=[ct,we],zt=h(et,!0);return f.createElement("td",{key:Qt,role:"gridcell","aria-colindex":ct+1+ie,"aria-selected":It,"aria-readonly":hi(zt)||!di(zt),id:nt,"data-testid":nt,onClick:()=>{const Bt=te?.current;if(Bt!=null)return Q?.({bounds:vt(Bt,ct,we),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:et})},onFocusCapture:Bt=>{if(!(Bt.target===Ft.current||Ri.current?.[0]===ct&&Ri.current?.[1]===we))return Ri.current=et,Ce?.(et)},ref:ft?ir:void 0,tabIndex:-1},f0(zt,Ve))})))))},[n,Ot,ht,Y,Me,g,a,i,M,ir,h,te,Q,vt,Ce],200),Zr=_===0||!A?0:ht>_?1:Ln(-Me/100,0,1),Ta=-a*32+pt,Ii=D?Ln(-Ta/100,0,1):0,K=f.useMemo(()=>{if(!Zr&&!Ii)return null;const z={position:"absolute",top:0,left:yn,width:n-yn,height:r,opacity:Zr,pointerEvents:"none",transition:tt?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},ie={position:"absolute",top:Rn,left:0,width:n,height:r,opacity:Ii,pointerEvents:"none",transition:Oe?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return f.createElement(f.Fragment,null,Zr>0&&f.createElement("div",{id:"shadow-x",style:z}),Ii>0&&f.createElement("div",{id:"shadow-y",style:ie}))},[Zr,Ii,yn,n,tt,Rn,r,Oe]),xn=f.useMemo(()=>({position:"absolute",top:0,left:0}),[]);return f.createElement(f.Fragment,null,f.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:Gn,onKeyUp:ki,onFocus:x,onBlur:O,ref:bo,style:Pn},yo),f.createElement("canvas",{ref:Nt,style:xn}),K)},g0=f.memo(f.forwardRef(h0));function Gi(e,t,n,r){return Ln(Math.round(t-(e.growOffset??0)),Math.ceil(n),Math.floor(r))}const m0=e=>{const[t,n]=f.useState(),[r,i]=f.useState(),[o,s]=f.useState(),[a,l]=f.useState(),[u,c]=f.useState(!1),[d,g]=f.useState(),[h,m]=f.useState(),[p,v]=f.useState(),[w,b]=f.useState(!1),[x,O]=f.useState(),{onHeaderMenuClick:R,onHeaderIndicatorClick:M,getCellContent:_,onColumnMoved:I,onColumnResize:k,onColumnResizeStart:A,onColumnResizeEnd:D,gridRef:C,maxColumnWidth:E,minColumnWidth:T,onRowMoved:S,lockColumns:B,onColumnProposeMove:X,onMouseDown:Y,onMouseUp:ae,onItemHovered:Q,onDragStart:ee,canvasRef:re}=e,te=(k??D??A)!==void 0,{columns:ue,selection:fe}=e,se=fe.columns,H=f.useCallback(ne=>{const[ge,Ce]=ne.location;o!==void 0&&a!==ge&&ge>=B?(c(!0),l(ge)):h!==void 0&&Ce!==void 0?(b(!0),v(Math.max(0,Ce))):r===void 0&&!u&&!w&&Q?.(ne)},[o,h,a,Q,B,r,u,w]),P=I!==void 0,G=f.useCallback(ne=>{if(ne.button===0){const[ge,Ce]=ne.location;if(ne.kind==="out-of-bounds"&&ne.isEdge&&te){const de=C?.current?.getBounds(ue.length-1,-1);de!==void 0&&(n(de.x),i(ue.length-1))}else if(ne.kind==="header"&&ge>=B){const de=re?.current;if(ne.isEdge&&te&&de){n(ne.bounds.x),i(ge);const Ee=de.getBoundingClientRect().width/de.offsetWidth,De=ne.bounds.width/Ee;A?.(ue[ge],De,ge,De+(ue[ge].growOffset??0))}else ne.kind==="header"&&P&&(g(ne.bounds.x),s(ge))}else ne.kind==="cell"&&B>0&&ge===0&&Ce!==void 0&&S!==void 0&&(O(ne.bounds.y),m(Ce))}Y?.(ne)},[Y,te,B,S,C,ue,P,A,re]),ce=f.useCallback((ne,ge)=>{u||w||R?.(ne,ge)},[u,w,R]),he=f.useCallback((ne,ge)=>{u||w||M?.(ne,ge)},[u,w,M]),Ie=f.useRef(-1),me=f.useCallback(()=>{Ie.current=-1,m(void 0),v(void 0),O(void 0),b(!1),s(void 0),l(void 0),g(void 0),c(!1),i(void 0),n(void 0)},[]),Qe=f.useCallback((ne,ge)=>{if(ne.button===0){if(r!==void 0){if(se?.hasIndex(r)===!0)for(const de of se){if(de===r)continue;const Se=ue[de],Ee=Gi(Se,Ie.current,T,E);k?.(Se,Ee,de,Ee+(Se.growOffset??0))}const Ce=Gi(ue[r],Ie.current,T,E);if(D?.(ue[r],Ce,r,Ce+(ue[r].growOffset??0)),se.hasIndex(r))for(const de of se){if(de===r)continue;const Se=ue[de],Ee=Gi(Se,Ie.current,T,E);D?.(Se,Ee,de,Ee+(Se.growOffset??0))}}me(),o!==void 0&&a!==void 0&&X?.(o,a)!==!1&&I?.(o,a),h!==void 0&&p!==void 0&&S?.(h,p)}ae?.(ne,ge)},[ae,r,o,a,h,p,se,D,ue,T,E,k,I,S,me,X]),xe=f.useMemo(()=>{if(!(o===void 0||a===void 0)&&o!==a&&X?.(o,a)!==!1)return{src:o,dest:a}},[o,a,X]),At=f.useCallback(ne=>{const ge=re?.current;if(o!==void 0&&d!==void 0)Math.abs(ne.clientX-d)>20&&c(!0);else if(h!==void 0&&x!==void 0)Math.abs(ne.clientY-x)>20&&b(!0);else if(r!==void 0&&t!==void 0&&ge){const de=ge.getBoundingClientRect().width/ge.offsetWidth,Se=(ne.clientX-t)/de,Ee=ue[r],De=Gi(Ee,Se,T,E);if(k?.(Ee,De,r,De+(Ee.growOffset??0)),Ie.current=Se,se?.first()===r)for(const tt of se){if(tt===r)continue;const Oe=ue[tt],Fe=Gi(Oe,Ie.current,T,E);k?.(Oe,Fe,tt,Fe+(Oe.growOffset??0))}}},[o,d,h,x,r,t,ue,T,E,k,se,re]),Xe=f.useCallback((ne,ge)=>{if(h===void 0||p===void 0)return _(ne,ge);let[Ce,de]=ne;return de===p?de=h:(de>p&&(de-=1),de>=h&&(de+=1)),_([Ce,de],ge)},[h,p,_]),qe=f.useCallback(ne=>{ee?.(ne),ne.defaultPrevented()||me()},[me,ee]);return f.createElement(g0,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,resizeColumn:r,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,resizeIndicator:e.resizeIndicator,verticalBorder:e.verticalBorder,width:e.width,getCellContent:Xe,isResizing:r!==void 0,onHeaderMenuClick:ce,onHeaderIndicatorClick:he,isDragging:u,onItemHovered:H,onDragStart:qe,onMouseDown:G,allowResize:te,onMouseUp:Qe,dragAndDropState:xe,onMouseMoveRaw:At,ref:C})};function p0(e){const t=f.useRef(null),[n,r]=f.useState({width:e?.[0],height:e?.[1]});return f.useLayoutEffect(()=>{const i=s=>{for(const a of s){const{width:l,height:u}=a&&a.contentRect||{};r(c=>c.width===l&&c.height===u?c:{width:l,height:u})}},o=new window.ResizeObserver(i);return t.current&&o.observe(t.current,void 0),()=>{o.disconnect()}},[t.current]),{ref:t,...n}}const v0=(e,t,n)=>{const r=f.useRef(null),i=f.useRef(null),o=f.useRef(null),s=f.useRef(0),a=f.useRef(t);a.current=t;const l=n.current;f.useEffect(()=>{const u=()=>{if(i.current===!1&&l!==null){const g=[l.scrollLeft,l.scrollTop];if(o.current?.[0]===g[0]&&o.current?.[1]===g[1])if(s.current>10){o.current=null,i.current=null;return}else s.current++;else s.current=0,a.current(g[0],g[1]),o.current=g;r.current=window.setTimeout(u,8.333333333333334)}},c=()=>{i.current=!0,o.current=null,r.current!==null&&(window.clearTimeout(r.current),r.current=null)},d=g=>{g.touches.length===0&&(i.current=!1,s.current=0,r.current=window.setTimeout(u,8.333333333333334))};if(e&&l!==null){const g=l;return g.addEventListener("touchstart",c),g.addEventListener("touchend",d),()=>{g.removeEventListener("touchstart",c),g.removeEventListener("touchend",d),r.current!==null&&window.clearTimeout(r.current)}}},[e,l])},b0=()=>e=>e.isSafari?"scroll":"auto",w0=un("div")({name:"ScrollRegionStyle",class:"gdg-s1dgczr6",propsAsIs:!1,vars:{"s1dgczr6-0":[b0()]}}),y0=33554400,C0=5e6;function S0(e){const[t,n]=f.useState(!1),r=typeof window>"u"?null:window,i=f.useRef(0);return bn("touchstart",f.useCallback(()=>{window.clearTimeout(i.current),n(!0)},[]),r,!0,!1),bn("touchend",f.useCallback(o=>{o.touches.length===0&&(i.current=window.setTimeout(()=>n(!1),e))},[e]),r,!0,!1),t}const x0=e=>{const{children:t,clientHeight:n,scrollHeight:r,scrollWidth:i,update:o,draggable:s,className:a,preventDiagonalScrolling:l=!1,paddingBottom:u=0,paddingRight:c=0,rightElement:d,rightElementProps:g,kineticScrollPerfHack:h=!1,scrollRef:m,initialSize:p}=e,v=[],w=g?.sticky??!1,b=g?.fill??!1,x=f.useRef(0),O=f.useRef(0),R=f.useRef(null),M=typeof window>"u"?1:window.devicePixelRatio,_=f.useRef(M);f.useEffect(()=>{if(_.current!==M){x.current=0,O.current=0,_.current=M;const fe=R.current;fe!==null&&S.current(fe.scrollLeft,fe.scrollTop)}},[M]);const I=f.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),k=f.useRef(null),A=S0(200),[D,C]=f.useState(!0),E=f.useRef(0);f.useLayoutEffect(()=>{if(!D||A||I.current.lockDirection===void 0)return;const fe=R.current;if(fe===null)return;const[se,H]=I.current.lockDirection;se!==void 0?fe.scrollLeft=se:H!==void 0&&(fe.scrollTop=H),I.current.lockDirection=void 0},[A,D]);const T=f.useCallback((fe,se)=>{const H=R.current;if(H===null)return;se=se??H.scrollTop,fe=fe??H.scrollLeft;const P=I.current.scrollTop,G=I.current.scrollLeft,ce=fe-G,he=se-P;A&&ce!==0&&he!==0&&(Math.abs(ce)>3||Math.abs(he)>3)&&l&&I.current.lockDirection===void 0&&(I.current.lockDirection=Math.abs(ce)<Math.abs(he)?[G,void 0]:[void 0,P]);const Ie=I.current.lockDirection;fe=Ie?.[0]??fe,se=Ie?.[1]??se,I.current.scrollLeft=fe,I.current.scrollTop=se;const me=H.clientWidth,Qe=H.clientHeight,xe=se,At=O.current-xe,Xe=H.scrollHeight-Qe;O.current=xe;let qe;if(Xe>0&&r>H.scrollHeight+5)if(Math.abs(At)>2e3||xe===0||xe===Xe){const ne=Math.max(0,Math.min(1,xe/Xe)),ge=r-Qe;qe=ne*ge,x.current=qe}else x.current-=At,qe=x.current;else qe=xe,x.current=qe;qe=Math.max(0,Math.min(qe,r-Qe)),x.current=qe,Ie!==void 0&&(window.clearTimeout(E.current),C(!1),E.current=window.setTimeout(()=>C(!0),200)),o({x:fe,y:qe,width:me-c,height:Qe-u,paddingRight:k.current?.clientWidth??0})},[u,c,r,o,l,A]);v0(h&&oa.value,T,R);const S=f.useRef(T);S.current=T;const B=f.useRef(),X=f.useRef(!1);f.useLayoutEffect(()=>{X.current?T():X.current=!0},[T,u,c]);const Y=f.useCallback(fe=>{R.current=fe,m!==void 0&&(m.current=fe)},[m]);let ae=0,Q=0;const ee=Math.min(r,y0);for(v.push(f.createElement("div",{key:ae++,style:{width:i,height:0}}));Q<ee;){const fe=Math.min(C0,ee-Q);v.push(f.createElement("div",{key:ae++,style:{width:0,height:fe}})),Q+=fe}const{ref:re,width:te,height:ue}=p0(p);return typeof window<"u"&&(B.current?.height!==ue||B.current?.width!==te)&&(window.setTimeout(()=>S.current(),0),B.current={width:te,height:ue}),(te??0)===0||(ue??0)===0?f.createElement("div",{ref:re}):f.createElement("div",{ref:re},f.createElement(w0,{isSafari:oa.value},f.createElement("div",{className:"dvn-underlay"},t),f.createElement("div",{ref:Y,style:B.current,draggable:s,onDragStart:fe=>{s||(fe.stopPropagation(),fe.preventDefault())},className:"dvn-scroller "+(a??""),onScroll:()=>T()},f.createElement("div",{className:"dvn-scroll-inner"+(d===void 0?" dvn-hidden":"")},f.createElement("div",{className:"dvn-stack"},v),d!==void 0&&f.createElement(f.Fragment,null,!b&&f.createElement("div",{className:"dvn-spacer"}),f.createElement("div",{ref:k,style:{height:ue,maxHeight:n-Math.ceil(M%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:c,flexGrow:b?1:void 0,right:w?c??0:void 0,pointerEvents:"auto"}},d))))))},k0=e=>{const{columns:t,rows:n,rowHeight:r,headerHeight:i,groupHeaderHeight:o,enableGroups:s,freezeColumns:a,experimental:l,nonGrowWidth:u,clientSize:c,className:d,onVisibleRegionChanged:g,scrollRef:h,preventDiagonalScrolling:m,rightElement:p,rightElementProps:v,overscrollX:w,overscrollY:b,initialSize:x,smoothScrollX:O=!1,smoothScrollY:R=!1,isDraggable:M}=e,{paddingRight:_,paddingBottom:I}=l??{},[k,A]=c,D=f.useRef(),C=f.useRef(),E=f.useRef(),T=f.useRef(),S=u+Math.max(0,w??0);let B=s?i+o:i;if(typeof r=="number")B+=n*r;else for(let Q=0;Q<n;Q++)B+=r(Q);b!==void 0&&(B+=b);const X=f.useRef(),Y=f.useCallback(()=>{if(X.current===void 0)return;const Q={...X.current};let ee=0,re=Q.x<0?-Q.x:0,te=0,ue=0;Q.x=Q.x<0?0:Q.x;let fe=0;for(let he=0;he<a;he++)fe+=t[he].width;for(const he of t){const Ie=ee-fe;if(Q.x>=Ie+he.width)ee+=he.width,ue++,te++;else if(Q.x>Ie)ee+=he.width,O?re+=Ie-Q.x:ue++,te++;else if(Q.x+Q.width>Ie)ee+=he.width,te++;else break}let se=0,H=0,P=0;if(typeof r=="number")R?(H=Math.floor(Q.y/r),se=H*r-Q.y):H=Math.ceil(Q.y/r),P=Math.ceil(Q.height/r)+H,se<0&&P++;else{let he=0;for(let Ie=0;Ie<n;Ie++){const me=r(Ie),Qe=he+(R?0:me/2);if(Q.y>=he+me)he+=me,H++,P++;else if(Q.y>Qe)he+=me,R?se+=Qe-Q.y:H++,P++;else if(Q.y+Q.height>me/2+he)he+=me,P++;else break}}H=Math.max(0,Math.min(H,n-1)),P=Math.max(H,Math.min(P,n));const G={x:ue,y:H,width:te-ue,height:P-H},ce=D.current;(ce===void 0||ce.y!==G.y||ce.x!==G.x||ce.height!==G.height||ce.width!==G.width||C.current!==re||E.current!==se||Q.width!==T.current?.[0]||Q.height!==T.current?.[1])&&(g?.({x:ue,y:H,width:te-ue,height:P-H},Q.width,Q.height,Q.paddingRight??0,re,se),D.current=G,C.current=re,E.current=se,T.current=[Q.width,Q.height])},[t,r,n,g,a,O,R]),ae=f.useCallback(Q=>{X.current=Q,Y()},[Y]);return f.useEffect(()=>{Y()},[Y]),f.createElement(x0,{scrollRef:h,className:d,kineticScrollPerfHack:l?.kineticScrollPerfHack,preventDiagonalScrolling:m,draggable:M===!0||typeof M=="string",scrollWidth:S+(_??0),scrollHeight:B+(I??0),clientHeight:A,rightElement:p,paddingBottom:I,paddingRight:_,rightElementProps:v,update:ae,initialSize:x},f.createElement(m0,{eventTargetRef:h,width:k,height:A,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,onColumnProposeMove:e.onColumnProposeMove,verticalBorder:e.verticalBorder,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}))},M0=un("div")({name:"SearchWrapper",class:"gdg-seveqep",propsAsIs:!1}),R0=f.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},f.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),I0=f.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},f.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),E0=f.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},f.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),T0=10,D0=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:i,searchInputRef:o,searchValue:s,searchResults:a,onSearchValueChange:l,getCellsForSelection:u,onSearchResultsChanged:c,showSearch:d=!1,onSearchClose:g}=e,[h]=f.useState(()=>"search-box-"+Math.round(Math.random()*1e3)),[m,p]=f.useState(""),v=s??m,w=f.useCallback(ee=>{p(ee),l?.(ee)},[l]),[b,x]=f.useState(),O=f.useRef(b);O.current=b,f.useEffect(()=>{a!==void 0&&(a.length>0?x(ee=>({rowsSearched:r,results:a.length,selectedIndex:ee?.selectedIndex??-1})):x(void 0))},[r,a]);const R=f.useRef();R.current===void 0&&(R.current=new AbortController);const M=f.useRef(),[_,I]=f.useState([]),k=a??_,A=f.useCallback(()=>{M.current!==void 0&&(window.cancelAnimationFrame(M.current),M.current=void 0,R.current.abort())},[]),D=f.useRef(n);D.current=n;const C=f.useCallback(ee=>{const re=new RegExp(ee.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let te=D.current,ue=Math.min(10,r),fe=0;x(void 0),I([]);const se=[],H=async()=>{if(u===void 0)return;const P=performance.now(),G=r-fe;let ce=u({x:0,y:te,width:i.length,height:Math.min(ue,G,r-te)},R.current.signal);typeof ce=="function"&&(ce=await ce());let he=!1;for(const[Xe,qe]of ce.entries())for(const[ne,ge]of qe.entries()){let Ce;switch(ge.kind){case J.Text:case J.Number:Ce=ge.displayData;break;case J.Uri:case J.Markdown:Ce=ge.data;break;case J.Boolean:Ce=typeof ge.data=="boolean"?ge.data.toString():void 0;break;case J.Image:case J.Bubble:Ce=ge.data.join("🐳");break;case J.Custom:Ce=ge.copyData;break}Ce!==void 0&&re.test(Ce)&&(se.push([ne,Xe+te]),he=!0)}const Ie=performance.now();he&&I([...se]),fe+=ce.length,Dn(fe<=r);const me=O.current?.selectedIndex??-1;x({results:se.length,rowsSearched:fe,selectedIndex:me}),c?.(se,me),te+ue>=r?te=0:te+=ue;const Qe=Ie-P,xe=Math.max(Qe,1),At=T0/xe;ue=Math.ceil(ue*At),fe<r&&se.length<1e3&&(M.current=window.requestAnimationFrame(H))};A(),M.current=window.requestAnimationFrame(H)},[A,i.length,u,c,r]),E=f.useCallback(()=>{g?.(),x(void 0),I([]),c?.([],-1),A(),t?.current?.focus()},[A,t,g,c]),T=f.useCallback(ee=>{w(ee.target.value),a===void 0&&(ee.target.value===""?(x(void 0),I([]),A()):C(ee.target.value))},[C,A,w,a]);f.useEffect(()=>{d&&o.current!==null&&(w(""),o.current.focus({preventScroll:!0}))},[d,o,w]);const S=f.useCallback(ee=>{if(ee?.stopPropagation?.(),b===void 0)return;const re=(b.selectedIndex+1)%b.results;x({...b,selectedIndex:re}),c?.(k,re)},[b,c,k]),B=f.useCallback(ee=>{if(ee?.stopPropagation?.(),b===void 0)return;let re=(b.selectedIndex-1)%b.results;re<0&&(re+=b.results),x({...b,selectedIndex:re}),c?.(k,re)},[c,k,b]),X=f.useCallback(ee=>{(ee.ctrlKey||ee.metaKey)&&ee.nativeEvent.code==="KeyF"||ee.key==="Escape"?(E(),ee.stopPropagation(),ee.preventDefault()):ee.key==="Enter"&&(ee.shiftKey?B():S())},[E,S,B]);f.useEffect(()=>()=>{A()},[A]);const[Y,ae]=f.useState(!1);f.useEffect(()=>{if(d)ae(!0);else{const ee=setTimeout(()=>ae(!1),150);return()=>clearTimeout(ee)}},[d]);const Q=f.useMemo(()=>{if(!d&&!Y)return null;let ee;b!==void 0&&(ee=b.results>=1e3?"over 1000":`${b.results} result${b.results!==1?"s":""}`,b.selectedIndex>=0&&(ee=`${b.selectedIndex+1} of ${ee}`));const re=fe=>{fe.stopPropagation()},ue={width:`${Math.floor((b?.rowsSearched??0)/r*100)}%`};return f.createElement(M0,{className:"gdg-search-bar"+(d?"":" out"),onMouseDown:re,onMouseMove:re,onMouseUp:re,onClick:re},f.createElement("div",{className:"gdg-search-bar-inner"},f.createElement("input",{id:h,"aria-hidden":!d,"data-testid":"search-input",ref:o,onChange:T,value:v,tabIndex:d?void 0:-1,onKeyDownCapture:X}),f.createElement("button",{type:"button","aria-label":"Previous Result","aria-hidden":!d,tabIndex:d?void 0:-1,onClick:B,disabled:(b?.results??0)===0},R0),f.createElement("button",{type:"button","aria-label":"Next Result","aria-hidden":!d,tabIndex:d?void 0:-1,onClick:S,disabled:(b?.results??0)===0},I0),g!==void 0&&f.createElement("button",{type:"button","aria-label":"Close Search","aria-hidden":!d,"data-testid":"search-close-button",tabIndex:d?void 0:-1,onClick:E},E0)),b!==void 0?f.createElement(f.Fragment,null,f.createElement("div",{className:"gdg-search-status"},f.createElement("div",{"data-testid":"search-result-area"},ee)),f.createElement("div",{className:"gdg-search-progress",style:ue})):f.createElement("div",{className:"gdg-search-status"},f.createElement("label",{htmlFor:h},"Type to search")))},[d,Y,b,r,h,o,T,v,X,B,S,g,E]);return f.createElement(f.Fragment,null,f.createElement(k0,{prelightCells:k,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,nonGrowWidth:e.nonGrowWidth,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,onColumnProposeMove:e.onColumnProposeMove,drawFocusRing:e.drawFocusRing,drawCell:e.drawCell,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}),Q)};class O0 extends f.PureComponent{wrapperRef=f.createRef();componentDidMount(){const t=this.props.customEventTarget??document;t.addEventListener("pointerdown",this.clickOutside,!0),t.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){const t=this.props.customEventTarget??document;t.removeEventListener("pointerdown",this.clickOutside,!0),t.removeEventListener("contextmenu",this.clickOutside,!0)}clickOutside=t=>{if(!(this.props.isOutsideClick&&!this.props.isOutsideClick(t))&&this.wrapperRef.current!==null&&!this.wrapperRef.current.contains(t.target)){let n=t.target;for(;n!==null;){if(n.classList.contains("click-outside-ignore"))return;n=n.parentElement}this.props.onClickOutside()}};render(){const{onClickOutside:t,isOutsideClick:n,customEventTarget:r,...i}=this.props;return f.createElement("div",{...i,ref:this.wrapperRef},this.props.children)}}const P0=()=>e=>Math.max(16,e.targetHeight-10),_0=un("input")({name:"RenameInput",class:"gdg-r17m35ur",propsAsIs:!1,vars:{"r17m35ur-0":[P0(),"px"]}}),F0=e=>{const{bounds:t,group:n,onClose:r,canvasBounds:i,onFinish:o}=e,[s,a]=Vt.useState(n);return Vt.createElement(O0,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"gdg-c1tqibwd",onClickOutside:r},Vt.createElement(_0,{targetHeight:t.height,"data-testid":"group-rename-input",value:s,onBlur:r,onFocus:l=>l.target.setSelectionRange(0,s.length),onChange:l=>a(l.target.value),onKeyDown:l=>{l.key==="Enter"?o(s):l.key==="Escape"&&r()},autoFocus:!0}))};function L0(e,t){return e===void 0?!1:e.length>1&&e.startsWith("_")?Number.parseInt(e.slice(1))===t.keyCode:e.length===1&&e>="a"&&e<="z"?e.toUpperCase().codePointAt(0)===t.keyCode:e===t.key}function at(e,t,n){const r=Ld(e,t);return r&&(n.didMatch=!0),r}function Ld(e,t){if(e.length===0)return!1;if(e.includes("|")){const l=e.split("|");for(const u of l)if(Ld(u,t))return!0;return!1}let n=!1,r=!1,i=!1,o=!1;const s=e.split("+"),a=s.pop();if(!L0(a,t))return!1;if(s[0]==="any")return!0;for(const l of s)switch(l){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":i=!0;break;case"meta":o=!0;break;case"primary":aa.value?o=!0:n=!0;break}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===o}function A0(e,t,n,r,i,o,s){const a=Vt.useCallback((c,d,g,h)=>{(o==="cell"||o==="multi-cell")&&c!==void 0&&(c={...c,range:{x:c.cell[0],y:c.cell[1],width:1,height:1}}),!s&&c!==void 0&&c.range.width>1&&(c={...c,range:{...c.range,width:1,x:c.cell[0]}});const m=n==="mixed"&&(g||h==="drag"),p=r==="mixed"&&m,v=i==="mixed"&&m;let w={current:c===void 0?void 0:{...c,rangeStack:h==="drag"?e.current?.rangeStack??[]:[]},columns:p?e.columns:ot.empty(),rows:v?e.rows:ot.empty()};g&&(o==="multi-rect"||o==="multi-cell")&&w.current!==void 0&&e.current!==void 0&&(w={...w,current:{...w.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(w,d)},[r,e,n,o,s,i,t]),l=Vt.useCallback((c,d,g)=>{c=c??e.rows,d!==void 0&&(c=c.add(d));let h;if(i==="exclusive"&&c.length>0)h={current:void 0,columns:ot.empty(),rows:c};else{const m=g&&n==="mixed",p=g&&r==="mixed";h={current:m?e.current:void 0,columns:p?e.columns:ot.empty(),rows:c}}t(h,!1)},[r,e,n,i,t]),u=Vt.useCallback((c,d,g)=>{c=c??e.columns,d!==void 0&&(c=c.add(d));let h;if(r==="exclusive"&&c.length>0)h={current:void 0,rows:ot.empty(),columns:c};else{const m=g&&n==="mixed",p=g&&i==="mixed";h={current:m?e.current:void 0,rows:p?e.rows:ot.empty(),columns:c}}t(h,!1)},[r,e,n,i,t]);return[a,l,u]}function H0(e,t,n,r,i){const o=f.useCallback(u=>{if(e===!0){const c=[];for(let d=u.y;d<u.y+u.height;d++){const g=[];for(let h=u.x;h<u.x+u.width;h++)h<0||d>=i?g.push({kind:J.Loading,allowOverlay:!1}):g.push(t([h,d]));c.push(g)}return c}return e?.(u,r.signal)??[]},[r.signal,t,e,i]),s=e!==void 0?o:void 0,a=f.useCallback(u=>{if(s===void 0)return[];const c={...u,x:u.x-n};if(c.x<0){c.x=0,c.width--;const d=s(c,r.signal);return typeof d=="function"?async()=>(await d()).map(g=>[{kind:J.Loading,allowOverlay:!1},...g]):d.map(g=>[{kind:J.Loading,allowOverlay:!1},...g])}return s(c,r.signal)},[r.signal,s,n]);return[e!==void 0?a:void 0,s]}function z0(e){if(e.copyData!==void 0)return{formatted:e.copyData,rawValue:e.copyData,format:"string",doNotEscape:!0};switch(e.kind){case J.Boolean:return{formatted:e.data===!0?"TRUE":e.data===!1?"FALSE":e.data===zs?"INDETERMINATE":"",rawValue:e.data,format:"boolean"};case J.Custom:return{formatted:e.copyData,rawValue:e.copyData,format:"string"};case J.Image:case J.Bubble:return{formatted:e.data,rawValue:e.data,format:"string-array"};case J.Drilldown:return{formatted:e.data.map(t=>t.text),rawValue:e.data.map(t=>t.text),format:"string-array"};case J.Text:return{formatted:e.displayData??e.data,rawValue:e.data,format:"string"};case J.Uri:return{formatted:e.displayData??e.data,rawValue:e.data,format:"url"};case J.Markdown:case J.RowID:return{formatted:e.data,rawValue:e.data,format:"string"};case J.Number:return{formatted:e.displayData,rawValue:e.data,format:"number"};case J.Loading:return{formatted:"#LOADING",rawValue:"",format:"string"};case J.Protected:return{formatted:"************",rawValue:"",format:"string"};default:no()}}function V0(e,t){return e.map((r,i)=>{const o=t[i];return r.map(s=>s.span!==void 0&&s.span[0]!==o?{formatted:"",rawValue:"",format:"string"}:z0(s))})}function Tu(e,t){return(t?/[\t\n",]/:/[\t\n"]/).test(e)&&(e=`"${e.replace(/"/g,'""')}"`),e}function $0(e){const t=[];for(const n of e){const r=[];for(const i of n)i.format==="url"?r.push(i.rawValue?.toString()??""):i.format==="string-array"?r.push(i.formatted.map(o=>Tu(o,!0)).join(",")):r.push(i.doNotEscape===!0?i.formatted:Tu(i.formatted,!1));t.push(r.join("	"))}return t.join(`
`)}function ss(e){return e.replace(/\t/g,"    ").replace(/ {2,}/g,t=>"<span> </span>".repeat(t.length))}function Du(e){return'"'+e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")+'"'}function N0(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}function B0(e){const t=[];t.push('<style type="text/css"><!--br {mso-data-placement:same-cell;}--></style>',"<table><tbody>");for(const n of e){t.push("<tr>");for(const r of n){const i=`gdg-format="${r.format}"`;r.format==="url"?t.push(`<td ${i}><a href="${r.rawValue}">${ss(r.formatted)}</a></td>`):r.format==="string-array"?t.push(`<td ${i}><ol>${r.formatted.map((o,s)=>`<li gdg-raw-value=${Du(r.rawValue[s])}>`+ss(o)+"</li>").join("")}</ol></td>`):t.push(`<td gdg-raw-value=${Du(r.rawValue?.toString()??"")} ${i}>${ss(r.formatted)}</td>`)}t.push("</tr>")}return t.push("</tbody></table>"),t.join("")}function W0(e,t){const n=V0(e,t),r=$0(n),i=B0(n);return{textPlain:r,textHtml:i}}function Ou(e){const t=document.createElement("html");t.innerHTML=e.replace(/&nbsp;/g," ");const n=t.querySelector("table");if(n===null)return;const r=[n],i=[];let o;for(;r.length>0;){const s=r.pop();if(s===void 0)break;if(s instanceof HTMLTableElement||s.nodeName==="TBODY")r.push(...[...s.children].reverse());else if(s instanceof HTMLTableRowElement)o!==void 0&&i.push(o),o=[],r.push(...[...s.children].reverse());else if(s instanceof HTMLTableCellElement){const a=s.cloneNode(!0),u=a.children.length===1&&a.children[0].nodeName==="P"?a.children[0]:null,c=u?.children.length===1&&u.children[0].nodeName==="FONT",d=a.querySelectorAll("br");for(const m of d)m.replaceWith(`
`);const g=a.getAttribute("gdg-raw-value"),h=a.getAttribute("gdg-format")??"string";if(a.querySelector("a")!==null)o?.push({rawValue:a.querySelector("a")?.getAttribute("href")??"",formatted:a.textContent??"",format:h});else if(a.querySelector("ol")!==null){const m=a.querySelectorAll("li");o?.push({rawValue:[...m].map(p=>p.getAttribute("gdg-raw-value")??""),formatted:[...m].map(p=>p.textContent??""),format:"string-array"})}else if(g!==null)o?.push({rawValue:N0(g),formatted:a.textContent??"",format:h});else{let m=a.textContent??"";c&&(m=m.replace(/\n(?!\n)/g,"")),o?.push({rawValue:m??"",formatted:m??"",format:h})}}}return o!==void 0&&i.push(o),i}function U0(e,t,n,r,i){const o=e;if(r==="allowPartial"||e.current===void 0||t===void 0)return e;let s=!1;do{if(e?.current===void 0)break;const a=e.current?.range,l=[];if(a.width>2){const d=t({x:a.x,y:a.y,width:1,height:a.height},i.signal);if(typeof d=="function")return o;l.push(...d);const g=t({x:a.x+a.width-1,y:a.y,width:1,height:a.height},i.signal);if(typeof g=="function")return o;l.push(...g)}else{const d=t({x:a.x,y:a.y,width:a.width,height:a.height},i.signal);if(typeof d=="function")return o;l.push(...d)}let u=a.x-n,c=a.x+a.width-1-n;for(const d of l)for(const g of d)g.span!==void 0&&(u=Math.min(g.span[0],u),c=Math.max(g.span[1],c));u===a.x-n&&c===a.x+a.width-1-n?s=!0:e={current:{cell:e.current.cell??[0,0],range:{x:u+n,y:a.y,width:c-u+1,height:a.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!s);return e}function Pu(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function q0(e){let t;(function(a){a[a.None=0]="None",a[a.inString=1]="inString",a[a.inStringPostQuote=2]="inStringPostQuote"})(t||(t={}));const n=[];let r=[],i=0,o=t.None;e=e.replace(/\r\n/g,`
`);let s=0;for(const a of e){switch(o){case t.None:a==="	"||a===`
`?(r.push(e.slice(i,s)),i=s+1,a===`
`&&(n.push(r),r=[])):a==='"'&&(o=t.inString);break;case t.inString:a==='"'&&(o=t.inStringPostQuote);break;case t.inStringPostQuote:a==='"'?o=t.inString:((a==="	"||a===`
`)&&(r.push(Pu(e.slice(i,s))),i=s+1,a===`
`&&(n.push(r),r=[])),o=t.None);break}s++}return i<e.length&&r.push(Pu(e.slice(i,e.length))),n.push(r),n.map(a=>a.map(l=>({rawValue:l,formatted:l,format:"string"})))}function _u(e,t,n){const r=W0(e,t),i=a=>{window.navigator.clipboard?.writeText(a)},o=(a,l)=>window.navigator.clipboard?.write===void 0?!1:(window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([a],{type:"text/plain"}),"text/html":new Blob([l],{type:"text/html"})})]),!0),s=(a,l)=>{try{if(n===void 0||n.clipboardData===null)throw new Error("No clipboard data");n?.clipboardData?.setData("text/plain",a),n?.clipboardData?.setData("text/html",l)}catch{o(a,l)||i(a)}};window.navigator.clipboard?.write!==void 0||n?.clipboardData!==void 0?s(r.textPlain,r.textHtml):i(r.textPlain),n?.preventDefault()}function Ad(e){return e!==!0}function Fu(e){return typeof e=="string"?e:`${e}px`}const G0=()=>e=>e.innerWidth,X0=()=>e=>e.innerHeight,Y0=un("div")({name:"Wrapper",class:"gdg-wmyidgi",propsAsIs:!1,vars:{"wmyidgi-0":[G0()],"wmyidgi-1":[X0()]}}),j0=e=>{const{inWidth:t,inHeight:n,children:r,...i}=e;return f.createElement(Y0,{innerHeight:Fu(n),innerWidth:Fu(t),...i},r)},K0=2,Z0=1300;function J0(e,t,n){const r=Vt.useRef(0),[i,o]=e??[0,0];Vt.useEffect(()=>{if(i===0&&o===0){r.current=0;return}let s=!1,a=0;const l=u=>{if(!s){if(a===0)a=u;else{const c=u-a;r.current=Math.min(1,r.current+c/Z0);const d=r.current**1.618*c*K0;t.current?.scrollBy(i*d,o*d),a=u,n?.()}window.requestAnimationFrame(l)}};return window.requestAnimationFrame(l),()=>{s=!0}},[t,i,o,n])}function Q0({rowHeight:e,headerHeight:t,groupHeaderHeight:n,theme:r,overscrollX:i,overscrollY:o,scaleToRem:s,remSize:a}){const[l,u,c,d,g,h]=Vt.useMemo(()=>{if(!s||a===16)return[e,t,n,r,i,o];const m=a/16,p=e,v=Cd();return[typeof p=="number"?p*m:w=>Math.ceil(p(w)*m),Math.ceil(t*m),Math.ceil(n*m),{...r,headerIconSize:(r?.headerIconSize??v.headerIconSize)*m,cellHorizontalPadding:(r?.cellHorizontalPadding??v.cellHorizontalPadding)*m,cellVerticalPadding:(r?.cellVerticalPadding??v.cellVerticalPadding)*m},Math.ceil((i??0)*m),Math.ceil((o??0)*m)]},[n,t,i,o,a,e,s,r]);return{rowHeight:l,headerHeight:u,groupHeaderHeight:c,theme:d,overscrollX:g,overscrollY:h}}const yr={downFill:!1,rightFill:!1,clear:!0,closeOverlay:!0,acceptOverlayDown:!0,acceptOverlayUp:!0,acceptOverlayLeft:!0,acceptOverlayRight:!0,copy:!0,paste:!0,cut:!0,search:!1,delete:!0,activateCell:!0,scrollToSelectedCell:!0,goToFirstCell:!0,goToFirstColumn:!0,goToFirstRow:!0,goToLastCell:!0,goToLastColumn:!0,goToLastRow:!0,goToNextPage:!0,goToPreviousPage:!0,selectToFirstCell:!0,selectToFirstColumn:!0,selectToFirstRow:!0,selectToLastCell:!0,selectToLastColumn:!0,selectToLastRow:!0,selectAll:!0,selectRow:!0,selectColumn:!0,goUpCell:!0,goRightCell:!0,goDownCell:!0,goLeftCell:!0,goUpCellRetainSelection:!0,goRightCellRetainSelection:!0,goDownCellRetainSelection:!0,goLeftCellRetainSelection:!0,selectGrowUp:!0,selectGrowRight:!0,selectGrowDown:!0,selectGrowLeft:!0};function st(e,t){return e===!0?t:e===!1?"":e}function Lu(e){const t=aa.value;return{activateCell:st(e.activateCell," |Enter|shift+Enter"),clear:st(e.clear,"any+Escape"),closeOverlay:st(e.closeOverlay,"any+Escape"),acceptOverlayDown:st(e.acceptOverlayDown,"Enter"),acceptOverlayUp:st(e.acceptOverlayUp,"shift+Enter"),acceptOverlayLeft:st(e.acceptOverlayLeft,"shift+Tab"),acceptOverlayRight:st(e.acceptOverlayRight,"Tab"),copy:e.copy,cut:e.cut,delete:st(e.delete,t?"Backspace|Delete":"Delete"),downFill:st(e.downFill,"primary+_68"),scrollToSelectedCell:st(e.scrollToSelectedCell,"primary+Enter"),goDownCell:st(e.goDownCell,"ArrowDown"),goDownCellRetainSelection:st(e.goDownCellRetainSelection,"alt+ArrowDown"),goLeftCell:st(e.goLeftCell,"ArrowLeft|shift+Tab"),goLeftCellRetainSelection:st(e.goLeftCellRetainSelection,"alt+ArrowLeft"),goRightCell:st(e.goRightCell,"ArrowRight|Tab"),goRightCellRetainSelection:st(e.goRightCellRetainSelection,"alt+ArrowRight"),goUpCell:st(e.goUpCell,"ArrowUp"),goUpCellRetainSelection:st(e.goUpCellRetainSelection,"alt+ArrowUp"),goToFirstCell:st(e.goToFirstCell,"primary+Home"),goToFirstColumn:st(e.goToFirstColumn,"Home|primary+ArrowLeft"),goToFirstRow:st(e.goToFirstRow,"primary+ArrowUp"),goToLastCell:st(e.goToLastCell,"primary+End"),goToLastColumn:st(e.goToLastColumn,"End|primary+ArrowRight"),goToLastRow:st(e.goToLastRow,"primary+ArrowDown"),goToNextPage:st(e.goToNextPage,"PageDown"),goToPreviousPage:st(e.goToPreviousPage,"PageUp"),paste:e.paste,rightFill:st(e.rightFill,"primary+_82"),search:st(e.search,"primary+f"),selectAll:st(e.selectAll,"primary+a"),selectColumn:st(e.selectColumn,"ctrl+ "),selectGrowDown:st(e.selectGrowDown,"shift+ArrowDown"),selectGrowLeft:st(e.selectGrowLeft,"shift+ArrowLeft"),selectGrowRight:st(e.selectGrowRight,"shift+ArrowRight"),selectGrowUp:st(e.selectGrowUp,"shift+ArrowUp"),selectRow:st(e.selectRow,"shift+ "),selectToFirstCell:st(e.selectToFirstCell,"primary+shift+Home"),selectToFirstColumn:st(e.selectToFirstColumn,"primary+shift+ArrowLeft"),selectToFirstRow:st(e.selectToFirstRow,"primary+shift+ArrowUp"),selectToLastCell:st(e.selectToLastCell,"primary+shift+End"),selectToLastColumn:st(e.selectToLastColumn,"primary+shift+ArrowRight"),selectToLastRow:st(e.selectToLastRow,"primary+shift+ArrowDown")}}function ev(e){const t=sm(e);return Vt.useMemo(()=>{if(t===void 0)return Lu(yr);const n={...t,goToNextPage:t?.goToNextPage??t?.pageDown??yr.goToNextPage,goToPreviousPage:t?.goToPreviousPage??t?.pageUp??yr.goToPreviousPage,goToFirstCell:t?.goToFirstCell??t?.first??yr.goToFirstCell,goToLastCell:t?.goToLastCell??t?.last??yr.goToLastCell,selectToFirstCell:t?.selectToFirstCell??t?.first??yr.selectToFirstCell,selectToLastCell:t?.selectToLastCell??t?.last??yr.selectToLastCell};return Lu({...yr,...n})},[t])}function tv(e){function t(r,i,o){if(typeof r=="number")return{headerIndex:r,isCollapsed:!1,depth:i,path:o};const s={headerIndex:r.headerIndex,isCollapsed:r.isCollapsed,depth:i,path:o};return r.subGroups!==void 0&&(s.subGroups=r.subGroups.map((a,l)=>t(a,i+1,[...o,l])).sort((a,l)=>a.headerIndex-l.headerIndex)),s}return e.map((r,i)=>t(r,0,[i])).sort((r,i)=>r.headerIndex-i.headerIndex)}function Js(e,t){const n=[];function r(a,l,u=!1){let c=l!==null?l-a.headerIndex:t-a.headerIndex;if(a.subGroups!==void 0&&(c=a.subGroups[0].headerIndex-a.headerIndex),c--,n.push({rowIndex:-1,headerIndex:a.headerIndex,contentIndex:-1,skip:u,isCollapsed:a.isCollapsed,depth:a.depth,path:a.path,rows:c}),a.subGroups)for(let d=0;d<a.subGroups.length;d++){const g=d<a.subGroups.length-1?a.subGroups[d+1].headerIndex:l;r(a.subGroups[d],g,u||a.isCollapsed)}}const i=tv(e.groups);for(let a=0;a<i.length;a++){const l=a<i.length-1?i[a+1].headerIndex:null;r(i[a],l)}let o=0,s=0;for(const a of n)a.contentIndex=s,s+=a.rows,a.rowIndex=o,o+=a.isCollapsed?1:a.rows+1;return n.filter(a=>a.skip===!1).map(a=>{const{skip:l,...u}=a;return u})}function Ts(e,t){if(t===void 0||Js.length===0)return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1};let n=e;for(const r of t){if(n===0)return{path:[...r.path,-1],originalIndex:r.headerIndex,isGroupHeader:!0,groupIndex:-1,contentIndex:-1,groupRows:r.rows};if(r.isCollapsed)n--;else{if(n<=r.rows)return{path:[...r.path,n-1],originalIndex:r.headerIndex+n,isGroupHeader:!1,groupIndex:n-1,contentIndex:r.contentIndex+n-1,groupRows:r.rows};n=n-r.rows-1}}return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1}}function nv(e,t,n,r){const i=Vt.useMemo(()=>e===void 0?void 0:Js(e,t),[e,t]),o=Vt.useMemo(()=>i?.reduce((c,d)=>(c[d.rowIndex]=d,c),{}),[i]),s=Vt.useMemo(()=>i===void 0?t:i.reduce((c,d)=>c+(d.isCollapsed?1:d.rows+1),0),[i,t]),a=Vt.useMemo(()=>e===void 0||typeof n=="number"&&e.height===n?n:c=>o?.[c]?e.height:typeof n=="number"?n:n(c),[o,e,n]),l=Vt.useCallback(c=>{if(i===void 0)return c;let d=c;for(const g of i){if(d===0)return;if(d--,!g.isCollapsed){if(d<g.rows)return g.contentIndex+d;d-=g.rows}}return c},[i]),u=Cr(r??e?.themeOverride,Vt.useCallback(c=>{if(e===void 0)return r?.(c,c,c);if(r===void 0&&e?.themeOverride===void 0)return;const{isGroupHeader:d,contentIndex:g,groupIndex:h}=Ts(c,i);return d?e.themeOverride:r?.(c,h,g)},[i,r,e]));return e===void 0?{rowHeight:a,rows:t,rowNumberMapper:l,getRowThemeOverride:u}:{rowHeight:a,rows:s,rowNumberMapper:l,getRowThemeOverride:u}}function rv(e,t){const n=Vt.useMemo(()=>e===void 0?void 0:Js(e,t),[e,t]);return{getRowGroupingForPath:zd,updateRowGroupingByPath:Hd,mapper:Vt.useCallback(r=>{if(typeof r=="number")return Ts(r,n);const i=Ts(r[1],n);return{...i,originalIndex:[r[0],i.originalIndex]}},[n])}}function Hd(e,t,n){const[r,...i]=t;return i[0]===-1?e.map((o,s)=>s===r?{...o,...n}:o):e.map((o,s)=>s===r?{...o,subGroups:Hd(o.subGroups??[],i,n)}:o)}function zd(e,t){const[n,...r]=t;return r[0]===-1?e[n]:zd(e[n].subGroups??[],r)}function iv(e,t){const[n]=f.useState(()=>({value:e,callback:t,facade:{get current(){return n.value},set current(r){const i=n.value;i!==r&&(n.value=r,n.callback(r,i))}}}));return n.callback=t,n.facade}function ov(e,t,n,r,i){const[o,s]=f.useMemo(()=>[t!==void 0&&typeof n=="number"?Math.floor(t/n):0,t!==void 0&&typeof n=="number"?-(t%n):0],[t,n]),a=f.useMemo(()=>({x:r.current.x,y:o,width:r.current.width??1,height:r.current.height??1,ty:s}),[r,s,o]),[l,u,c]=am(a),d=f.useRef(i);d.current=i;const g=iv(null,p=>{p!==null&&t!==void 0?p.scrollTop=t:p!==null&&e!==void 0&&(p.scrollLeft=e)}),h=(l.height??1)>1;f.useLayoutEffect(()=>{if(t!==void 0&&g.current!==null&&h){if(g.current.scrollTop===t)return;g.current.scrollTop=t,g.current.scrollTop!==t&&c(),d.current()}},[t,h,c,g]);const m=(l.width??1)>1;return f.useLayoutEffect(()=>{if(e!==void 0&&g.current!==null&&m){if(g.current.scrollLeft===e)return;g.current.scrollLeft=e,g.current.scrollLeft!==e&&c(),d.current()}},[e,m,c,g]),{visibleRegion:l,setVisibleRegion:u,scrollRef:g}}const av=f.lazy(async()=>await As(()=>import("./data-grid-overlay-editor.Ct51iCb_.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url));let sv=0;function lv(e){return cp(Cu(Cu(e).filter(t=>t.span!==void 0).map(t=>kr((t.span?.[0]??0)+1,(t.span?.[1]??0)+1))))}function $o(e,t){return e===void 0||t===0||e.columns.length===0&&e.current===void 0?e:{current:e.current===void 0?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map(n=>({...n,x:n.x+t}))},rows:e.rows,columns:e.columns.offset(t)}}const No={kind:J.Loading,allowOverlay:!1},Bo={columns:ot.empty(),rows:ot.empty(),current:void 0},uv=(e,t)=>{const[n,r]=f.useState(Bo),[i,o]=f.useState(),s=f.useRef(null),a=f.useRef(null),[l,u]=f.useState(),c=f.useRef(),d=typeof window>"u"?null:window,{imageEditorOverride:g,getRowThemeOverride:h,markdownDivCreateNode:m,width:p,height:v,columns:w,rows:b,getCellContent:x,onCellClicked:O,onCellActivated:R,onFillPattern:M,onFinishedEditing:_,coercePasteValue:I,drawHeader:k,drawCell:A,editorBloom:D,onHeaderClicked:C,onColumnProposeMove:E,rangeSelectionColumnSpanning:T=!0,spanRangeBehavior:S="default",onGroupHeaderClicked:B,onCellContextMenu:X,className:Y,onHeaderContextMenu:ae,getCellsForSelection:Q,onGroupHeaderContextMenu:ee,onGroupHeaderRenamed:re,onCellEdited:te,onCellsEdited:ue,onSearchResultsChanged:fe,searchResults:se,onSearchValueChange:H,searchValue:P,onKeyDown:G,onKeyUp:ce,keybindings:he,editOnType:Ie=!0,onRowAppended:me,onColumnAppended:Qe,onColumnMoved:xe,validateCell:At,highlightRegions:Xe,rangeSelect:qe="rect",columnSelect:ne="multi",rowSelect:ge="multi",rangeSelectionBlending:Ce="exclusive",columnSelectionBlending:de="exclusive",rowSelectionBlending:Se="exclusive",onDelete:Ee,onDragStart:De,onMouseMove:tt,onPaste:Oe,copyHeaders:Fe=!1,freezeColumns:Ve=0,cellActivationBehavior:Te="second-click",rowSelectionMode:Me="auto",onHeaderMenuClick:pt,onHeaderIndicatorClick:ht,getGroupDetails:Ye,rowGrouping:xt,onSearchClose:Yt,onItemHovered:Ht,onSelectionCleared:Mt,showSearch:cn,onVisibleRegionChanged:Ut,gridSelection:tn,onGridSelectionChange:Tt,minColumnWidth:yt=50,maxColumnWidth:nn=500,maxColumnAutoWidth:ut,provideEditor:rn,trailingRowOptions:Nt,freezeTrailingRows:Pe=0,allowedFillDirections:Dt="orthogonal",scrollOffsetX:Rt,scrollOffsetY:Mn,verticalBorder:qt,onDragOverCell:dn,onDrop:Rn,onColumnResize:Zt,onColumnResizeEnd:Jt,onColumnResizeStart:We,customRenderers:Ot,fillHandle:yn,experimental:vt,fixedShadowX:_t,fixedShadowY:Cn,headerIcons:er,imageWindowLoader:zn,initialSize:$,isDraggable:$e,onDragLeave:Ge,onRowMoved:Ct,overscrollX:mn,overscrollY:sn,preventDiagonalScrolling:be,rightElement:dt,rightElementProps:bt,trapFocus:jt=!1,smoothScrollX:Gt,smoothScrollY:qn,scaleToRem:ho=!1,rowHeight:ka=34,headerHeight:go=36,groupHeaderHeight:Er=go,theme:cr,isOutsideClick:Ma,renderers:wi,resizeIndicator:mo,scrollToActiveCell:yi=!0,drawFocusRing:Tr=!0,portalElementRef:ml}=e,Ci=Tr==="no-editor"?i===void 0:Tr,Pn=typeof e.rowMarkers=="string"?void 0:e.rowMarkers,Sn=Pn?.kind??e.rowMarkers??"none",Si=Pn?.width??e.rowMarkerWidth,tr=Pn?.startIndex??e.rowMarkerStartIndex??1,nr=Pn?.theme??e.rowMarkerTheme,Yr=Pn?.headerTheme,dr=Pn?.headerAlwaysVisible,Dr=ge!=="multi"||Pn?.headerDisabled===!0,jr=Pn?.checkboxStyle??"square",rr=Math.max(yt,20),Kr=Math.max(nn,rr),Ra=Math.max(ut??Kr,rr),po=f.useMemo(()=>typeof window>"u"?{fontSize:"16px"}:window.getComputedStyle(document.documentElement),[]),{rows:Le,rowNumberMapper:xi,rowHeight:vo,getRowThemeOverride:fr}=nv(xt,b,ka,h),Ia=f.useMemo(()=>Number.parseFloat(po.fontSize),[po]),{rowHeight:Gn,headerHeight:ki,groupHeaderHeight:bo,theme:wo,overscrollX:Mi,overscrollY:Ea}=Q0({groupHeaderHeight:Er,headerHeight:go,overscrollX:mn,overscrollY:sn,remSize:Ia,rowHeight:vo,scaleToRem:ho,theme:cr}),Xn=ev(he),hr=Si??(b>1e4?48:b>1e3?44:b>100?36:32),_n=Sn!=="none",U=_n?1:0,Ft=Nt!==void 0,ir=Nt?.sticky===!0,[Ri,yo]=f.useState(!1),Zr=cn??Ri,Ta=f.useCallback(()=>{Yt!==void 0?Yt():yo(!1)},[Yt]),K=f.useMemo(()=>tn===void 0?void 0:$o(tn,U),[tn,U])??n,xn=f.useRef();xn.current===void 0&&(xn.current=new AbortController),f.useEffect(()=>()=>xn?.current.abort(),[]);const[z,ie]=H0(Q,x,U,xn.current,Le),ke=f.useCallback((y,L,F)=>{if(At===void 0)return!0;const W=[y[0]-U,y[1]];return At?.(W,L,F)},[U,At]),Re=f.useRef(tn),oe=f.useCallback((y,L)=>{L&&(y=U0(y,z,U,S,xn.current)),Tt!==void 0?(Re.current=$o(y,-U),Tt(Re.current)):r(y)},[Tt,z,U,S]),ve=Cr(Zt,f.useCallback((y,L,F,W)=>{Zt?.(w[F-U],L,F-U,W)},[Zt,U,w])),je=Cr(Jt,f.useCallback((y,L,F,W)=>{Jt?.(w[F-U],L,F-U,W)},[Jt,U,w])),we=Cr(We,f.useCallback((y,L,F,W)=>{We?.(w[F-U],L,F-U,W)},[We,U,w])),Ke=Cr(k,f.useCallback((y,L)=>k?.({...y,columnIndex:y.columnIndex-U},L)??!1,[k,U])),ct=Cr(A,f.useCallback((y,L)=>A?.({...y,col:y.col-U},L)??!1,[A,U])),Qt=f.useCallback(y=>{if(Ee!==void 0){const L=Ee($o(y,-U));return typeof L=="boolean"?L:$o(L,U)}return!0},[Ee,U]),[ft,It,nt]=A0(K,oe,Ce,de,Se,qe,T),et=f.useMemo(()=>ar(Cd(),wo),[wo]),[zt,Bt]=f.useState([0,0,0]),Or=f.useMemo(()=>{if(wi===void 0)return{};const y={};for(const L of wi)y[L.kind]=L;return y},[wi]),en=f.useCallback(y=>y.kind!==J.Custom?Or[y.kind]:Ot?.find(L=>L.isMatch(y)),[Ot,Or]);let{sizedColumns:on,nonGrowWidth:gr}=tp(w,Le,ie,zt[0]-(U===0?0:hr)-zt[2],rr,Ra,et,en,xn.current);Sn!=="none"&&(gr+=hr);const Pt=f.useMemo(()=>on.some(y=>y.group!==void 0),[on]),kn=Pt?ki+bo:ki,In=K.rows.length,mr=Sn==="none"?void 0:In===0?!1:In===Le?!0:void 0,rt=f.useMemo(()=>Sn==="none"?on:[{title:"",width:hr,icon:void 0,hasMenu:!1,style:"normal",themeOverride:nr,rowMarker:jr,rowMarkerChecked:mr,headerRowMarkerTheme:Yr,headerRowMarkerAlwaysVisible:dr,headerRowMarkerDisabled:Dr},...on],[Sn,on,hr,nr,jr,mr,Yr,dr,Dr]),ln=f.useRef({height:1,width:1,x:0,y:0}),Yn=f.useRef(!1),{setVisibleRegion:pl,visibleRegion:Pr,scrollRef:Wt}=ov(Rt,Mn,Gn,ln,()=>Yn.current=!0);ln.current=Pr;const Lf=Pr.x+U,Co=Pr.y,fn=f.useRef(null),pn=f.useCallback(y=>{y===!0?fn.current?.focus():window.requestAnimationFrame(()=>{fn.current?.focus()})},[]),hn=Ft?Le+1:Le,Fn=f.useCallback(y=>{const L=U===0?y:y.map(W=>({...W,location:[W.location[0]-U,W.location[1]]})),F=ue?.(L);if(F!==!0)for(const W of L)te?.(W.location,W.value);return F},[te,ue,U]),[_r,Da]=f.useState(),So=K.current!==void 0&&K.current.range.width*K.current.range.height>1?K.current.range:void 0,vl=Ci?K.current?.cell:void 0,xo=vl?.[0],ko=vl?.[1],Af=f.useMemo(()=>{if((Xe===void 0||Xe.length===0)&&(So??xo??ko??_r)===void 0)return;const y=[];if(Xe!==void 0)for(const L of Xe){const F=rt.length-L.range.x-U;F>0&&y.push({color:L.color,range:{...L.range,x:L.range.x+U,width:Math.min(F,L.range.width)},style:L.style})}return _r!==void 0&&y.push({color:Br(et.accentColor,0),range:_r,style:"dashed"}),So!==void 0&&y.push({color:Br(et.accentColor,.5),range:So,style:"solid-outline"}),xo!==void 0&&ko!==void 0&&y.push({color:et.accentColor,range:{x:xo,y:ko,width:1,height:1},style:"solid-outline"}),y.length>0?y:void 0},[_r,So,xo,ko,Xe,rt.length,et.accentColor,U]),bl=f.useRef(rt);bl.current=rt;const En=f.useCallback(([y,L],F=!1)=>{const W=Ft&&L===hn-1;if(y===0&&_n){if(W)return No;const j=xi(L);return j===void 0?No:{kind:Hn.Marker,allowOverlay:!1,checkboxStyle:jr,checked:K?.rows.hasIndex(L)===!0,markerKind:Sn==="clickable-number"?"number":Sn,row:tr+j,drawHandle:Ct!==void 0,cursor:Sn==="clickable-number"?"pointer":void 0}}else if(W){const V=y===U?Nt?.hint??"":"",N=bl.current[y];if(N?.trailingRowOptions?.disabled===!0)return No;{const Z=N?.trailingRowOptions?.hint??V,le=N?.trailingRowOptions?.addIcon??Nt?.addIcon;return{kind:Hn.NewRow,hint:Z,allowOverlay:!1,icon:le}}}else{const j=y-U;if(F||vt?.strict===!0){const N=ln.current,Z=N.x>j||j>N.x+N.width||N.y>L||L>N.y+N.height||L>=Fa.current,le=j===N.extras?.selected?.[0]&&L===N.extras?.selected[1];let pe=!1;if(N.extras?.freezeRegions!==void 0){for(const St of N.extras.freezeRegions)if(Nr(St,j,L)){pe=!0;break}}if(Z&&!le&&!pe)return No}let V=x([j,L]);return U!==0&&V.span!==void 0&&(V={...V,span:[V.span[0]+U,V.span[1]+U]}),V}},[Ft,hn,_n,xi,jr,K?.rows,Sn,tr,Ct,U,Nt?.hint,Nt?.addIcon,vt?.strict,x]),Oa=f.useCallback(y=>{let L=Ye?.(y)??{name:y};return re!==void 0&&y!==""&&(L={icon:L.icon,name:L.name,overrideTheme:L.overrideTheme,actions:[...L.actions??[],{title:"Rename",icon:"renameIcon",onClick:F=>Aa({group:L.name,bounds:F.bounds})}]}),L},[Ye,re]),Mo=f.useCallback(y=>{const[L,F]=y.cell,W=rt[L],q=W?.group!==void 0?Oa(W.group)?.overrideTheme:void 0,j=W?.themeOverride,V=fr?.(F);o({...y,theme:ar(et,q,j,V,y.content.themeOverride)})},[fr,rt,Oa,et]),Jr=f.useCallback((y,L,F)=>{if(K.current===void 0)return;const[W,q]=K.current.cell,j=En([W,q]);if(j.kind!==J.Boolean&&j.allowOverlay){let V=j;if(F!==void 0)switch(V.kind){case J.Number:{const N=Fg(()=>F==="-"?-0:Number.parseFloat(F),0);V={...V,data:Number.isNaN(N)?0:N};break}case J.Text:case J.Markdown:case J.Uri:V={...V,data:F};break}Mo({target:y,content:V,initialValue:F,cell:[W,q],highlight:F===void 0,forceEditMode:F!==void 0})}else j.kind===J.Boolean&&L&&j.readonly!==!0&&(Fn([{location:K.current.cell,value:{...j,data:Ad(j.data)}}]),fn.current?.damage([{cell:K.current.cell}]))},[En,K,Fn,Mo]),wl=f.useCallback((y,L)=>{const F=fn.current?.getBounds(y,L);if(F===void 0||Wt.current===null)return;const W=En([y,L]);W.allowOverlay&&Mo({target:F,content:W,initialValue:void 0,highlight:!0,cell:[y,L],forceEditMode:!0})},[En,Wt,Mo]),Xt=f.useCallback((y,L,F="both",W=0,q=0,j=void 0)=>{if(Wt.current!==null){const V=fn.current,N=a.current,Z=typeof y!="number"?y.unit==="cell"?y.amount:void 0:y,le=typeof L!="number"?L.unit==="cell"?L.amount:void 0:L,pe=typeof y!="number"&&y.unit==="px"?y.amount:void 0,St=typeof L!="number"&&L.unit==="px"?L.amount:void 0;if(V!==null&&N!==null){let it={x:0,y:0,width:0,height:0},Ze=0,Je=0;if((Z!==void 0||le!==void 0)&&(it=V.getBounds((Z??0)+U,le??0)??it,it.width===0||it.height===0))return;const gt=N.getBoundingClientRect(),kt=gt.width/N.offsetWidth;if(pe!==void 0&&(it={...it,x:pe-gt.left-Wt.current.scrollLeft,width:1}),St!==void 0&&(it={...it,y:St+gt.top-Wt.current.scrollTop,height:1}),it!==void 0){const wt={x:it.x-W,y:it.y-q,width:it.width+2*W,height:it.height+2*q};let ii=0;for(let Wa=0;Wa<Ve;Wa++)ii+=on[Wa].width;let Fi=0;const Li=Pe+(ir?1:0);Li>0&&(Fi=Gr(hn,Li,Gn));let Kn=ii*kt+gt.left+U*hr*kt,oi=gt.right,Hr=gt.top+kn*kt,ai=gt.bottom-Fi*kt;const Po=it.width+W*2;switch(j?.hAlign){case"start":oi=Kn+Po;break;case"end":Kn=oi-Po;break;case"center":Kn=Math.floor((Kn+oi)/2)-Po/2,oi=Kn+Po;break}const _o=it.height+q*2;switch(j?.vAlign){case"start":ai=Hr+_o;break;case"end":Hr=ai-_o;break;case"center":Hr=Math.floor((Hr+ai)/2)-_o/2,ai=Hr+_o;break}Kn>wt.x?Ze=wt.x-Kn:oi<wt.x+wt.width&&(Ze=wt.x+wt.width-oi),Hr>wt.y?Je=wt.y-Hr:ai<wt.y+wt.height&&(Je=wt.y+wt.height-ai),F==="vertical"||typeof y=="number"&&y<Ve?Ze=0:(F==="horizontal"||typeof L=="number"&&L>=hn-Li)&&(Je=0),(Ze!==0||Je!==0)&&(kt!==1&&(Ze/=kt,Je/=kt),Wt.current.scrollTo({left:Ze+Wt.current.scrollLeft,top:Je+Wt.current.scrollTop,behavior:j?.behavior??"auto"}))}}}},[U,Pe,hr,Wt,kn,Ve,on,hn,ir,Gn]),Pa=f.useRef(wl),_a=f.useRef(x);Pa.current=wl,_a.current=x;const Fa=f.useRef(Le);Fa.current=Le;const yl=f.useRef(rt.length);yl.current=rt.length;const pr=f.useCallback(async(y,L=!0,F)=>{if(rt[y]?.trailingRowOptions?.disabled===!0)return;const q=me?.();let j,V=!0;q!==void 0&&(j=await q,j==="top"&&(V=!1),typeof j=="number"&&(V=!1));let N=0;const Z=()=>{if(Fa.current<=Le){N<500&&window.setTimeout(Z,N),N=50+N*2;return}const le=typeof j=="number"?j:V?Le:0;Oo.current(y-U,le,"both",0,0,F?{behavior:F}:void 0),ft({cell:[y,le],range:{x:y,y:le,width:1,height:1}},!1,!1,"edit");const pe=_a.current([y-U,le]);pe.allowOverlay&&di(pe)&&pe.readonly!==!0&&L&&window.setTimeout(()=>{Pa.current(y,le)},0)};Z()},[rt,me,U,Le,ft]),Ro=f.useCallback(async(y,L=!0)=>{const F=Qe?.();let W,q=!0;F!==void 0&&(W=await F,W==="left"&&(q=!1),typeof W=="number"&&(q=!1));let j=0;const V=()=>{if(yl.current<=rt.length){j<500&&window.setTimeout(V,j),j=50+j*2;return}const N=typeof W=="number"?W:q?rt.length:0;Xt(N-U,y),ft({cell:[N,y],range:{x:N,y,width:1,height:1}},!1,!1,"edit");const Z=_a.current([N-U,y]);Z.allowOverlay&&di(Z)&&Z.readonly!==!0&&L&&window.setTimeout(()=>{Pa.current(N,y)},0)};V()},[rt,Qe,U,Xt,ft]),Qr=f.useCallback(y=>{const L=on[y]?.trailingRowOptions?.targetColumn??Nt?.targetColumn;if(typeof L=="number")return L+(_n?1:0);if(typeof L=="object"){const F=w.indexOf(L);if(F>=0)return F+(_n?1:0)}},[on,w,_n,Nt?.targetColumn]),vr=f.useRef(),ei=f.useRef(),Ei=f.useCallback((y,L)=>{const[F,W]=L;return ar(et,rt[F]?.themeOverride,fr?.(W),y.themeOverride)},[fr,rt,et]),{mapper:Fr}=rv(xt,b),Vn=xt?.navigationBehavior,Ti=f.useCallback(y=>{const L=aa.value?y.metaKey:y.ctrlKey,F=L&&ge==="multi",W=L&&ne==="multi",[q,j]=y.location,V=K.columns,N=K.rows,[Z,le]=K.current?.cell??[];if(y.kind==="cell"){if(ei.current=void 0,Lr.current=[q,j],q===0&&_n){if(Ft===!0&&j===Le||Sn==="number"||ge==="none")return;const pe=En(y.location);if(pe.kind!==Hn.Marker)return;if(Ct!==void 0){const Ze=en(pe);Dn(Ze?.kind===Hn.Marker);const Je=Ze?.onClick?.({...y,cell:pe,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,theme:Ei(pe,y.location),preventDefault:()=>{}});if(Je===void 0||Je.checked===pe.checked)return}o(void 0),pn();const St=N.hasIndex(j),it=vr.current;if(ge==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&it!==void 0&&N.hasIndex(it)){const Ze=[Math.min(it,j),Math.max(it,j)+1];F||Me==="multi"?It(void 0,Ze,!0):It(ot.fromSingleSelection(Ze),void 0,F)}else ge==="multi"&&(F||y.isTouch||Me==="multi")?St?It(N.remove(j),void 0,!0):(It(void 0,j,!0),vr.current=j):St&&N.length===1?It(ot.empty(),void 0,L):(It(ot.fromSingleSelection(j),void 0,L),vr.current=j)}else if(q>=U&&Ft&&j===Le){const pe=Qr(q);pr(pe??q)}else if(Z!==q||le!==j){const pe=En(y.location),St=en(pe);if(St?.onSelect!==void 0){let Je=!1;if(St.onSelect({...y,cell:pe,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,preventDefault:()=>Je=!0,theme:Ei(pe,y.location)}),Je)return}if(Vn==="block"&&Fr(j).isGroupHeader)return;const it=ir&&j===Le,Ze=ir&&K!==void 0&&K.current?.cell[1]===Le;if((y.shiftKey||y.isLongTouch===!0)&&Z!==void 0&&le!==void 0&&K.current!==void 0&&!Ze){if(it)return;const Je=Math.min(q,Z),gt=Math.max(q,Z),kt=Math.min(j,le),wt=Math.max(j,le);ft({...K.current,range:{x:Je,y:kt,width:gt-Je+1,height:wt-kt+1}},!0,L,"click"),vr.current=void 0,pn()}else ft({cell:[q,j],range:{x:q,y:j,width:1,height:1}},!0,L,"click"),vr.current=void 0,o(void 0),pn()}}else if(y.kind==="header")if(Lr.current=[q,j],o(void 0),_n&&q===0)vr.current=void 0,ei.current=void 0,!Dr&&ge==="multi"&&(N.length!==Le?It(ot.fromSingleSelection([0,Le]),void 0,L):It(ot.empty(),void 0,L),pn());else{const pe=ei.current;if(ne==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&pe!==void 0&&V.hasIndex(pe)){const St=[Math.min(pe,q),Math.max(pe,q)+1];W?nt(void 0,St,L):nt(ot.fromSingleSelection(St),void 0,L)}else W?(V.hasIndex(q)?nt(V.remove(q),void 0,L):nt(void 0,q,L),ei.current=q):ne!=="none"&&(V.hasIndex(q)?nt(V.remove(q),void 0,L):nt(ot.fromSingleSelection(q),void 0,L),ei.current=q);vr.current=void 0,pn()}else y.kind===An?Lr.current=[q,j]:y.kind===sa&&!y.isMaybeScrollbar&&(oe(Bo,!1),o(void 0),pn(),Mt?.(),vr.current=void 0,ei.current=void 0)},[ge,ne,K,_n,U,Ft,Le,Sn,En,Ct,pn,Me,en,Ei,It,Qr,pr,Vn,Fr,ir,ft,nt,oe,Mt]),Di=f.useRef(!1),Lr=f.useRef(),Cl=f.useRef(Pr),jn=f.useRef(),Hf=f.useCallback(y=>{if(ti.current=!1,Cl.current=ln.current,y.button!==0&&y.button!==1){jn.current=void 0;return}const L=performance.now();jn.current={button:y.button,time:L,location:y.location},y?.kind==="header"&&(Di.current=!0);const F=y.kind==="cell"&&y.isFillHandle;!F&&y.kind!=="cell"&&y.isEdge||(u({previousSelection:K,fillHandle:F}),Lr.current=void 0,!y.isTouch&&y.button===0&&!F?Ti(y):!y.isTouch&&y.button===1&&(Lr.current=y.location))},[K,Ti]),[La,Aa]=f.useState(),Sl=f.useCallback(y=>{if(y.kind!==An||ne!=="multi")return;const L=aa.value?y.metaKey:y.ctrlKey,[F]=y.location,W=K.columns;if(F<U)return;const q=rt[F];let j=F,V=F;for(let N=F-1;N>=U&&ro(q.group,rt[N].group);N--)j--;for(let N=F+1;N<rt.length&&ro(q.group,rt[N].group);N++)V++;if(pn(),L)if(W.hasAll([j,V+1])){let N=W;for(let Z=j;Z<=V;Z++)N=N.remove(Z);nt(N,void 0,L)}else nt(void 0,[j,V+1],L);else nt(ot.fromSingleSelection([j,V+1]),void 0,L)},[ne,pn,K.columns,rt,U,nt]),ti=f.useRef(!1),Io=f.useCallback(async y=>{if(z!==void 0&&ve!==void 0){const L=ln.current.y,F=ln.current.height;let W=z({x:y,y:L,width:1,height:Math.min(F,Le-L)},xn.current.signal);typeof W!="object"&&(W=await W());const q=on[y-U],V=document.createElement("canvas").getContext("2d",{alpha:!1});if(V!==null){V.font=et.baseFontFull;const N=xd(V,et,q,0,W,rr,Kr,!1,en);ve?.(q,N.width,y,N.width)}}},[on,z,Kr,et,rr,ve,U,Le,en]),[zf,Ha]=f.useState(),ni=f.useCallback(async(y,L)=>{const F=y.current?.range;if(F===void 0||z===void 0||L.current===void 0)return;const W=L.current.range;if(M!==void 0){let N=!1;if(M({fillDestination:{...W,x:W.x-U},patternSource:{...F,x:F.x-U},preventDefault:()=>N=!0}),N)return}let q=z(F,xn.current.signal);typeof q!="object"&&(q=await q());const j=q,V=[];for(let N=0;N<W.width;N++)for(let Z=0;Z<W.height;Z++){const le=[W.x+N,W.y+Z];if(fd(le,F))continue;const pe=j[Z%F.height][N%F.width];hi(pe)||!di(pe)||V.push({location:le,value:{...pe}})}Fn(V),fn.current?.damage(V.map(N=>({cell:N.location})))},[z,Fn,M,U]),xl=f.useCallback(()=>{if(K.current===void 0||K.current.range.width<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,width:1}}};ni(y,K)},[ni,K]),kl=f.useCallback(()=>{if(K.current===void 0||K.current.range.height<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,height:1}}};ni(y,K)},[ni,K]),Vf=f.useCallback((y,L)=>{const F=l;if(u(void 0),Da(void 0),Ha(void 0),Di.current=!1,L)return;if(F?.fillHandle===!0&&K.current!==void 0&&F.previousSelection?.current!==void 0){if(_r===void 0)return;const pe={...K,current:{...K.current,range:Ed(F.previousSelection.current.range,_r)}};ni(F.previousSelection,pe),oe(pe,!0);return}const[W,q]=y.location,[j,V]=Lr.current??[],N=()=>{ti.current=!0},Z=pe=>{const St=pe.isTouch||j===W&&V===q;if(St&&O?.([W-U,q],{...pe,preventDefault:N}),pe.button===1)return!ti.current;if(!ti.current){const it=En(y.location),Ze=en(it);if(Ze!==void 0&&Ze.onClick!==void 0&&St){const gt=Ze.onClick({...pe,cell:it,posX:pe.localEventX,posY:pe.localEventY,bounds:pe.bounds,theme:Ei(it,y.location),preventDefault:N});gt!==void 0&&!hi(gt)&&ci(gt)&&(Fn([{location:pe.location,value:gt}]),fn.current?.damage([{cell:pe.location}]))}if(ti.current||K.current===void 0)return!1;let Je=!1;switch(it.activationBehaviorOverride??Te){case"double-click":case"second-click":{if(F?.previousSelection?.current?.cell===void 0)break;const[gt,kt]=K.current.cell,[wt,ii]=F.previousSelection.current.cell;Je=W===gt&&W===wt&&q===kt&&q===ii&&(pe.isDoubleClick===!0||Te==="second-click");break}case"single-click":{Je=!0;break}}if(Je)return R?.([W-U,q]),Jr(pe.bounds,!1),!0}return!1},le=y.location[0]-U;if(y.isTouch){const pe=ln.current,St=Cl.current;if(pe.x!==St.x||pe.y!==St.y)return;if(y.isLongTouch===!0){if(y.kind==="cell"&&Ji(K.current?.cell,y.location)){X?.([le,y.location[1]],{...y,preventDefault:N});return}else if(y.kind==="header"&&K.columns.hasIndex(W)){ae?.(le,{...y,preventDefault:N});return}else if(y.kind===An){if(le<0)return;ee?.(le,{...y,preventDefault:N});return}}y.kind==="cell"?Z(y)||Ti(y):y.kind===An?B?.(le,{...y,preventDefault:N}):(y.kind===Sr&&C?.(le,{...y,preventDefault:N}),Ti(y));return}if(y.kind==="header"){if(le<0)return;y.isEdge?y.isDoubleClick===!0&&Io(W):y.button===0&&W===j&&q===V&&C?.(le,{...y,preventDefault:N})}if(y.kind===An){if(le<0)return;y.button===0&&W===j&&q===V&&(B?.(le,{...y,preventDefault:N}),ti.current||Sl(y))}y.kind==="cell"&&(y.button===0||y.button===1)&&Z(y),Lr.current=void 0},[l,K,U,_r,ni,oe,O,En,en,Te,Ei,Fn,R,Jr,X,ae,ee,Ti,B,C,Io,Sl]),$f=f.useCallback(y=>{const L={...y,location:[y.location[0]-U,y.location[1]]};tt?.(L),l!==void 0&&y.buttons===0&&(u(void 0),Da(void 0),Ha(void 0),Di.current=!1),Ha(F=>Di.current?[y.scrollEdge[0],0]:y.scrollEdge[0]===F?.[0]&&y.scrollEdge[1]===F[1]?F:l===void 0||(jn.current?.location[0]??0)<U?void 0:y.scrollEdge)},[l,tt,U]),Nf=f.useCallback((y,L)=>{pt?.(y-U,L)},[pt,U]),Bf=f.useCallback((y,L)=>{ht?.(y-U,L)},[ht,U]),ri=K?.current?.cell,Wf=f.useCallback((y,L,F,W,q,j)=>{Yn.current=!1;let V=ri;V!==void 0&&(V=[V[0]-U,V[1]]);const N=Ve===0?void 0:{x:0,y:y.y,width:Ve,height:y.height},Z=[];N!==void 0&&Z.push(N),Pe>0&&(Z.push({x:y.x-U,y:Le-Pe,width:y.width,height:Pe}),Ve>0&&Z.push({x:0,y:Le-Pe,width:Ve,height:Pe}));const le={x:y.x-U,y:y.y,width:y.width,height:Ft&&y.y+y.height>=Le?y.height-1:y.height,tx:q,ty:j,extras:{selected:V,freezeRegion:N,freezeRegions:Z}};ln.current=le,pl(le),Bt([L,F,W]),Ut?.(le,le.tx,le.ty,le.extras)},[ri,U,Ft,Le,Ve,Pe,pl,Ut]),Uf=Cr(E,f.useCallback((y,L)=>E?.(y-U,L-U)!==!1,[E,U])),qf=Cr(xe,f.useCallback((y,L)=>{xe?.(y-U,L-U),ne!=="none"&&nt(ot.fromSingleSelection(L),void 0,!0)},[ne,xe,U,nt])),za=f.useRef(!1),Gf=f.useCallback(y=>{if(y.location[0]===0&&U>0){y.preventDefault();return}De?.({...y,location:[y.location[0]-U,y.location[1]]}),y.defaultPrevented()||(za.current=!0),u(void 0)},[De,U]),Xf=f.useCallback(()=>{za.current=!1},[]),Ml=xt?.selectionBehavior,Eo=f.useCallback(y=>{if(Ml!=="block-spanning")return;const{isGroupHeader:L,path:F,groupRows:W}=Fr(y);if(L)return[y,y];const q=F[F.length-1],j=y-q,V=y+W-q-1;return[j,V]},[Fr,Ml]),Va=f.useRef(),$a=f.useCallback(y=>{if(!Fd(y,Va.current)&&(Va.current=y,!(jn?.current?.button!==void 0&&jn.current.button>=1))){if(y.buttons!==0&&l!==void 0&&jn.current?.location[0]===0&&y.location[0]===0&&U===1&&ge==="multi"&&l.previousSelection&&!l.previousSelection.rows.hasIndex(jn.current.location[1])&&K.rows.hasIndex(jn.current.location[1])){const L=Math.min(jn.current.location[1],y.location[1]),F=Math.max(jn.current.location[1],y.location[1])+1;It(ot.fromSingleSelection([L,F]),void 0,!1)}if(y.buttons!==0&&l!==void 0&&K.current!==void 0&&!za.current&&!Di.current&&(qe==="rect"||qe==="multi-rect")){const[L,F]=K.current.cell;let[W,q]=y.location;if(q<0&&(q=ln.current.y),l.fillHandle===!0&&l.previousSelection?.current!==void 0){const j=l.previousSelection.current.range;q=Math.min(q,Ft?Le-1:Le);const V=Wp(j,W,q,Dt);Da(V)}else{if(Ft&&F===Le)return;if(Ft&&q===Le)if(y.kind===sa)q--;else return;W=Math.max(W,U);const N=Eo(F);q=N===void 0?q:Ln(q,N[0],N[1]);const Z=W-L,le=q-F,pe={x:Z>=0?L:W,y:le>=0?F:q,width:Math.abs(Z)+1,height:Math.abs(le)+1};ft({...K.current,range:pe},!0,!1,"drag")}}Ht?.({...y,location:[y.location[0]-U,y.location[1]]})}},[l,U,ge,K,qe,Ht,It,Ft,Le,Dt,Eo,ft]),Yf=f.useCallback(()=>{const y=Va.current;if(y===void 0)return;const[L,F]=y.scrollEdge;let[W,q]=y.location;const j=ln.current;L===-1?W=j.extras?.freezeRegion?.x??j.x:L===1&&(W=j.x+j.width),F===-1?q=Math.max(0,j.y):F===1&&(q=Math.min(Le-1,j.y+j.height)),W=Ln(W,0,rt.length-1),q=Ln(q,0,Le-1),$a({...y,location:[W,q]})},[rt.length,$a,Le]);J0(zf,Wt,Yf);const $n=f.useCallback(y=>{if(K.current===void 0)return;const[L,F]=y,[W,q]=K.current.cell,j=K.current.range;let V=j.x,N=j.x+j.width,Z=j.y,le=j.y+j.height;const[pe,St]=Eo(q)??[0,Le-1],it=St+1;if(F!==0)switch(F){case 2:{le=it,Z=q,Xt(0,le,"vertical");break}case-2:{Z=pe,le=q+1,Xt(0,Z,"vertical");break}case 1:{Z<q?(Z++,Xt(0,Z,"vertical")):(le=Math.min(it,le+1),Xt(0,le,"vertical"));break}case-1:{le>q+1?(le--,Xt(0,le,"vertical")):(Z=Math.max(pe,Z-1),Xt(0,Z,"vertical"));break}default:no()}if(L!==0)if(L===2)N=rt.length,V=W,Xt(N-1-U,0,"horizontal");else if(L===-2)V=U,N=W+1,Xt(V-U,0,"horizontal");else{let Ze=[];if(z!==void 0){const Je=z({x:V,y:Z,width:N-V-U,height:le-Z},xn.current.signal);typeof Je=="object"&&(Ze=lv(Je))}if(L===1){let Je=!1;if(V<W){if(Ze.length>0){const gt=kr(V+1,W+1).find(kt=>!Ze.includes(kt-U));gt!==void 0&&(V=gt,Je=!0)}else V++,Je=!0;Je&&Xt(V,0,"horizontal")}Je||(N=Math.min(rt.length,N+1),Xt(N-1-U,0,"horizontal"))}else if(L===-1){let Je=!1;if(N>W+1){if(Ze.length>0){const gt=kr(N-1,W,-1).find(kt=>!Ze.includes(kt-U));gt!==void 0&&(N=gt,Je=!0)}else N--,Je=!0;Je&&Xt(N-U,0,"horizontal")}Je||(V=Math.max(U,V-1),Xt(V-U,0,"horizontal"))}else no()}ft({cell:K.current.cell,range:{x:V,y:Z,width:N-V,height:le-Z}},!0,!1,"keyboard-select")},[z,Eo,K,rt.length,U,Le,Xt,ft]),Na=f.useRef(yi);Na.current=yi;const br=f.useCallback((y,L,F,W)=>{const q=hn-(F?0:1);y=Ln(y,U,on.length-1+U),L=Ln(L,0,q);const j=ri?.[0],V=ri?.[1];if(y===j&&L===V)return!1;if(W&&K.current!==void 0){const N=[...K.current.rangeStack];(K.current.range.width>1||K.current.range.height>1)&&N.push(K.current.range),oe({...K,current:{cell:[y,L],range:{x:y,y:L,width:1,height:1},rangeStack:N}},!0)}else ft({cell:[y,L],range:{x:y,y:L,width:1,height:1}},!0,!1,"keyboard-nav");return c.current!==void 0&&c.current[0]===y&&c.current[1]===L&&(c.current=void 0),Na.current&&Xt(y-U,L),!0},[hn,U,on.length,ri,K,Xt,oe,ft]),jf=f.useCallback((y,L)=>{i?.cell!==void 0&&y!==void 0&&ci(y)&&(Fn([{location:i.cell,value:y}]),window.requestAnimationFrame(()=>{fn.current?.damage([{cell:i.cell}])})),pn(!0),o(void 0);const[F,W]=L;if(K.current!==void 0&&(F!==0||W!==0)){const q=K.current.cell[1]===hn-1&&y!==void 0,j=K.current.cell[0]===rt.length-1&&y!==void 0;let V=!0;if(q&&W===1&&me!==void 0){V=!1;const N=K.current.cell[0]+F,Z=Qr(N);pr(Z??N,!1)}if(j&&F===1&&Qe!==void 0){V=!1;const N=K.current.cell[1]+W;Ro(N,!1)}V&&br(Ln(K.current.cell[0]+F,0,rt.length-1),Ln(K.current.cell[1]+W,0,hn-1),q,!1)}_?.(y,L)},[i?.cell,pn,K,_,Fn,hn,br,rt.length,pr,Ro,me,Qe,Qr]),Kf=f.useMemo(()=>`gdg-overlay-${sv++}`,[]),Ar=f.useCallback(y=>{pn();const L=[];for(let F=y.x;F<y.x+y.width;F++)for(let W=y.y;W<y.y+y.height;W++){const q=x([F-U,W]);if(!q.allowOverlay&&q.kind!==J.Boolean)continue;let j;if(q.kind===J.Custom){const V=en(q),N=V?.provideEditor?.({...q,location:[F-U,W]});V?.onDelete!==void 0?j=V.onDelete(q):Lg(N)&&(j=N?.deletedValue?.(q))}else(ci(q)&&q.allowOverlay||q.kind===J.Boolean)&&(j=en(q)?.onDelete?.(q));j!==void 0&&!hi(j)&&ci(j)&&L.push({location:[F,W],value:j})}Fn(L),fn.current?.damage(L.map(F=>({cell:F.location})))},[pn,x,en,Fn,U]),Oi=i!==void 0,Rl=f.useCallback(y=>{const L=()=>{y.stopPropagation(),y.preventDefault()},F={didMatch:!1},{bounds:W}=y,q=K.columns,j=K.rows,V=Xn;if(!Oi&&at(V.clear,y,F))oe(Bo,!1),Mt?.();else if(!Oi&&at(V.selectAll,y,F))oe({columns:ot.empty(),rows:ot.empty(),current:{cell:K.current?.cell??[U,0],range:{x:U,y:0,width:w.length,height:Le},rangeStack:[]}},!1);else if(at(V.search,y,F))s?.current?.focus({preventScroll:!0}),yo(!0);else if(at(V.delete,y,F)){const gt=Qt?.(K)??!0;if(gt!==!1){const kt=gt===!0?K:gt;if(kt.current!==void 0){Ar(kt.current.range);for(const wt of kt.current.rangeStack)Ar(wt)}for(const wt of kt.rows)Ar({x:U,y:wt,width:w.length,height:1});for(const wt of kt.columns)Ar({x:wt,y:0,width:1,height:Le})}}if(F.didMatch)return L(),!0;if(K.current===void 0)return!1;let[N,Z]=K.current.cell;const[,le]=K.current.cell;let pe=!1,St=!1;if(at(V.scrollToSelectedCell,y,F)?Oo.current(N-U,Z):ne!=="none"&&at(V.selectColumn,y,F)?q.hasIndex(N)?nt(q.remove(N),void 0,!0):ne==="single"?nt(ot.fromSingleSelection(N),void 0,!0):nt(void 0,N,!0):ge!=="none"&&at(V.selectRow,y,F)?j.hasIndex(Z)?It(j.remove(Z),void 0,!0):ge==="single"?It(ot.fromSingleSelection(Z),void 0,!0):It(void 0,Z,!0):!Oi&&W!==void 0&&at(V.activateCell,y,F)?Z===Le&&Ft?window.setTimeout(()=>{const gt=Qr(N);pr(gt??N)},0):(R?.([N-U,Z]),Jr(W,!0)):K.current.range.height>1&&at(V.downFill,y,F)?kl():K.current.range.width>1&&at(V.rightFill,y,F)?xl():at(V.goToNextPage,y,F)?Z+=Math.max(1,ln.current.height-4):at(V.goToPreviousPage,y,F)?Z-=Math.max(1,ln.current.height-4):at(V.goToFirstCell,y,F)?(o(void 0),Z=0,N=0):at(V.goToLastCell,y,F)?(o(void 0),Z=Number.MAX_SAFE_INTEGER,N=Number.MAX_SAFE_INTEGER):at(V.selectToFirstCell,y,F)?(o(void 0),$n([-2,-2])):at(V.selectToLastCell,y,F)?(o(void 0),$n([2,2])):Oi?(at(V.closeOverlay,y,F)&&o(void 0),at(V.acceptOverlayDown,y,F)&&(o(void 0),Z++),at(V.acceptOverlayUp,y,F)&&(o(void 0),Z--),at(V.acceptOverlayLeft,y,F)&&(o(void 0),N--),at(V.acceptOverlayRight,y,F)&&(o(void 0),N++)):(at(V.goDownCell,y,F)?Z+=1:at(V.goUpCell,y,F)?Z-=1:at(V.goRightCell,y,F)?N+=1:at(V.goLeftCell,y,F)?N-=1:at(V.goDownCellRetainSelection,y,F)?(Z+=1,pe=!0):at(V.goUpCellRetainSelection,y,F)?(Z-=1,pe=!0):at(V.goRightCellRetainSelection,y,F)?(N+=1,pe=!0):at(V.goLeftCellRetainSelection,y,F)?(N-=1,pe=!0):at(V.goToLastRow,y,F)?Z=Le-1:at(V.goToFirstRow,y,F)?Z=Number.MIN_SAFE_INTEGER:at(V.goToLastColumn,y,F)?N=Number.MAX_SAFE_INTEGER:at(V.goToFirstColumn,y,F)?N=Number.MIN_SAFE_INTEGER:(qe==="rect"||qe==="multi-rect")&&(at(V.selectGrowDown,y,F)?$n([0,1]):at(V.selectGrowUp,y,F)?$n([0,-1]):at(V.selectGrowRight,y,F)?$n([1,0]):at(V.selectGrowLeft,y,F)?$n([-1,0]):at(V.selectToLastRow,y,F)?$n([0,2]):at(V.selectToFirstRow,y,F)?$n([0,-2]):at(V.selectToLastColumn,y,F)?$n([2,0]):at(V.selectToFirstColumn,y,F)&&$n([-2,0])),St=F.didMatch),Vn!==void 0&&Vn!=="normal"&&Z!==le){const gt=Vn==="skip-up"||Vn==="skip"||Vn==="block",kt=Vn==="skip-down"||Vn==="skip"||Vn==="block",wt=Z<le;if(wt&&gt){for(;Z>=0&&Fr(Z).isGroupHeader;)Z--;Z<0&&(Z=le)}else if(!wt&&kt){for(;Z<Le&&Fr(Z).isGroupHeader;)Z++;Z>=Le&&(Z=le)}}const Ze=br(N,Z,!1,pe),Je=F.didMatch;return Je&&(Ze||!St||jt)&&L(),Je},[Vn,Oi,K,Xn,ne,ge,qe,U,Fr,Le,br,oe,Mt,w.length,Qt,jt,Ar,nt,It,Ft,Qr,pr,R,Jr,kl,xl,$n]),Pi=f.useCallback(y=>{let L=!1;if(G!==void 0&&G({...y,...y.location&&{location:[y.location[0]-U,y.location[1]]},cancel:()=>{L=!0}}),L||Rl(y)||K.current===void 0)return;const[F,W]=K.current.cell,q=ln.current;if(Ie&&!y.metaKey&&!y.ctrlKey&&K.current!==void 0&&y.key.length===1&&/[\p{L}\p{M}\p{N}\p{S}\p{P}]/u.test(y.key)&&y.bounds!==void 0&&di(x([F-U,Math.max(0,Math.min(W,Le-1))]))){if((!Ft||W!==Le)&&(q.y>W||W>q.y+q.height||q.x>F||F>q.x+q.width))return;R?.([F-U,W]),Jr(y.bounds,!0,y.key),y.stopPropagation(),y.preventDefault()}},[Ie,G,Rl,K,x,U,Le,Ft,R,Jr]),Zf=f.useCallback((y,L)=>{const F=y.location[0]-U;if(y.kind==="header"&&ae?.(F,{...y,preventDefault:L}),y.kind===An){if(F<0)return;ee?.(F,{...y,preventDefault:L})}if(y.kind==="cell"){const[W,q]=y.location;X?.([F,q],{...y,preventDefault:L}),zm(K,y.location)||br(W,q,!1,!1)}},[K,X,ee,ae,U,br]),Ba=f.useCallback(async y=>{if(!Xn.paste)return;function L(V,N,Z,le){const pe=typeof Z=="object"?Z?.join(`
`)??"":Z?.toString()??"";if(!hi(V)&&di(V)&&V.readonly!==!0){const St=I?.(pe,V);if(St!==void 0&&ci(St))return{location:N,value:St};const it=en(V);if(it===void 0)return;if(it.kind===J.Custom){Dn(V.kind===J.Custom);const Ze=it.onPaste?.(pe,V.data);return Ze===void 0?void 0:{location:N,value:{...V,data:Ze}}}else{const Ze=it.onPaste?.(pe,V,{formatted:le,formattedString:typeof le=="string"?le:le?.join(`
`),rawValue:Z});return Ze===void 0?void 0:(Dn(Ze.kind===V.kind),{location:N,value:Ze})}}}const F=K.columns,W=K.rows,q=Wt.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0;let j;if(K.current!==void 0?j=[K.current.range.x,K.current.range.y]:F.length===1?j=[F.first()??0,0]:W.length===1&&(j=[U,W.first()??0]),q&&j!==void 0){let V,N;const Z="text/plain",le="text/html";if(navigator.clipboard.read!==void 0){const Ze=await navigator.clipboard.read();for(const Je of Ze){if(Je.types.includes(le)){const kt=await(await Je.getType(le)).text(),wt=Ou(kt);if(wt!==void 0){V=wt;break}}Je.types.includes(Z)&&(N=await(await Je.getType(Z)).text())}}else if(navigator.clipboard.readText!==void 0)N=await navigator.clipboard.readText();else if(y!==void 0&&y?.clipboardData!==null){if(y.clipboardData.types.includes(le)){const Ze=y.clipboardData.getData(le);V=Ou(Ze)}V===void 0&&y.clipboardData.types.includes(Z)&&(N=y.clipboardData.getData(Z))}else return;const[pe,St]=j,it=[];do{if(Oe===void 0){const Ze=En(j),Je=N??V?.map(kt=>kt.map(wt=>wt.rawValue).join("	")).join("	")??"",gt=L(Ze,j,Je,void 0);gt!==void 0&&it.push(gt);break}if(V===void 0){if(N===void 0)return;V=q0(N)}if(Oe===!1||typeof Oe=="function"&&Oe?.([j[0]-U,j[1]],V.map(Ze=>Ze.map(Je=>Je.rawValue?.toString()??"")))!==!0)return;for(const[Ze,Je]of V.entries()){if(Ze+St>=Le)break;for(const[gt,kt]of Je.entries()){const wt=[gt+pe,Ze+St],[ii,Fi]=wt;if(ii>=rt.length||Fi>=hn)continue;const Li=En(wt),Kn=L(Li,wt,kt.rawValue,kt.formatted);Kn!==void 0&&it.push(Kn)}}}while(!1);Fn(it),fn.current?.damage(it.map(Ze=>({cell:Ze.location})))}},[I,en,En,K,Xn.paste,Wt,rt.length,Fn,hn,Oe,U,Le]);bn("paste",Ba,d,!1,!0);const _i=f.useCallback(async(y,L)=>{if(!Xn.copy)return;const F=L===!0||Wt.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0,W=K.columns,q=K.rows,j=(V,N)=>{if(!Fe)_u(V,N,y);else{const Z=N.map(le=>({kind:J.Text,data:w[le].title,displayData:w[le].title,allowOverlay:!1}));_u([Z,...V],N,y)}};if(F&&z!==void 0){if(K.current!==void 0){let V=z(K.current.range,xn.current.signal);typeof V!="object"&&(V=await V()),j(V,kr(K.current.range.x-U,K.current.range.x+K.current.range.width-U))}else if(q!==void 0&&q.length>0){const N=[...q].map(Z=>{const le=z({x:U,y:Z,width:w.length,height:1},xn.current.signal);return typeof le=="object"?le[0]:le().then(pe=>pe[0])});if(N.some(Z=>Z instanceof Promise)){const Z=await Promise.all(N);j(Z,kr(w.length))}else j(N,kr(w.length))}else if(W.length>0){const V=[],N=[];for(const Z of W){let le=z({x:Z,y:0,width:1,height:Le},xn.current.signal);typeof le!="object"&&(le=await le()),V.push(le),N.push(Z-U)}if(V.length===1)j(V[0],N);else{const Z=V.reduce((le,pe)=>le.map((St,it)=>[...St,...pe[it]]));j(Z,N)}}}},[w,z,K,Xn.copy,U,Wt,Le,Fe]);bn("copy",_i,d,!1,!1);const Jf=f.useCallback(async y=>{if(!(!Xn.cut||!(Wt.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0))&&(await _i(y),K.current!==void 0)){let F={current:{cell:K.current.cell,range:K.current.range,rangeStack:[]},rows:ot.empty(),columns:ot.empty()};const W=Qt?.(F);if(W===!1||(F=W===!0?F:W,F.current===void 0))return;Ar(F.current.range)}},[Ar,K,Xn.cut,_i,Wt,Qt]);bn("cut",Jf,d,!1,!1);const Qf=f.useCallback((y,L)=>{if(fe!==void 0){U!==0&&(y=y.map(q=>[q[0]-U,q[1]])),fe(y,L);return}if(y.length===0||L===-1)return;const[F,W]=y[L];c.current!==void 0&&c.current[0]===F&&c.current[1]===W||(c.current=[F,W],br(F,W,!1,!1))},[fe,U,br]),[To,Do]=tn?.current?.cell??[],Oo=f.useRef(Xt);Oo.current=Xt,f.useLayoutEffect(()=>{Na.current&&!Yn.current&&To!==void 0&&Do!==void 0&&(To!==Re.current?.current?.cell[0]||Do!==Re.current?.current?.cell[1])&&Oo.current(To,Do),Yn.current=!1},[To,Do]);const Il=K.current!==void 0&&(K.current.cell[0]>=rt.length||K.current.cell[1]>=hn);f.useLayoutEffect(()=>{Il&&oe(Bo,!1)},[Il,oe]);const eh=f.useMemo(()=>Ft===!0&&Nt?.tint===!0?ot.fromSingleSelection(hn-1):ot.empty(),[hn,Ft,Nt?.tint]),th=f.useCallback(y=>typeof qt=="boolean"?qt:qt?.(y-U)??!0,[U,qt]),nh=f.useMemo(()=>{if(La===void 0||a.current===null)return null;const{bounds:y,group:L}=La,F=a.current.getBoundingClientRect();return f.createElement(F0,{bounds:y,group:L,canvasBounds:F,onClose:()=>Aa(void 0),onFinish:W=>{Aa(void 0),re?.(L,W)}})},[re,La]),rh=Math.min(rt.length,Ve+(_n?1:0));f.useImperativeHandle(t,()=>({appendRow:(y,L)=>pr(y+U,L),appendColumn:(y,L)=>Ro(y,L),updateCells:y=>(U!==0&&(y=y.map(L=>({cell:[L.cell[0]+U,L.cell[1]]}))),fn.current?.damage(y)),getBounds:(y,L)=>{if(!(a?.current===null||Wt?.current===null)){if(y===void 0&&L===void 0){const F=a.current.getBoundingClientRect(),W=F.width/Wt.current.clientWidth;return{x:F.x-Wt.current.scrollLeft*W,y:F.y-Wt.current.scrollTop*W,width:Wt.current.scrollWidth*W,height:Wt.current.scrollHeight*W}}return fn.current?.getBounds((y??0)+U,L)}},focus:()=>fn.current?.focus(),emit:async y=>{switch(y){case"delete":Pi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":Pi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":Pi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await _i(void 0,!0);break;case"paste":await Ba();break}},scrollTo:Xt,remeasureColumns:y=>{for(const L of y)Io(L+U)},getMouseArgsForPosition:(y,L,F)=>{if(fn?.current===null)return;const W=fn.current.getMouseArgsForPosition(y,L,F);if(W!==void 0)return{...W,location:[W.location[0]-U,W.location[1]]}}}),[pr,Ro,Io,Wt,_i,Pi,Ba,U,Xt]);const[El,Tl]=ri??[],ih=f.useCallback(y=>{const[L,F]=y;if(F===-1){ne!=="none"&&(nt(ot.fromSingleSelection(L),void 0,!1),pn());return}El===L&&Tl===F||(ft({cell:y,range:{x:L,y:F,width:1,height:1}},!0,!1,"keyboard-nav"),Xt(L,F))},[ne,pn,Xt,El,Tl,ft,nt]),[oh,ah]=f.useState(!1),Dl=f.useRef(Qc(y=>{ah(y)},5)),sh=f.useCallback(()=>{Dl.current(!0),K.current===void 0&&K.columns.length===0&&K.rows.length===0&&l===void 0&&ft({cell:[U,Co],range:{x:U,y:Co,width:1,height:1}},!0,!1,"keyboard-select")},[Co,K,l,U,ft]),lh=f.useCallback(()=>{Dl.current(!1)},[]),[uh,ch]=f.useMemo(()=>{let y;const L=vt?.scrollbarWidthOverride??Ss(),F=Le+(Ft?1:0);if(typeof Gn=="number")y=kn+F*Gn;else{let q=0;const j=Math.min(F,10);for(let V=0;V<j;V++)q+=Gn(V);q=Math.floor(q/j),y=kn+F*q}y+=L;const W=rt.reduce((q,j)=>j.width+q,0)+L;return[`${Math.min(1e5,W)}px`,`${Math.min(1e5,y)}px`]},[rt,vt?.scrollbarWidthOverride,Gn,Le,Ft,kn]),dh=f.useMemo(()=>Jm(et),[et]);return f.createElement(Sd.Provider,{value:et},f.createElement(j0,{style:dh,className:Y,inWidth:p??uh,inHeight:v??ch},f.createElement(D0,{fillHandle:yn,drawFocusRing:Ci,experimental:vt,fixedShadowX:_t,fixedShadowY:Cn,getRowThemeOverride:fr,headerIcons:er,imageWindowLoader:zn,initialSize:$,isDraggable:$e,onDragLeave:Ge,onRowMoved:Ct,overscrollX:Mi,overscrollY:Ea,preventDiagonalScrolling:be,rightElement:dt,rightElementProps:bt,smoothScrollX:Gt,smoothScrollY:qn,className:Y,enableGroups:Pt,onCanvasFocused:sh,onCanvasBlur:lh,canvasRef:a,onContextMenu:Zf,theme:et,cellXOffset:Lf,cellYOffset:Co,accessibilityHeight:Pr.height,onDragEnd:Xf,columns:rt,nonGrowWidth:gr,drawHeader:Ke,onColumnProposeMove:Uf,drawCell:ct,disabledRows:eh,freezeColumns:rh,lockColumns:U,firstColAccessible:U===0,getCellContent:En,minColumnWidth:rr,maxColumnWidth:Kr,searchInputRef:s,showSearch:Zr,onSearchClose:Ta,highlightRegions:Af,getCellsForSelection:z,getGroupDetails:Oa,headerHeight:ki,isFocused:oh,groupHeaderHeight:Pt?bo:0,freezeTrailingRows:Pe+(Ft&&Nt?.sticky===!0?1:0),hasAppendRow:Ft,onColumnResize:ve,onColumnResizeEnd:je,onColumnResizeStart:we,onCellFocused:ih,onColumnMoved:qf,onDragStart:Gf,onHeaderMenuClick:Nf,onHeaderIndicatorClick:Bf,onItemHovered:$a,isFilling:l?.fillHandle===!0,onMouseMove:$f,onKeyDown:Pi,onKeyUp:ce,onMouseDown:Hf,onMouseUp:Vf,onDragOverCell:dn,onDrop:Rn,onSearchResultsChanged:Qf,onVisibleRegionChanged:Wf,clientSize:zt,rowHeight:Gn,searchResults:se,searchValue:P,onSearchValueChange:H,rows:hn,scrollRef:Wt,selection:K,translateX:Pr.tx,translateY:Pr.ty,verticalBorder:th,gridRef:fn,getCellRenderer:en,resizeIndicator:mo}),nh,i!==void 0&&f.createElement(f.Suspense,{fallback:null},f.createElement(av,{...i,validateCell:ke,bloom:D,id:Kf,getCellRenderer:en,className:vt?.isSubGrid===!0?"click-outside-ignore":void 0,provideEditor:rn,imageEditorOverride:g,portalElementRef:ml,onFinishEditing:jf,markdownDivCreateNode:m,isOutsideClick:Ma,customEventTarget:vt?.eventTarget}))))},cv=f.forwardRef(uv);function Au(e){const{cell:t,posX:n,posY:r,bounds:i,theme:o}=e,{width:s,height:a,x:l,y:u}=i,c=t.maxSize??o.checkboxMaxSize,d=Math.floor(i.y+a/2),g=nd(c,a,o.cellVerticalPadding),h=td(t.contentAlign??"center",l,s,o.cellHorizontalPadding,g),m=ed(h,d,g),p=rd(l+n,u+r,m);return Vs(t)&&p}const dv={getAccessibilityString:e=>e.data?.toString()??"false",kind:J.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>fv(e,e.cell.data,Vs(e.cell),e.cell.maxSize??e.theme.checkboxMaxSize,e.cell.hoverEffectIntensity??.35),onDelete:e=>({...e,data:!1}),onSelect:e=>{Au(e)&&e.preventDefault()},onClick:e=>{if(Au(e))return{...e.cell,data:Ad(e.cell.data)}},onPaste:(e,t)=>{let n=ta;return e.toLowerCase()==="true"?n=!0:e.toLowerCase()==="false"?n=!1:e.toLowerCase()==="indeterminate"&&(n=zs),n===t.data?void 0:{...t,data:n}}};function fv(e,t,n,r,i){if(!n&&t===ta)return;const{ctx:o,hoverAmount:s,theme:a,rect:l,highlighted:u,hoverX:c,hoverY:d,cell:{contentAlign:g}}=e,{x:h,y:m,width:p,height:v}=l;let w=!1;if(i>0){let b=n?1-i+i*s:.4;if(t===ta&&(b*=s),b===0)return;b<1&&(w=!0,o.globalAlpha=b)}Ks(o,a,t,h,m,p,v,u,c,d,r,g),w&&(o.globalAlpha=1)}const hv=un("div")({name:"BubblesOverlayEditorStyle",class:"gdg-b1ygi5by",propsAsIs:!1}),gv=e=>{const{bubbles:t}=e;return f.createElement(hv,null,t.map((n,r)=>f.createElement("div",{key:r,className:"boe-bubble"},n)),f.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},mv={getAccessibilityString:e=>id(e.data),kind:J.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>{const r=t.data.reduce((i,o)=>e.measureText(o).width+i+n.bubblePadding*2+n.bubbleMargin,0);return t.data.length===0?n.cellHorizontalPadding*2:r+2*n.cellHorizontalPadding-n.bubbleMargin},draw:e=>pv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return f.createElement(gv,{bubbles:t.data})},onPaste:()=>{}};function pv(e,t){const{rect:n,theme:r,ctx:i,highlighted:o}=e,{x:s,y:a,width:l,height:u}=n;let c=s+r.cellHorizontalPadding;const d=[];for(const g of t){if(c>s+l)break;const h=Xr(g,i,r.baseFontFull).width;d.push({x:c,width:h}),c+=h+r.bubblePadding*2+r.bubbleMargin}i.beginPath();for(const g of d)Jn(i,g.x,a+(u-r.bubbleHeight)/2,g.width+r.bubblePadding*2,r.bubbleHeight,r.roundingRadius??r.bubbleHeight/2);i.fillStyle=o?r.bgBubbleSelected:r.bgBubble,i.fill();for(const[g,h]of d.entries())i.beginPath(),i.fillStyle=r.textBubble,i.fillText(t[g],h.x+r.bubblePadding,a+u/2+Qn(i,r))}const vv=un("div")({name:"DrilldownOverlayEditorStyle",class:"gdg-d4zsq0x",propsAsIs:!1}),bv=e=>{const{drilldowns:t}=e;return f.createElement(vv,null,t.map((n,r)=>f.createElement("div",{key:r,className:"doe-bubble"},n.img!==void 0&&f.createElement("img",{src:n.img}),f.createElement("div",null,n.text))))},wv={getAccessibilityString:e=>id(e.data.map(t=>t.text)),kind:J.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i.text).width+r+n.bubblePadding*2+n.bubbleMargin+(i.img!==void 0?18:0),0)+2*n.cellHorizontalPadding-4,draw:e=>Cv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return f.createElement(bv,{drilldowns:t.data})},onPaste:()=>{}},ls={};function yv(e,t,n,r){const i=Math.ceil(window.devicePixelRatio),o=5,s=n-o*2,a=4,l=n*i,u=r+o,c=r*3,d=(c+o*2)*i,g=`${e},${t},${i},${n}`;if(ls[g]!==void 0)return{el:ls[g],height:l,width:d,middleWidth:a*i,sideWidth:u*i,padding:o*i,dpr:i};const h=document.createElement("canvas"),m=h.getContext("2d");return m===null?null:(h.width=d,h.height=l,m.scale(i,i),ls[g]=h,m.beginPath(),Jn(m,o,o,c,s,r),m.shadowColor="rgba(24, 25, 34, 0.4)",m.shadowBlur=1,m.fillStyle=e,m.fill(),m.shadowColor="rgba(24, 25, 34, 0.3)",m.shadowOffsetY=1,m.shadowBlur=5,m.fillStyle=e,m.fill(),m.shadowOffsetY=0,m.shadowBlur=0,m.shadowBlur=0,m.beginPath(),Jn(m,o+.5,o+.5,c,s,r),m.strokeStyle=t,m.lineWidth=1,m.stroke(),{el:h,height:l,width:d,sideWidth:u*i,middleWidth:r*i,padding:o*i,dpr:i})}function Cv(e,t){const{rect:n,theme:r,ctx:i,imageLoader:o,col:s,row:a}=e,{x:l,width:u}=n,c=r.baseFontFull,d=Gs(i,c),g=Math.min(n.height,Math.max(16,Math.ceil(d*r.lineHeight)*2)),h=Math.floor(n.y+(n.height-g)/2),m=g-10,p=r.bubblePadding,v=r.bubbleMargin;let w=l+r.cellHorizontalPadding;const b=r.roundingRadius??6,x=yv(r.bgCell,r.drilldownBorder,g,b),O=[];for(const R of t){if(w>l+u)break;const _=Xr(R.text,i,c).width;let I=0;R.img!==void 0&&o.loadOrGetImage(R.img,s,a)!==void 0&&(I=m-8+4);const k=_+I+p*2;O.push({x:w,width:k}),w+=k+v}if(x!==null){const{el:R,height:M,middleWidth:_,sideWidth:I,width:k,dpr:A,padding:D}=x,C=I/A,E=D/A;for(const T of O){const S=Math.floor(T.x),B=Math.floor(T.width),X=B-(C-E)*2;i.imageSmoothingEnabled=!1,i.drawImage(R,0,0,I,M,S-E,h,C,g),X>0&&i.drawImage(R,I,0,_,M,S+(C-E),h,X,g),i.drawImage(R,k-I,0,I,M,S+B-(C-E),h,C,g),i.imageSmoothingEnabled=!0}}i.beginPath();for(const[R,M]of O.entries()){const _=t[R];let I=M.x+p;if(_.img!==void 0){const k=o.loadOrGetImage(_.img,s,a);if(k!==void 0){const A=m-8;let D=0,C=0,E=k.width,T=k.height;E>T?(D+=(E-T)/2,E=T):T>E&&(C+=(T-E)/2,T=E),i.beginPath(),Jn(i,I,h+g/2-A/2,A,A,r.roundingRadius??3),i.save(),i.clip(),i.drawImage(k,D,C,E,T,I,h+g/2-A/2,A,A),i.restore(),I+=A+4}}i.beginPath(),i.fillStyle=r.textBubble,i.fillText(_.text,I,h+g/2+Qn(i,r))}}const Sv={getAccessibilityString:e=>e.data.join(", "),kind:J.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>xv(e,e.cell.displayData??e.cell.data,e.cell.rounding??e.theme.roundingRadius??4,e.cell.contentAlign),measure:(e,t)=>t.data.length*50,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:r}=e,i=r??lm;return f.createElement(i,{urls:t.data,canWrite:t.readonly!==!0,onCancel:n,onChange:o=>{n({...t,data:[o]})}})},onPaste:(e,t)=>{e=e.trim();const r=e.split(",").map(i=>{try{return new URL(i),i}catch{return}}).filter(i=>i!==void 0);if(!(r.length===t.data.length&&r.every((i,o)=>i===t.data[o])))return{...t,data:r}}},us=4;function xv(e,t,n,r){const{rect:i,col:o,row:s,theme:a,ctx:l,imageLoader:u}=e,{x:c,y:d,height:g,width:h}=i,m=g-a.cellVerticalPadding*2,p=[];let v=0;for(let b=0;b<t.length;b++){const x=t[b];if(x.length===0)continue;const O=u.loadOrGetImage(x,o,s);if(O!==void 0){p[b]=O;const R=O.width*(m/O.height);v+=R+us}}if(v===0)return;v-=us;let w=c+a.cellHorizontalPadding;r==="right"?w=Math.floor(c+h-a.cellHorizontalPadding-v):r==="center"&&(w=Math.floor(c+h/2-v/2));for(const b of p){if(b===void 0)continue;const x=b.width*(m/b.height);n>0&&(l.beginPath(),Jn(l,w,d+a.cellVerticalPadding,x,m,n),l.save(),l.clip()),l.drawImage(b,w,d+a.cellVerticalPadding,x,m),n>0&&l.restore(),w+=x+us}}function kv(e,t){let n=e*49632+t*325176;return n^=n<<13,n^=n>>17,n^=n<<5,n/4294967295*2}const Mv={getAccessibilityString:()=>"",kind:J.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:e=>{const{cell:t,col:n,row:r,ctx:i,rect:o,theme:s}=e;if(t.skeletonWidth===void 0||t.skeletonWidth===0)return;let a=t.skeletonWidth;t.skeletonWidthVariability!==void 0&&t.skeletonWidthVariability>0&&(a+=Math.round(kv(n,r)*t.skeletonWidthVariability));const l=s.cellHorizontalPadding;a+l*2>=o.width&&(a=o.width-l*2-1);const u=t.skeletonHeight??Math.min(18,o.height-2*s.cellVerticalPadding);Jn(i,o.x+l,o.y+(o.height-u)/2,a,u,s.roundingRadius??3),i.fillStyle=Br(s.textDark,.1),i.fill()},onPaste:()=>{}},Rv=()=>e=>e.targetWidth,Hu=un("div")({name:"MarkdownOverlayEditorStyle",class:"gdg-m1pnx84e",propsAsIs:!1,vars:{"m1pnx84e-0":[Rv(),"px"]}}),Iv=e=>{const{value:t,onChange:n,forceEditMode:r,createNode:i,targetRect:o,onFinish:s,validatedSelection:a}=e,l=t.data,u=t.readonly===!0,[c,d]=f.useState(l===""||r),g=f.useCallback(()=>{d(m=>!m)},[]),h=l?"gdg-ml-6":"";return c?f.createElement(Hu,{targetWidth:o.width-20},f.createElement(qr,{autoFocus:!0,highlight:!1,validatedSelection:a,value:l,onKeyDown:m=>{m.key==="Enter"&&m.stopPropagation()},onChange:n}),f.createElement("div",{className:`gdg-edit-icon gdg-checkmark-hover ${h}`,onClick:()=>s(t)},f.createElement(tm,null))):f.createElement(Hu,{targetWidth:o.width},f.createElement(Im,{contents:l,createNode:i}),!u&&f.createElement(f.Fragment,null,f.createElement("div",{className:"spacer"}),f.createElement("div",{className:`gdg-edit-icon gdg-edit-hover ${h}`,onClick:g},f.createElement(Ns,null))),f.createElement("textarea",{className:"gdg-md-edit-textarea gdg-input",autoFocus:!0}))},Ev={getAccessibilityString:e=>e.data?.toString()??"",kind:J.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:co,measure:(e,t,n)=>{const r=t.data.split(`
`)[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:e=>Zn(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:r,onFinishedEditing:i,markdownDivCreateNode:o,forceEditMode:s,validatedSelection:a}=e;return f.createElement(Iv,{onFinish:i,targetRect:r,value:n,validatedSelection:a,onChange:l=>t({...n,data:l.target.value}),forceEditMode:s,createNode:o})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},Tv={getAccessibilityString:e=>e.row.toString(),kind:Hn.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:Dv,measure:()=>44,draw:e=>Pv(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle,e.cell.checkboxStyle),onClick:e=>{const{bounds:t,cell:n,posX:r,posY:i}=e,{width:o,height:s}=t,a=n.drawHandle?7+(o-7)/2:o/2,l=s/2;if(Math.abs(r-a)<=10&&Math.abs(i-l)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}};function Dv(e,t){const{ctx:n,theme:r}=e,i=r.markerFontFull,o=t??{};return o?.font!==i&&(n.font=i,o.font=i),o.deprep=Ov,n.textAlign="center",o}function Ov(e){const{ctx:t}=e;t.textAlign="start"}function Pv(e,t,n,r,i,o){const{ctx:s,rect:a,hoverAmount:l,theme:u}=e,{x:c,y:d,width:g,height:h}=a,m=n?1:r==="checkbox-visible"?.6+.4*l:l;if(r!=="number"&&m>0){s.globalAlpha=m;const p=7*(n?l:1);if(Ks(s,u,n,i?c+p:c,d,i?g-p:g,h,!0,void 0,void 0,u.checkboxMaxSize,"center",o),i){s.globalAlpha=l,s.beginPath();for(const v of[3,6])for(const w of[-5,-1,3])s.rect(c+v,d+h/2+w,2,2);s.fillStyle=u.textLight,s.fill(),s.beginPath()}s.globalAlpha=1}if(r==="number"||r==="both"&&!n){const p=t.toString(),v=u.markerFontFull,w=c+g/2;r==="both"&&l!==0&&(s.globalAlpha=1-l),s.fillStyle=u.textLight,s.font=v,s.fillText(p,w,d+h/2+Qn(s,v)),l!==0&&(s.globalAlpha=1)}}const _v={getAccessibilityString:()=>"",kind:Hn.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>Fv(e,e.cell.hint,e.cell.icon),onPaste:()=>{}};function Fv(e,t,n){const{ctx:r,rect:i,hoverAmount:o,theme:s,spriteManager:a}=e,{x:l,y:u,width:c,height:d}=i;r.beginPath(),r.globalAlpha=o,r.rect(l+1,u+1,c,d-2),r.fillStyle=s.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();const g=t!=="";let h=0;if(n!==void 0){const p=d-8,v=l+8/2,w=u+8/2;a.drawSprite(n,"normal",r,v,w,p,s,g?1:o),h=p}else{h=24;const m=12,p=g?m:o*m,v=g?0:(1-o)*m*.5,w=s.cellHorizontalPadding+4;p>0&&(r.moveTo(l+w+v,u+d/2),r.lineTo(l+w+v+p,u+d/2),r.moveTo(l+w+v+p*.5,u+d/2-p*.5),r.lineTo(l+w+v+p*.5,u+d/2+p*.5),r.lineWidth=2,r.strokeStyle=s.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=s.textMedium,r.fillText(t,h+l+s.cellHorizontalPadding+.5,u+d/2+Qn(r,s)),r.beginPath()}function Vd(e,t,n,r,i,o,s){e.textBaseline="alphabetic";const a=Lv(e,i,r,t,n?.fullSize??!1);e.beginPath(),Jn(e,a.x,a.y,a.width,a.height,t.roundingRadius??4),e.globalAlpha=o,e.fillStyle=n?.bgColor??Br(t.textDark,.1),e.fill(),e.globalAlpha=1,e.fillStyle=t.textDark,e.textBaseline="middle",s?.("text")}function Lv(e,t,n,r,i){const o=r.cellHorizontalPadding,s=r.cellVerticalPadding;if(i)return{x:t.x+o/2,y:t.y+s/2+1,width:t.width-o,height:t.height-s-1};const a=Xr(n,e,r.baseFontFull,"alphabetic"),l=t.height-s,u=Math.min(l,a.actualBoundingBoxAscent*2.5);return{x:t.x+o/2,y:t.y+(t.height-u)/2+1,width:a.width+o*3,height:u-1}}const Av=f.lazy(async()=>await As(()=>import("./number-overlay-editor.DXS2qb1U.js"),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),Hv={getAccessibilityString:e=>e.data?.toString()??"",kind:J.Number,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,useLabel:!0,drawPrep:co,draw:e=>{const{hoverAmount:t,cell:n,ctx:r,theme:i,rect:o,overrideCursor:s}=e,{hoverEffect:a,displayData:l,hoverEffectTheme:u}=n;a===!0&&t>0&&Vd(r,i,u,l,o,t,s),Zn(e,e.cell.displayData,e.cell.contentAlign)},measure:(e,t,n)=>e.measureText(t.displayData).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return f.createElement(f.Suspense,{fallback:null},f.createElement(Av,{highlight:t,disabled:r.readonly===!0,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:o=>n({...r,data:Number.isNaN(o.floatValue??0)?0:o.floatValue})}))},onPaste:(e,t,n)=>{const r=typeof n.rawValue=="number"?n.rawValue:Number.parseFloat(typeof n.rawValue=="string"?n.rawValue:e);if(!(Number.isNaN(r)||t.data===r))return{...t,data:r,displayData:n.formattedString??t.displayData}}},zv={getAccessibilityString:()=>"",measure:()=>108,kind:J.Protected,needsHover:!1,needsHoverPosition:!1,draw:Vv,onPaste:()=>{}};function Vv(e){const{ctx:t,theme:n,rect:r}=e,{x:i,y:o,height:s}=r;t.beginPath();const a=2.5;let l=i+n.cellHorizontalPadding+a;const u=o+s/2,c=Math.cos(nu(30))*a,d=Math.sin(nu(30))*a;for(let g=0;g<12;g++)t.moveTo(l,u-a),t.lineTo(l,u+a),t.moveTo(l+c,u-d),t.lineTo(l-c,u+d),t.moveTo(l-c,u-d),t.lineTo(l+c,u+d),l+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()}const $v={getAccessibilityString:e=>e.data?.toString()??"",kind:J.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>co(e,t,e.theme.textLight),draw:e=>Zn(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+n.cellHorizontalPadding*2,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return Vt.createElement(qr,{highlight:t,autoFocus:r.readonly!==!0,disabled:r.readonly!==!1,value:r.data,validatedSelection:i,onChange:o=>n({...r,data:o.target.value})})},onPaste:()=>{}},Nv={getAccessibilityString:e=>e.data?.toString()??"",kind:J.Text,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,drawPrep:co,useLabel:!0,draw:e=>{const{cell:t,hoverAmount:n,hyperWrapping:r,ctx:i,rect:o,theme:s,overrideCursor:a}=e,{displayData:l,contentAlign:u,hoverEffect:c,allowWrapping:d,hoverEffectTheme:g}=t;c===!0&&n>0&&Vd(i,s,g,l,o,n,a),Zn(e,l,u,d,r)},measure:(e,t,n)=>{const r=t.displayData.split(`
`,t.allowWrapping===!0?void 0:1);let i=0;for(const o of r)i=Math.max(i,e.measureText(o).width);return i+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:e.allowWrapping===!0,editor:t=>{const{isHighlighted:n,onChange:r,value:i,validatedSelection:o}=t;return f.createElement(qr,{style:e.allowWrapping===!0?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:i.readonly!==!0,disabled:i.readonly===!0,altNewline:!0,value:i.data,validatedSelection:o,onChange:s=>r({...i,data:s.target.value})})}}),onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},Bv=un("div")({name:"UriOverlayEditorStyle",class:"gdg-u1rrojo",propsAsIs:!1}),Wv=e=>{const{uri:t,onChange:n,forceEditMode:r,readonly:i,validatedSelection:o,preview:s}=e,[a,l]=f.useState(!i&&(t===""||r)),u=f.useCallback(()=>{l(!0)},[]);return a?f.createElement(qr,{validatedSelection:o,highlight:!0,autoFocus:!0,value:t,onChange:n}):f.createElement(Bv,null,f.createElement("a",{className:"gdg-link-area",href:t,target:"_blank",rel:"noopener noreferrer"},s),!i&&f.createElement("div",{className:"gdg-edit-icon",onClick:u},f.createElement(Ns,null)),f.createElement("textarea",{className:"gdg-input",autoFocus:!0}))};function $d(e,t,n,r){let i=n.cellHorizontalPadding;const o=t.height/2-e.actualBoundingBoxAscent/2,s=e.width,a=e.actualBoundingBoxAscent;return r==="right"?i=t.width-s-n.cellHorizontalPadding:r==="center"&&(i=t.width/2-s/2),{x:i,y:o,width:s,height:a}}function zu(e){const{cell:t,bounds:n,posX:r,posY:i,theme:o}=e,s=t.displayData??t.data;if(t.hoverEffect!==!0||t.onClickUri===void 0)return!1;const a=pd(s,o.baseFontFull);if(a===void 0)return!1;const l=$d(a,n,o,t.contentAlign);return Nr({x:l.x-4,y:l.y-4,width:l.width+8,height:l.height+8},r,i)}const Uv={getAccessibilityString:e=>e.data?.toString()??"",kind:J.Uri,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!0,useLabel:!0,drawPrep:co,draw:e=>{const{cell:t,theme:n,overrideCursor:r,hoverX:i,hoverY:o,rect:s,ctx:a}=e,l=t.displayData??t.data,u=t.hoverEffect===!0;if(r!==void 0&&u&&i!==void 0&&o!==void 0){const c=Xr(l,a,n.baseFontFull),d=$d(c,s,n,t.contentAlign),{x:g,y:h,width:m,height:p}=d;if(i>=g-4&&i<=g-4+m+8&&o>=h-4&&o<=h-4+p+8){const v=Qn(a,n.baseFontFull);r("pointer");const w=5,b=h-v;a.beginPath(),a.moveTo(s.x+g,Math.floor(s.y+b+p+w)+.5),a.lineTo(s.x+g+m,Math.floor(s.y+b+p+w)+.5),a.strokeStyle=n.linkColor,a.stroke(),a.save(),a.fillStyle=e.cellFillColor,Zn({...e,rect:{...s,x:s.x-1}},l,t.contentAlign),Zn({...e,rect:{...s,x:s.x-2}},l,t.contentAlign),Zn({...e,rect:{...s,x:s.x+1}},l,t.contentAlign),Zn({...e,rect:{...s,x:s.x+2}},l,t.contentAlign),a.restore()}}a.fillStyle=u?n.linkColor:n.textDark,Zn(e,l,t.contentAlign)},onSelect:e=>{zu(e)&&e.preventDefault()},onClick:e=>{const{cell:t}=e;zu(e)&&t.onClickUri?.(e)},measure:(e,t,n)=>e.measureText(t.displayData??t.data).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:""}),provideEditor:e=>t=>{const{onChange:n,value:r,forceEditMode:i,validatedSelection:o}=t;return f.createElement(Wv,{forceEditMode:r.readonly!==!0&&(i||e.hoverEffect===!0&&e.onClickUri!==void 0),uri:r.data,preview:r.displayData??r.data,validatedSelection:o,readonly:r.readonly===!0,onChange:s=>n({...r,data:s.target.value})})},onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},qv=[Tv,_v,dv,mv,wv,Sv,Mv,Ev,Hv,zv,$v,Nv,Uv];var cs,Vu;function Gv(){if(Vu)return cs;Vu=1;var e=Jc(),t=Tc(),n="Expected a function";function r(i,o,s){var a=!0,l=!0;if(typeof i!="function")throw new TypeError(n);return t(s)&&(a="leading"in s?!!s.leading:a,l="trailing"in s?!!s.trailing:l),e(i,o,{leading:a,maxWait:o,trailing:l})}return cs=r,cs}var Xv=Gv();const Yv=lr(Xv),ds=[];class jv extends wd{imageLoaded=()=>{};loadedLocations=[];cache={};setCallback(t){this.imageLoaded=t}sendLoaded=Yv(()=>{this.imageLoaded(new eo(this.loadedLocations)),this.loadedLocations=[]},20);clearOutOfWindow=()=>{const t=Object.keys(this.cache);for(const n of t){const r=this.cache[n];let i=!1;for(let o=0;o<r.cells.length;o++){const s=r.cells[o];if(this.isInWindow(s)){i=!0;break}}i?r.cells=r.cells.filter(this.isInWindow):(r.cancel(),delete this.cache[n])}};loadImage(t,n,r,i){let o=!1;const s=ds.pop()??new Image;let a=!1;const l={img:void 0,cells:[Wn(n,r)],url:t,cancel:()=>{a||(a=!0,ds.length<12?ds.unshift(s):o||(s.src=""))}},u=new Promise(c=>s.addEventListener("load",()=>c(null)));requestAnimationFrame(async()=>{try{s.src=t,await u,await s.decode();const c=this.cache[i];if(c!==void 0&&!a){c.img=s;for(const d of c.cells)this.loadedLocations.push(js(d));o=!0,this.sendLoaded()}}catch{l.cancel()}}),this.cache[i]=l}loadOrGetImage(t,n,r){const i=t,o=this.cache[i];if(o!==void 0){const s=Wn(n,r);return o.cells.includes(s)||o.cells.push(s),o.img}else this.loadImage(t,n,r,i)}}const Kv=(e,t)=>{const n=f.useMemo(()=>({...$p,...e.headerIcons}),[e.headerIcons]),r=f.useMemo(()=>e.renderers??qv,[e.renderers]),i=f.useMemo(()=>e.imageWindowLoader??new jv,[e.imageWindowLoader]);return f.createElement(cv,{...e,renderers:r,headerIcons:n,ref:t,imageWindowLoader:i})},Zv=f.forwardRef(Kv);function $u(e,t){const n=f.useRef(null),r=f.useRef(),i=f.useCallback(()=>{n.current&&(clearTimeout(n.current),n.current=null)},[]);return f.useEffect(()=>i,[i]),{debouncedCallback:f.useCallback((...s)=>{r.current=s,i(),n.current=setTimeout(()=>{r.current&&(e(...r.current),r.current=void 0)},t)},[e,t,i]),cancel:i}}var Jv=xh();const Qv=lr(Jv);var Jo={exports:{}};/*! Moment Duration Format v2.2.2
 *  https://github.com/jsmreese/moment-duration-format
 *  Date: 2018-02-16
 *
 *  Duration format plugin function for the Moment.js library
 *  http://momentjs.com/
 *
 *  Copyright 2018 John Madhavan-Reese
 *  Released under the MIT license
 */var eb=Jo.exports,Nu;function tb(){return Nu||(Nu=1,function(e,t){(function(n,r){try{e.exports=r(kh)}catch{e.exports=r}n&&(n.momentDurationFormatSetup=n.moment?r(n.moment):r)})(eb,function(n){var r=!1,i=!1,o=!1,s=!1,a="escape years months weeks days hours minutes seconds milliseconds general".split(" "),l=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function u(H,P){return P.length>H.length?!1:H.indexOf(P)!==-1}function c(H){for(var P="";H;)P+="0",H-=1;return P}function d(H){for(var P=H.split("").reverse(),G=0,ce=!0;ce&&G<P.length;)G?P[G]==="9"?P[G]="0":(P[G]=(parseInt(P[G],10)+1).toString(),ce=!1):(parseInt(P[G],10)<5&&(ce=!1),P[G]="0"),G+=1;return ce&&P.push("1"),P.reverse().join("")}function g(H,P){var G=_(S(P).sort(),function(he){return he+":"+P[he]}).join(","),ce=H+"+"+G;return g.cache[ce]||(g.cache[ce]=Intl.NumberFormat(H,P)),g.cache[ce]}g.cache={};function h(H,P,G){var ce=P.useToLocaleString,he=P.useGrouping,Ie=he&&P.grouping.slice(),me=P.maximumSignificantDigits,Qe=P.minimumIntegerDigits||1,xe=P.fractionDigits||0,At=P.groupingSeparator,Xe=P.decimalSeparator;if(ce&&G){var qe={minimumIntegerDigits:Qe,useGrouping:he};if(xe&&(qe.maximumFractionDigits=xe,qe.minimumFractionDigits=xe),me&&H>0&&(qe.maximumSignificantDigits=me),o){if(!s){var ne=T({},P);ne.useGrouping=!1,ne.decimalSeparator=".",H=parseFloat(h(H,ne),10)}return g(G,qe).format(H)}else{if(!i){var ne=T({},P);ne.useGrouping=!1,ne.decimalSeparator=".",H=parseFloat(h(H,ne),10)}return H.toLocaleString(G,qe)}}var ge;me?ge=H.toPrecision(me+1):ge=H.toFixed(xe+1);var Ce,de,Se,Ee=ge.split("e");Se=Ee[1]||"",Ee=Ee[0].split("."),de=Ee[1]||"",Ce=Ee[0]||"";var De=Ce.length,tt=de.length,Oe=De+tt,Fe=Ce+de;(me&&Oe===me+1||!me&&tt===xe+1)&&(Fe=d(Fe),Fe.length===Oe+1&&(De=De+1),tt&&(Fe=Fe.slice(0,-1)),Ce=Fe.slice(0,De),de=Fe.slice(De)),me&&(de=de.replace(/0*$/,""));var Ve=parseInt(Se,10);Ve>0?de.length<=Ve?(de=de+c(Ve-de.length),Ce=Ce+de,de=""):(Ce=Ce+de.slice(0,Ve),de=de.slice(Ve)):Ve<0&&(de=c(Math.abs(Ve)-Ce.length)+Ce+de,Ce="0"),me||(de=de.slice(0,xe),de.length<xe&&(de=de+c(xe-de.length)),Ce.length<Qe&&(Ce=c(Qe-Ce.length)+Ce));var Te="";if(he){Ee=Ce;for(var Me;Ee.length;)Ie.length&&(Me=Ie.shift()),Te&&(Te=At+Te),Te=Ee.slice(-Me)+Te,Ee=Ee.slice(0,-Me)}else Te=Ce;return de&&(Te=Te+Xe+de),Te}function m(H,P){return H.label.length>P.label.length?-1:H.label.length<P.label.length?1:0}function p(H,P){var G=[];return M(S(P),function(ce){if(ce.slice(0,15)==="_durationLabels"){var he=ce.slice(15).toLowerCase();M(S(P[ce]),function(Ie){Ie.slice(0,1)===H&&G.push({type:he,key:Ie,label:P[ce][Ie]})})}}),G}function v(H,P,G){return P===1&&G===null?H:H+H}var w={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:v};function b(H){return Object.prototype.toString.call(H)==="[object Array]"}function x(H){return Object.prototype.toString.call(H)==="[object Object]"}function O(H,P){for(var G=H.length;G-=1;)if(P(H[G]))return H[G]}function R(H,P){var G=0,ce=H&&H.length||0,he;for(typeof P!="function"&&(he=P,P=function(Ie){return Ie===he});G<ce;){if(P(H[G]))return H[G];G+=1}}function M(H,P){var G=0,ce=H.length;if(!(!H||!ce))for(;G<ce;){if(P(H[G],G)===!1)return;G+=1}}function _(H,P){var G=0,ce=H.length,he=[];if(!H||!ce)return he;for(;G<ce;)he[G]=P(H[G],G),G+=1;return he}function I(H,P){return _(H,function(G){return G[P]})}function k(H){var P=[];return M(H,function(G){G&&P.push(G)}),P}function A(H){var P=[];return M(H,function(G){R(P,G)||P.push(G)}),P}function D(H,P){var G=[];return M(H,function(ce){M(P,function(he){ce===he&&G.push(ce)})}),A(G)}function C(H,P){var G=[];return M(H,function(ce,he){if(!P(ce))return G=H.slice(he),!1}),G}function E(H,P){var G=H.slice().reverse();return C(G,P).reverse()}function T(H,P){for(var G in P)P.hasOwnProperty(G)&&(H[G]=P[G]);return H}function S(H){var P=[];for(var G in H)H.hasOwnProperty(G)&&P.push(G);return P}function B(H,P){var G=0,ce=H.length;if(!H||!ce)return!1;for(;G<ce;){if(P(H[G],G)===!0)return!0;G+=1}return!1}function X(H){var P=[];return M(H,function(G){P=P.concat(G)}),P}function Y(){var H=0;try{H.toLocaleString("i")}catch(P){return P.name==="RangeError"}return!1}function ae(H){return H(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})==="3.6"}function Q(H){var P=!0;return P=P&&H(1,"en",{minimumIntegerDigits:1})==="1",P=P&&H(1,"en",{minimumIntegerDigits:2})==="01",P=P&&H(1,"en",{minimumIntegerDigits:3})==="001",!(!P||(P=P&&H(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0})==="100",P=P&&H(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1})==="100.0",P=P&&H(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2})==="99.99",P=P&&H(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3})==="99.990",!P)||(P=P&&H(99.99,"en",{maximumSignificantDigits:1})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:2})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:3})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:4})==="99.99",P=P&&H(99.99,"en",{maximumSignificantDigits:5})==="99.99",!P)||(P=P&&H(1e3,"en",{useGrouping:!0})==="1,000",P=P&&H(1e3,"en",{useGrouping:!1})==="1000",!P))}function ee(){var H=[].slice.call(arguments),P={},G;if(M(H,function(me,Qe){if(!Qe){if(!b(me))throw"Expected array as the first argument to durationsFormat.";G=me}if(typeof me=="string"||typeof me=="function"){P.template=me;return}if(typeof me=="number"){P.precision=me;return}x(me)&&T(P,me)}),!G||!G.length)return[];P.returnMomentTypes=!0;var ce=_(G,function(me){return me.format(P)}),he=D(a,A(I(X(ce),"type"))),Ie=P.largest;return Ie&&(he=he.slice(0,Ie)),P.returnMomentTypes=!1,P.outputTypes=he,_(G,function(me){return me.format(P)})}function re(){var H=[].slice.call(arguments),P=T({},this.format.defaults),G=this.asMilliseconds(),ce=this.asMonths();typeof this.isValid=="function"&&this.isValid()===!1&&(G=0,ce=0);var he=G<0,Ie=n.duration(Math.abs(G),"milliseconds"),me=n.duration(Math.abs(ce),"months");M(H,function($){if(typeof $=="string"||typeof $=="function"){P.template=$;return}if(typeof $=="number"){P.precision=$;return}x($)&&T(P,$)});var Qe={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},xe={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};P.types=a;var At=function($){return R(a,function($e){return xe[$e].test($)})},Xe=new RegExp(_(a,function($){return xe[$].source}).join("|"),"g");P.duration=this;var qe=typeof P.template=="function"?P.template.apply(P):P.template,ne=P.outputTypes,ge=P.returnMomentTypes,Ce=P.largest,de=[];ne||(b(P.stopTrim)&&(P.stopTrim=P.stopTrim.join("")),P.stopTrim&&M(P.stopTrim.match(Xe),function($){var $e=At($);$e==="escape"||$e==="general"||de.push($e)}));var Se=n.localeData();Se||(Se={}),M(S(w),function($){if(typeof w[$]=="function"){Se[$]||(Se[$]=w[$]);return}Se["_"+$]||(Se["_"+$]=w[$])}),M(S(Se._durationTimeTemplates),function($){qe=qe.replace("_"+$+"_",Se._durationTimeTemplates[$])});var Ee=P.userLocale||n.locale(),De=P.useLeftUnits,tt=P.usePlural,Oe=P.precision,Fe=P.forceLength,Ve=P.useGrouping,Te=P.trunc,Me=P.useSignificantDigits&&Oe>0,pt=Me?P.precision:0,ht=pt,Ye=P.minValue,xt=!1,Yt=P.maxValue,Ht=!1,Mt=P.useToLocaleString,cn=P.groupingSeparator,Ut=P.decimalSeparator,tn=P.grouping;Mt=Mt&&(r||o);var Tt=P.trim;b(Tt)&&(Tt=Tt.join(" ")),Tt===null&&(Ce||Yt||Me)&&(Tt="all"),(Tt===null||Tt===!0||Tt==="left"||Tt==="right")&&(Tt="large"),Tt===!1&&(Tt="");var yt=function($){return $.test(Tt)},nn=/large/,ut=/small/,rn=/both/,Nt=/mid/,Pe=/^all|[^sm]all/,Dt=/final/,Rt=Ce>0||B([nn,rn,Pe],yt),Mn=B([ut,rn,Pe],yt),qt=B([Nt,Pe],yt),dn=B([Dt,Pe],yt),Rn=_(qe.match(Xe),function($,$e){var Ge=At($);return $.slice(0,1)==="*"&&($=$.slice(1),Ge!=="escape"&&Ge!=="general"&&de.push(Ge)),{index:$e,length:$.length,text:"",token:Ge==="escape"?$.replace(xe.escape,"$1"):$,type:Ge==="escape"||Ge==="general"?null:Ge}}),Zt={index:0,length:0,token:"",text:"",type:null},Jt=[];De&&Rn.reverse(),M(Rn,function($){if($.type){(Zt.type||Zt.text)&&Jt.push(Zt),Zt=$;return}De?Zt.text=$.token+Zt.text:Zt.text+=$.token}),(Zt.type||Zt.text)&&Jt.push(Zt),De&&Jt.reverse();var We=D(a,A(k(I(Jt,"type"))));if(!We.length)return I(Jt,"text").join("");We=_(We,function($,$e){var Ge=$e+1===We.length,Ct=!$e,mn;$==="years"||$==="months"?mn=me.as($):mn=Ie.as($);var sn=Math.floor(mn),be=mn-sn,dt=R(Jt,function(bt){return $===bt.type});return Ct&&Yt&&mn>Yt&&(Ht=!0),Ge&&Ye&&Math.abs(P.duration.as($))<Ye&&(xt=!0),Ct&&Fe===null&&dt.length>1&&(Fe=!0),Ie.subtract(sn,$),me.subtract(sn,$),{rawValue:mn,wholeValue:sn,decimalValue:Ge?be:0,isSmallest:Ge,isLargest:Ct,type:$,tokenLength:dt.length}});var Ot=Te?Math.floor:Math.round,yn=function($,$e){var Ge=Math.pow(10,$e);return Ot($*Ge)/Ge},vt=!1,_t=!1,Cn=function($,$e){var Ge={useGrouping:Ve,groupingSeparator:cn,decimalSeparator:Ut,grouping:tn,useToLocaleString:Mt};return Me&&(pt<=0?($.rawValue=0,$.wholeValue=0,$.decimalValue=0):(Ge.maximumSignificantDigits=pt,$.significantDigits=pt)),Ht&&!_t&&($.isLargest?($.wholeValue=Yt,$.decimalValue=0):($.wholeValue=0,$.decimalValue=0)),xt&&!_t&&($.isSmallest?($.wholeValue=Ye,$.decimalValue=0):($.wholeValue=0,$.decimalValue=0)),$.isSmallest||$.significantDigits&&$.significantDigits-$.wholeValue.toString().length<=0?Oe<0?$.value=yn($.wholeValue,Oe):Oe===0?$.value=Ot($.wholeValue+$.decimalValue):Me?(Te?$.value=yn($.rawValue,pt-$.wholeValue.toString().length):$.value=$.rawValue,$.wholeValue&&(pt-=$.wholeValue.toString().length)):(Ge.fractionDigits=Oe,Te?$.value=$.wholeValue+yn($.decimalValue,Oe):$.value=$.wholeValue+$.decimalValue):Me&&$.wholeValue?($.value=Math.round(yn($.wholeValue,$.significantDigits-$.wholeValue.toString().length)),pt-=$.wholeValue.toString().length):$.value=$.wholeValue,$.tokenLength>1&&(Fe||vt)&&(Ge.minimumIntegerDigits=$.tokenLength,_t&&Ge.maximumSignificantDigits<$.tokenLength&&delete Ge.maximumSignificantDigits),!vt&&($.value>0||Tt===""||R(de,$.type)||R(ne,$.type))&&(vt=!0),$.formattedValue=h($.value,Ge,Ee),Ge.useGrouping=!1,Ge.decimalSeparator=".",$.formattedValueEn=h($.value,Ge,"en"),$.tokenLength===2&&$.type==="milliseconds"&&($.formattedValueMS=h($.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),$};if(We=_(We,Cn),We=k(We),We.length>1){var er=function($){return R(We,function($e){return $e.type===$})},zn=function($){var $e=er($.type);$e&&M($.targets,function(Ge){var Ct=er(Ge.type);Ct&&parseInt($e.formattedValueEn,10)===Ge.value&&($e.rawValue=0,$e.wholeValue=0,$e.decimalValue=0,Ct.rawValue+=1,Ct.wholeValue+=1,Ct.decimalValue=0,Ct.formattedValueEn=Ct.wholeValue.toString(),_t=!0)})};M(l,zn)}return _t&&(vt=!1,pt=ht,We=_(We,Cn),We=k(We)),ne&&!(Ht&&!P.trim)?(We=_(We,function($){return R(ne,function($e){return $.type===$e})?$:null}),We=k(We)):(Rt&&(We=C(We,function($){return!$.isSmallest&&!$.wholeValue&&!R(de,$.type)})),Ce&&We.length&&(We=We.slice(0,Ce)),Mn&&We.length>1&&(We=E(We,function($){return!$.wholeValue&&!R(de,$.type)&&!$.isLargest})),qt&&(We=_(We,function($,$e){return $e>0&&$e<We.length-1&&!$.wholeValue?null:$}),We=k(We)),dn&&We.length===1&&!We[0].wholeValue&&!(!Te&&We[0].isSmallest&&We[0].rawValue<Ye)&&(We=[])),ge?We:(M(Jt,function($){var $e=Qe[$.type],Ge=R(We,function(bt){return bt.type===$.type});if(!(!$e||!Ge)){var Ct=Ge.formattedValueEn.split(".");Ct[0]=parseInt(Ct[0],10),Ct[1]?Ct[1]=parseFloat("0."+Ct[1],10):Ct[1]=null;var mn=Se.durationPluralKey($e,Ct[0],Ct[1]),sn=p($e,Se),be=!1,dt={};M(Se._durationLabelTypes,function(bt){var jt=R(sn,function(Gt){return Gt.type===bt.type&&Gt.key===mn});jt&&(dt[jt.type]=jt.label,u($.text,bt.string)&&($.text=$.text.replace(bt.string,jt.label),be=!0))}),tt&&!be&&(sn.sort(m),M(sn,function(bt){if(dt[bt.type]===bt.label)return u($.text,bt.label)?!1:void 0;if(u($.text,bt.label))return $.text=$.text.replace(bt.label,dt[bt.type]),!1}))}}),Jt=_(Jt,function($){if(!$.type)return $.text;var $e=R(We,function(Ct){return Ct.type===$.type});if(!$e)return"";var Ge="";return De&&(Ge+=$.text),(he&&Ht||!he&&xt)&&(Ge+="< ",Ht=!1,xt=!1),(he&&xt||!he&&Ht)&&(Ge+="> ",Ht=!1,xt=!1),he&&($e.value>0||Tt===""||R(de,$e.type)||R(ne,$e.type))&&(Ge+="-",he=!1),$.type==="milliseconds"&&$e.formattedValueMS?Ge+=$e.formattedValueMS:Ge+=$e.formattedValue,De||(Ge+=$.text),Ge}),Jt.join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function te(){var H=this.duration,P=function(Ie){return H._data[Ie]},G=R(this.types,P),ce=O(this.types,P);switch(G){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(G===ce)return"d __";case"weeks":return G===ce?"w __":(this.trim===null&&(this.trim="both"),"w __, d __, h __");case"months":if(G===ce)return"M __";case"years":return G===ce?"y __":(this.trim===null&&(this.trim="both"),"y __, M __, d __");default:return this.trim===null&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function ue(H){if(!H)throw"Moment Duration Format init cannot find moment instance.";H.duration.format=ee,H.duration.fn.format=re,H.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:te,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},H.updateLocale("en",w)}var fe=function(H,P,G){return H.toLocaleString(P,G)};r=Y()&&Q(fe),i=r&&ae(fe);var se=function(H,P,G){if(typeof window<"u"&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(P,G).format(H)};return o=Q(se),s=o&&ae(se),ue(n),ue})}(Jo)),Jo.exports}tb();const nb=["true","t","yes","y","on","1"],rb=["false","f","no","n","off","0"];function Et(e,t=""){return{kind:J.Text,readonly:!0,allowOverlay:!0,data:e,displayData:e,errorDetails:t,isError:!0,style:"faded"}}function pi(e){return Object.hasOwn(e,"isError")&&e.isError}function ib(e){return Object.hasOwn(e,"tooltip")&&e.tooltip!==""}function Ca(e){return Object.hasOwn(e,"isMissingValue")&&e.isMissingValue}function Ds(e=!1){return e?{kind:J.Loading,allowOverlay:!1,isMissingValue:!0,copyData:""}:{kind:J.Loading,allowOverlay:!1,copyData:""}}function ob(e,t){const n=t?"faded":"normal";return{kind:J.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function Os(e){return{id:e.id,title:e.title,hasMenu:!1,menuIcon:"dots",themeOverride:e.themeOverride,icon:e.icon,group:e.group,...e.isStretched&&!e.isPinned&&{grow:1},...e.width&&{width:e.width}}}function fo(e,t){return He(e)?t||{}:He(t)?e||{}:Pc(e,t)}function Nd(e){if(He(e))return[];if(typeof e=="number"||typeof e=="boolean")return[e];if(typeof e=="string"){if(e==="")return[];if(e.trim().startsWith("[")&&e.trim().endsWith("]"))try{return JSON.parse(e)}catch{return[e]}else return e.split(",")}try{const t=JSON.parse(JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r));return Array.isArray(t)?t.map(n=>["string","number","boolean","null"].includes(typeof n)?n:mt(n)):[mt(t)]}catch{return[mt(e)]}}function ab(e){return e&&e.startsWith("{")&&e.endsWith("}")}function mt(e){try{try{return Qv(e)}catch{return JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r)}}catch{return`[${typeof e}]`}}function Bd(e){if(He(e))return null;if(typeof e=="boolean")return e;const t=mt(e).toLowerCase().trim();if(t==="")return null;if(nb.includes(t))return!0;if(rb.includes(t))return!1}function oo(e){if(He(e))return null;if(Array.isArray(e))return NaN;if(typeof e=="string"){if(e.trim().length===0)return null;try{const t=Yi.unformat(e.trim());if(lt(t))return t}catch{}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function la(e){if(He(e))return"";if(typeof e=="string")return e;try{return JSON.stringify(e,(t,n)=>typeof n=="bigint"?Number(n):n)}catch{return mt(e)}}function sb(e){if(e===0||Math.abs(e)>=1e-4)return 4;const n=e.toExponential().split("e");return Math.abs(parseInt(n[1],10))}function Vr(e,t={}){const n=navigator.languages;try{return new Intl.NumberFormat(n,t).format(e)}catch(r){if(r instanceof RangeError)return new Intl.NumberFormat(void 0,t).format(e);throw r}}function ua(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":He(t)||t===""?lt(n)?(n===0&&(e=Math.round(e)),Yi(e).format({thousandSeparated:!1,mantissa:n,trimMantissa:!1})):Yi(e).format({thousandSeparated:!1,mantissa:sb(e),trimMantissa:!0}):t==="plain"?Yi(e).format({thousandSeparated:!1,mantissa:20,trimMantissa:!0}):t==="localized"?Vr(e,{minimumFractionDigits:n??void 0,maximumFractionDigits:n??void 0}):t==="percent"?Vr(e,{style:"percent",minimumFractionDigits:lt(n)?Math.max(n-2,0):0,maximumFractionDigits:lt(n)?Math.max(n-2,0):2}):t==="dollar"?Vr(e,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol",minimumFractionDigits:n??2,maximumFractionDigits:n??2}):t==="euro"?Vr(e,{style:"currency",currency:"EUR",minimumFractionDigits:n??2,maximumFractionDigits:n??2}):t==="yen"?Vr(e,{style:"currency",currency:"JPY",minimumFractionDigits:n??0,maximumFractionDigits:n??0}):["compact","scientific","engineering"].includes(t)?Vr(e,{notation:t}):t==="accounting"?Yi(e).format({thousandSeparated:!0,negative:"parenthesis",mantissa:n??2,trimMantissa:!1}):t==="bytes"?Vr(e,{notation:"compact",style:"unit",unit:"byte",unitDisplay:"narrow",maximumFractionDigits:1}).replace("BB","GB"):bg.sprintf(t,e)}function Bu(e,t,n="datetime"){if(t==="localized"){const r=navigator.languages,i=n==="time"?void 0:"medium",o=n==="date"?void 0:"medium";try{return new Intl.DateTimeFormat(r,{dateStyle:i,timeStyle:o}).format(e.toDate())}catch(s){if(s instanceof RangeError)return new Intl.DateTimeFormat(void 0,{dateStyle:i,timeStyle:o}).format(e.toDate());throw s}}else{if(t==="distance")return e.fromNow();if(t==="calendar")return e.calendar();if(t==="iso8601")return n==="date"?e.format("YYYY-MM-DD"):n==="time"?e.format("HH:mm:ss.SSS[Z]"):e.toISOString()}return e.format(t)}function Wo(e){if(He(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if(typeof e=="string"&&e.trim().length===0)return null;try{const t=Number(e);if(!isNaN(t)){let n=t;t>=10**18?n=t/1e3**3:t>=10**15?n=t/1e3**2:t>=10**12&&(n=t/1e3);const r=$r.unix(n).utc();if(r.isValid())return r.toDate()}if(typeof e=="string"){const n=$r.utc(e);if(n.isValid())return n.toDate();const r=$r.utc(e,[$r.HTML5_FMT.TIME_MS,$r.HTML5_FMT.TIME_SECONDS,$r.HTML5_FMT.TIME]);if(r.isValid())return r.toDate()}}catch{return}}function Wd(e){if(e%1===0)return 0;let t=e.toString();return t.indexOf("e")!==-1&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),t.indexOf(".")===-1?0:t.split(".")[1].length}function lb(e,t){if(!Number.isFinite(e))return e;if(t<=0)return Math.trunc(e);const n=10**t,r=e*n,i=Number.EPSILON*Math.abs(r)*10;return Math.trunc(r+Math.sign(r)*i)/n}const ub=new RegExp(/(\r\n|\n|\r)/gm);function ao(e){return e.indexOf(`
`)!==-1?e.replace(ub," "):e}function cb(e,t){if(He(t))return"";try{const n=t.match(e);return n&&n[1]!==void 0?decodeURIComponent(n[1].replace(/\+/g,"%20")):t}catch{return t}}const db=Un("div",{target:"e1ugbj0f0"})(({theme:e})=>({overflowY:"auto",padding:e.spacing.sm,".react-json-view .copy-icon svg":{fontSize:"0.9em !important",marginRight:`${e.spacing.threeXS} !important`,verticalAlign:"middle !important"}})),Ud=({jsonValue:e,theme:t})=>{let n;if(e)try{n=typeof e=="string"?qa.parse(e):qa.parse(qa.stringify(e))}catch{n=void 0}return He(n)?Ue(qr,{highlight:!0,autoFocus:!1,disabled:!0,value:la(e)??"",onChange:()=>{}}):Ue(db,{"data-testid":"stJsonColumnViewer",children:Ue(Mh,{src:n,collapsed:2,theme:Rh(t.bgCell)>.5?"rjv-default":"monokai",displayDataTypes:!1,displayObjectSize:!1,name:!1,enableClipboard:!0,style:{fontFamily:t.fontFamily,fontSize:t.baseFontStyle,backgroundColor:t.bgCell,whiteSpace:"pre-wrap"}})})},fb=e=>{const t=e.theme,n=e.value.data;return Ue(Ud,{jsonValue:n.value||n.displayValue,theme:t})},hb=e=>{const t=e.theme,n=e.value;return Ue(Ud,{jsonValue:n.data,theme:t})},gb={kind:J.Custom,isMatch:e=>e.data.kind==="json-cell",draw:(e,t)=>{const{value:n,displayValue:r}=t.data;return qs(e,r??la(n)??"",t.contentAlign),!0},measure:(e,t,n)=>{const{value:r,displayValue:i}=t.data,o=i??la(r)??"";return(o?e.measureText(o).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:fb})},qd="line_chart",mb="area_chart",Gd="bar_chart";function Qs(e,t,n){const r=fo({y_min:null,y_max:null},t.columnTypeOptions),i={kind:J.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[r.y_min??0,r.y_max??1]}};return{...t,kind:e,typeIcon:e===qd?":material/show_chart:":e===Gd?":material/bar_chart:":":material/area_chart:",sortMode:"default",isEditable:!1,getCell(o){if(He(o))return Ds();const s=Nd(o),a=[];let l=[];if(s.length===0)return Ds();let u=Number.MIN_SAFE_INTEGER,c=Number.MAX_SAFE_INTEGER;for(let h=0;h<s.length;h++){const m=oo(s[h]);if(Number.isNaN(m)||He(m))return Et(mt(s),`The value cannot be interpreted as a numeric array. ${mt(m)} is not a number.`);m>u&&(u=m),m<c&&(c=m),a.push(m)}let d,g;if(s.length===1){let h;u<=0?h=u===0?1:0:h=u,d=r.y_max??h,g=r.y_min??(u>=0?0:u)}else d=r.y_max??u,g=r.y_min??c;return He(g)||He(d)||Number.isNaN(g)||Number.isNaN(d)||g>=d?Et("Invalid min/max y-axis configuration",`The y_min (${g}) and y_max (${d}) configuration options must be valid numbers.`):(a.length>0&&(u>d||c<g)?l=a.map(h=>u-c===0?u>(d||1)?d:g:(d-g)*((h-c)/(u-c))+g):l=a,{...i,copyData:a.join(","),data:{...i.data,values:l,displayValues:a.map(h=>ua(h)),yAxis:[g,d]},isMissingValue:He(o)})},getCellValue(o){return o.kind===J.Loading||o.data?.values===void 0?null:o.data?.values}}}function Xd(e){return Qs(qd,e,"line")}Xd.isEditableType=!1;function Yd(e){return Qs(Gd,e,"bar")}Yd.isEditableType=!1;function jd(e){return Qs(mb,e,"area")}jd.isEditableType=!1;function el(e){const t={kind:J.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:"normal"};return{...e,kind:"checkbox",typeIcon:":material/check_box:",sortMode:"default",getCell(n){let r=null;return r=Bd(n),r===void 0?Et(mt(n),"The value cannot be interpreted as boolean."):{...t,data:r,isMissingValue:He(r)}},getCellValue(n){return n.data===void 0?null:n.data}}}el.isEditableType=!0;function Wu(e,t){return t.startsWith("+")||t.startsWith("-")?e=e.utcOffset(t,!1):e=e.tz(t),e}function tl(e,t,n,r,i,o,s){const a=fo({format:n,step:r,timezone:s},t.columnTypeOptions);let l;if(lt(a.timezone))try{l=Wu(Ol(),a.timezone)?.utcOffset()||void 0}catch{}let u;lt(a.min_value)&&(u=Wo(a.min_value)||void 0);let c;lt(a.max_value)&&(c=Wo(a.max_value)||void 0);const d={kind:J.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isPinned?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:a.step?.toString()||"1",format:i,min:u,max:c}},g=h=>{const m=Wo(h);return m===null?!t.isRequired:!(m===void 0||lt(u)&&o(m)<o(u)||lt(c)&&o(m)>o(c))};return{...t,kind:e,typeIcon:e==="date"?":material/calendar_month:":e==="time"?":material/access_time:":":material/calendar_today:",sortMode:"default",validateInput:g,getCell(h,m){if(m===!0){const x=g(h);if(x===!1)return Et(mt(h),"Invalid input.");x instanceof Date&&(h=x)}const p=Wo(h);let v="",w="",b=l;if(p===void 0)return Et(mt(h),"The value cannot be interpreted as a datetime object.");if(p!==null){let x=Ol.utc(p);if(!x.isValid())return Et(mt(p),`Invalid moment date. This should never happen. Please report this bug. 
Error: ${x.toString()}`);if(a.timezone){try{x=Wu(x,a.timezone)}catch(O){return Et(x.toISOString(),`Failed to adjust to the provided timezone: ${a.timezone}. 
Error: ${O}`)}b=x.utcOffset()}try{w=Bu(x,a.format||n,e)}catch(O){return Et(x.toISOString(),`Failed to format the date for rendering with: ${a.format}. 
Error: ${O}`)}v=Bu(x,n,e)}return{...d,copyData:v,isMissingValue:He(p),data:{...d.data,date:p,displayDate:w,timezoneOffset:b}}},getCellValue(h){return He(h?.data?.date)?null:o(h.data.date)}}}function nl(e){let t="YYYY-MM-DD HH:mm:ss";e.columnTypeOptions?.step>=60?t="YYYY-MM-DD HH:mm":e.columnTypeOptions?.step<1&&(t="YYYY-MM-DD HH:mm:ss.SSS");const n=Ih(e.arrowType),r=lt(n)||lt(e?.columnTypeOptions?.timezone);return tl("datetime",e,r?t+"Z":t,1,"datetime-local",i=>r?i.toISOString():i.toISOString().replace("Z",""),n)}nl.isEditableType=!0;function rl(e){let t="HH:mm:ss";return e.columnTypeOptions?.step>=60?t="HH:mm":e.columnTypeOptions?.step<1&&(t="HH:mm:ss.SSS"),tl("time",e,t,1,"time",n=>n.toISOString().split("T")[1].replace("Z",""))}rl.isEditableType=!0;function il(e){return tl("date",e,"YYYY-MM-DD",1,"date",t=>t.toISOString().split("T")[0])}il.isEditableType=!0;function Kd(e){const t={kind:J.Image,data:[],displayData:[],readonly:!0,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:"normal"};return{...e,kind:"image",typeIcon:":material/image:",sortMode:"default",isEditable:!1,getCell(n){const r=lt(n)?[mt(n)]:[];return{...t,data:r,isMissingValue:!lt(n),displayData:r}},getCellValue(n){return n.data===void 0||n.data.length===0?null:n.data[0]}}}Kd.isEditableType=!1;function Zd(e){const t={kind:J.Custom,allowOverlay:!0,contentAlign:e.contentAlignment,readonly:!0,style:e.isPinned?"faded":"normal",copyData:"",data:{kind:"json-cell",value:""}};return{...e,kind:"json",typeIcon:":material/code_blocks:",sortMode:"default",isEditable:!1,getCell(n){try{const r=lt(n)?ao(la(n)):"";return{...t,copyData:r,isMissingValue:He(n),data:{...t.data,value:n,displayValue:r}}}catch(r){return Et(mt(n),`The value cannot be interpreted as a JSON string. Error: ${r}`)}},getCellValue(n){return n.data?.value??null}}}Zd.isEditableType=!1;function Jd(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(l){n=`Invalid validate regex: ${t.validate}.
Error: ${l}`}let r=!1,i=t.display_text,o;if(!He(i)){if(i.startsWith(":material/")&&Eh(i))i=Th(i).icon,r=!0;else if(i.includes("(")&&i.includes(")"))try{o=new RegExp(i,"us")}catch{o=void 0}}const s={kind:J.Uri,readonly:!e.isEditable,allowOverlay:!r,contentAlign:e.contentAlignment??(r?"center":void 0),style:"normal",hoverEffect:!0,data:"",displayData:"",copyData:"",...r&&{themeOverride:{fontFamily:Dh.iconFont,linkColor:void 0}}},a=l=>{if(He(l))return!e.isRequired;const u=mt(l);return!(t.max_chars&&u.length>t.max_chars||n instanceof RegExp&&n.test(u)===!1)};return{...e,kind:"link",typeIcon:":material/link:",sortMode:"default",validateInput:a,getCell(l,u){if(He(l))return{...s,data:null,isMissingValue:!0,onClickUri:()=>{},themeOverride:void 0};const c=l;if(typeof n=="string")return Et(mt(c),n);if(u&&a(c)===!1)return Et(mt(c),"Invalid input.");let d="";return c&&(o!==void 0?d=cb(o,c):d=i||c),{...s,data:c,displayData:d,isMissingValue:He(c),onClickUri:g=>{window.open(c.startsWith("www.")?`https://${c}`:c,"_blank","noopener,noreferrer"),g.preventDefault()},copyData:c}},getCellValue(l){return He(l.data)?null:l.data}}}Jd.isEditableType=!0;function ol(e){const t={kind:J.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal"};return{...e,kind:"list",sortMode:"default",typeIcon:":material/list:",isEditable:!1,getCell(n){const r=He(n)?[]:Nd(n);return{...t,data:r,isMissingValue:He(n),copyData:He(n)?"":mt(r.map(i=>typeof i=="string"&&i.includes(",")?i.replace(/,/g," "):i))}},getCellValue(n){return He(n.data)||Ca(n)?null:n.data}}}ol.isEditableType=!1;function al(e){const t=fo({step:_c(e.arrowType)?1:void 0,min_value:Oh(e.arrowType)?0:void 0},e.columnTypeOptions),n=!t.format&&(Ph(e.arrowType)||_h(e.arrowType)),r=He(t.min_value)||t.min_value<0,i=lt(t.step)&&!Number.isNaN(t.step)?Wd(t.step):void 0,o={kind:J.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||n?"left":"right",style:e.isPinned?"faded":"normal",allowNegative:r,fixedDecimals:i,thousandSeparator:""},s=a=>{let l=oo(a);if(He(l))return!e.isRequired;if(Number.isNaN(l))return!1;let u=!1;return lt(t.max_value)&&l>t.max_value&&(l=t.max_value,u=!0),lt(t.min_value)&&l<t.min_value?!1:u?l:!0};return{...e,kind:"number",sortMode:"smart",typeIcon:":material/tag:",validateInput:s,getCell(a,l){if(l===!0){const d=s(a);if(d===!1)return Et(mt(a),"Invalid input.");typeof d=="number"&&(a=d)}let u=oo(a),c="";if(lt(u)){if(Number.isNaN(u))return Et(mt(a),"The value cannot be interpreted as a number.");if(lt(i)&&(u=lb(u,i)),Number.isInteger(u)&&!Number.isSafeInteger(u))return Et(mt(a),"The value is larger than the maximum supported integer values in number columns (2^53).");try{n?c=Cs(u,e.arrowType):c=ua(u,t.format,i)}catch(d){return Et(mt(u),lt(t.format)?`Failed to format the number based on the provided format configuration: (${t.format}). Error: ${d}`:`Failed to format the number. Error: ${d}`)}}return{...o,data:u,displayData:c,isMissingValue:He(u),copyData:He(u)?"":mt(u)}},getCellValue(a){return a.data===void 0?null:a.data}}}al.isEditableType=!0;function so(e){const t={kind:J.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!0,style:e.isPinned?"faded":"normal"};return{...e,kind:"object",sortMode:"default",typeIcon:":material/data_object:",isEditable:!1,getCell(n){try{const r=lt(n)?mt(n):null,i=lt(r)?ao(r):"";return{...t,data:r,displayData:i,isMissingValue:He(n)}}catch(r){return Et(mt(n),`The value cannot be interpreted as a string. Error: ${r}`)}},getCellValue(n){return n.data===void 0?null:n.data}}}so.isEditableType=!1;function Qd(e){const t=_c(e.arrowType),n=fo({min_value:0,max_value:t?100:1,format:t?"%3d%%":"percent",step:t?1:void 0},e.columnTypeOptions),r=He(n.step)||Number.isNaN(n.step)?void 0:Wd(n.step);let i;try{i=ua(n.max_value,n.format,r)}catch{i=mt(n.max_value)}const o={kind:J.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,readonly:!0,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step??.01,value:n.min_value,label:String(n.min_value),measureLabel:i}};return{...e,kind:"progress",sortMode:"smart",typeIcon:":material/commit:",isEditable:!1,getCell(s){if(He(s))return Ds();if(He(n.min_value)||He(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return Et("Invalid min/max parameters",`The min_value (${n.min_value}) and max_value (${n.max_value}) parameters must be valid numbers.`);if(lt(n.step)&&Number.isNaN(n.step))return Et("Invalid step parameter",`The step parameter (${n.step}) must be a valid number.`);const a=oo(s);if(Number.isNaN(a)||He(a))return Et(mt(s),"The value cannot be interpreted as a number.");if(Number.isInteger(a)&&!Number.isSafeInteger(a))return Et(mt(s),"The value is larger than the maximum supported integer values in number columns (2^53).");let l="";try{l=ua(a,n.format,r)}catch(c){return Et(mt(a),lt(n.format)?`Failed to format the number based on the provided format configuration: (${n.format}). Error: ${c}`:`Failed to format the number. Error: ${c}`)}const u=Math.min(n.max_value,Math.max(n.min_value,a));return{...o,isMissingValue:He(s),copyData:String(a),data:{...o.data,value:u,label:l,measureLabel:l.length>i.length?l:i}}},getCellValue(s){return s.kind===J.Loading||s.data?.value===void 0?null:s.data?.value}}}Qd.isEditableType=!1;function sl(e){let t="string";const n=fo({options:Fc(e.arrowType)?[!0,!1]:e.arrowType.categoricalOptions??[]},e.columnTypeOptions),r=new Set(n.options.map(o=>typeof o));r.size===1&&(r.has("number")||r.has("bigint")?t="number":r.has("boolean")&&(t="boolean"));const i={kind:J.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isPinned?"faded":"normal",data:{kind:"dropdown-cell",allowedValues:[...e.isRequired!==!0?[null]:[],...n.options.filter(o=>o!==null&&o!=="").map(o=>mt(o))],value:""}};return{...e,kind:"selectbox",sortMode:"default",typeIcon:":material/arrow_drop_down_circle:",getCell(o,s){let a=null;return lt(o)&&o!==""&&(a=mt(o)),s&&!i.data.allowedValues.includes(a)?Et(mt(a),"The value is not part of the allowed options."):{...i,isMissingValue:a===null,copyData:a||"",data:{...i.data,value:a}}},getCellValue(o){return He(o.data?.value)||o.data?.value===""?null:t==="number"?oo(o.data?.value)??null:t==="boolean"?Bd(o.data?.value)??null:o.data?.value}}}sl.isEditableType=!0;function ll(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(o){n=`Invalid validate regex: ${t.validate}.
Error: ${o}`}const r={kind:J.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!e.isEditable,style:e.isPinned?"faded":"normal"},i=o=>{if(He(o))return!e.isRequired;let s=mt(o),a=!1;return t.max_chars&&s.length>t.max_chars&&(s=s.slice(0,t.max_chars),a=!0),n instanceof RegExp&&n.test(s)===!1?!1:a?s:!0};return{...e,kind:"text",sortMode:"default",typeIcon:":material/notes:",validateInput:i,getCell(o,s){if(typeof n=="string")return Et(mt(o),n);if(s){const a=i(o);if(a===!1)return Et(mt(o),"Invalid input.");typeof a=="string"&&(o=a)}try{const a=lt(o)?mt(o):null,l=lt(a)?ao(a):"";return{...r,isMissingValue:He(a),data:a,displayData:l}}catch(a){return Et("Incompatible value",`The value cannot be interpreted as string. Error: ${a}`)}},getCellValue(o){return o.data===void 0?null:o.data}}}ll.isEditableType=!0;const Uu=Un("img",{target:"e3b94od0"})({maxWidth:"100%",maxHeight:"37.5rem",objectFit:"scale-down"}),pb=({urls:e})=>{const t=e&&e.length>0?e[0]:"";return t.startsWith("http")?Ue("a",{href:t,target:"_blank",rel:"noreferrer noopener",children:Ue(Uu,{src:t})}):Ue(Uu,{src:t})},qu=new Map(Object.entries({object:so,text:ll,checkbox:el,selectbox:sl,list:ol,number:al,link:Jd,datetime:nl,date:il,time:rl,line_chart:Xd,bar_chart:Yd,area_chart:jd,image:Kd,progress:Qd,json:Zd})),vb=[gb];var bb=Lc();const wb=lr(bb);var fs,Gu;function yb(){if(Gu)return fs;Gu=1;var e=Fh(),t=Lh(),n=Ah(),r=Lc(),i=Hh(),o=zh(),s=Vh(),a=$h(),l="[object Map]",u="[object Set]",c=Object.prototype,d=c.hasOwnProperty;function g(h){if(h==null)return!0;if(i(h)&&(r(h)||typeof h=="string"||typeof h.splice=="function"||o(h)||a(h)||n(h)))return!h.length;var m=t(h);if(m==l||m==u)return!h.size;if(s(h))return!e(h).length;for(var p in h)if(d.call(h,p))return!1;return!0}return fs=g,fs}var Cb=yb();const Sb=lr(Cb);function hs(e,t,n){if(!n.includes(t))return;const r=new RegExp(`${e}[,\\s].*{(?:[^}]*[\\s;]{1})?${t}:\\s*([^;}]+)[;]?.*}`,"gm");n=n.replace(/{/g," {");const i=r.exec(n);if(i)return i[1].trim()}function xb(e,t,n){const r={};if(!n.includes(t))return e;const i=hs(t,"color",n);i&&(r.textDark=i,e.kind===J.Bubble&&(r.textBubble=i),e.kind===J.Uri&&(r.linkColor=i));const o=hs(t,"background-color",n);o&&(r.bgCell=o),o==="yellow"&&i===void 0&&(r.textDark="#31333F");const s=hs(t,"font-weight",n);return s&&(r.baseFontStyle=`${s} ${tg.sm}`),r?{...e,themeOverride:r}:e}function kb(e){return Gh(e)||Xh(e)?ll:Yh(e)?nl:Ac(e)?rl:jh(e)?il:Kh(e)||Zh(e)?so:Fc(e)?el:Jh(e)?al:Qh(e)?sl:eg(e)?ol:so}function ef(e){const t=e.length>0?e[e.length-1]:"",n=e.length>1?e.slice(0,-1).filter(r=>r!=="").join(" / "):void 0;return{title:t,group:n}}function ul(e){return{group:void 0,isEditable:!1,isIndex:!1,isPinned:!1,isHidden:!1,isStretched:!1,...e}}function Mb(e,t){const n=e.columnNames.map(a=>a[t]),{title:r,group:i}=ef(n),o=e.columnTypes[t];let s=!0;return qh(o)&&(s=!1),ul({id:`_index-${t}`,indexNumber:t,name:r,title:r,group:i,isEditable:s,arrowType:o,isIndex:!0,isPinned:!0})}function Rb(e,t){const n=e.columnNames.map(s=>s[t]),{title:r,group:i}=ef(n),o=e.columnTypes[t];return ul({id:`_column-${r}-${t}`,indexNumber:t,name:r,isEditable:!0,title:r,arrowType:o,group:i})}function tf(){return ul({id:"_empty-index",indexNumber:0,title:"",name:"",isEditable:!1,isIndex:!0,isPinned:!0,arrowType:{type:ng.INDEX,arrowField:new Nh("",new Bh,!0),pandasType:void 0}})}function Xu(e){const t=[],{dimensions:n}=e,r=n.numIndexColumns,i=n.numDataColumns;if(r===0&&i===0)return t.push(tf()),t;for(let o=0;o<r;o++)t.push(Mb(e,o));for(let o=0;o<i;o++)t.push(Rb(e,o+r));return t}function Ib(e,t,n,r=void 0){let i;if(e.kind==="object"||e.kind==="json")i=e.getCell(lt(t.content)?ao(Cs(t.content,t.contentType)):null);else if(["time","date","datetime"].includes(e.kind)&&lt(t.content)&&(typeof t.content=="number"||typeof t.content=="bigint")){let o;Ac(t.contentType)&&lt(t.field?.type?.unit)?o=Wh(t.content,t.field):o=$r.utc(Number(t.content)).toDate(),i=e.getCell(o)}else if(Uh(t.contentType)){const o=He(t.content)?null:Cs(t.content,t.contentType);i=e.getCell(o)}else i=e.getCell(t.content);if(pi(i))return i;if(!e.isEditable){if(n&&lt(n?.displayContent)){const o=ao(n.displayContent);i.kind===J.Text?i={...i,displayData:o}:i.kind===J.Number&&He(e.columnTypeOptions?.format)?i={...i,displayData:o}:i.kind===J.Uri&&He(e.columnTypeOptions?.display_text)?i={...i,displayData:o}:i.kind===J.Custom&&i.data?.kind==="date-picker-cell"&&He(e.columnTypeOptions?.format)&&(i={...i,data:{...i.data,displayDate:o}})}r&&n?.cssId&&(i=xb(i,n.cssId,r))}return i}const Qo="_index",Yu="_pos:",ju={small:75,medium:200,large:400},nf=pa.getLogger("useColumnLoader");function Eb(e){if(!He(e)){if(typeof e=="number")return e;if(e in ju)return ju[e]}}const Uo=(e,t)=>vg(e,t,(r,i)=>{if(wb(i))return i});function Ku(e,t){if(!t)return e;let n={};return e.isIndex&&t.has(Qo)&&(n=Uo(n,t.get(Qo)??{})),t.has(`${Yu}${e.indexNumber}`)&&(n=Uo(n,t.get(`${Yu}${e.indexNumber}`)??{})),t.has(e.name)&&e.name!==Qo&&(n=Uo(n,t.get(e.name)??{})),t.has(e.id)&&(n=Uo(n,t.get(e.id)??{})),Sb(n)?e:Pc({...e},{title:n.label,width:Eb(n.width),isEditable:lt(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isPinned:n.pinned,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help})}function Tb(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return nf.error(t),new Map}}function Zu(e){const t=e.columnTypeOptions?.type;let n;return lt(t)&&(qu.has(t)?n=qu.get(t):nf.warn(`Unknown column type configured in column configuration: ${t}`)),He(n)&&(n=kb(e.arrowType)),n}function Db(e,t,n,r){const i=Wr(),o=f.useMemo(()=>Tb(e.columns),[e.columns]),[s,a]=f.useState(o);f.useEffect(()=>{a(o)},[o]);const l=e.useContainerWidth||lt(e.width)&&e.width>0,u=lt(e.rowHeight)&&e.rowHeight>an("4rem"),c=f.useMemo(()=>Xu(t).map(g=>{let h={...g,...Ku(g,s),isStretched:l};const m=Zu(h);return(e.editingMode===wn.EditingMode.READ_ONLY||n||m.isEditableType===!1)&&(h={...h,isEditable:!1}),e.editingMode!==wn.EditingMode.READ_ONLY&&h.isEditable==!0&&(h={...h,icon:"editable"},h.isRequired&&e.editingMode===wn.EditingMode.DYNAMIC&&(h={...h,isHidden:!1})),m(h,i)}),[t,s,l,e.editingMode,n,i]);return{columns:f.useMemo(()=>{const g=Xu(t).map(v=>{let w={...v,...Ku(v,s),isStretched:l,isWrappingAllowed:u};const b=Zu(w);return(e.editingMode===wn.EditingMode.READ_ONLY||n||b.isEditableType===!1)&&(w={...w,isEditable:!1}),e.editingMode!==wn.EditingMode.READ_ONLY&&w.isEditable==!0&&(w={...w,icon:"editable"},w.isRequired&&e.editingMode===wn.EditingMode.DYNAMIC&&(w={...w,isHidden:!1})),b(w,i)}).filter(v=>!v.isHidden),h=[],m=[];r?.length?(g.forEach(v=>{v.isIndex&&!r.includes(v.name)&&!r.includes(v.id)&&v.isPinned!==!1&&h.push(v)}),r.forEach(v=>{const w=g.find(b=>b.name===v||b.id===v);w&&(w.isPinned?h.push(w):m.push(w))})):g.forEach(v=>{v.isPinned?h.push(v):m.push(v)});const p=[...h,...m];return p.length>0?p:[so(tf())]},[t,s,u,l,n,e.editingMode,r,i]),allColumns:c,setColumnConfigMapping:a}}function to(e){return e.isIndex?Qo:He(e.name)?"":e.name}class qo{constructor(t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=t}toJson(t){const n=new Map;t.forEach(o=>{n.set(o.indexNumber,o)});const r={edited_rows:{},added_rows:[],deleted_rows:[]};return this.editedCells.forEach((o,s,a)=>{const l={};o.forEach((u,c)=>{const d=n.get(c);d&&(l[to(d)]=d.getCellValue(u))}),r.edited_rows[s]=l}),this.addedRows.forEach(o=>{const s={};let a=!1;o.forEach((l,u,c)=>{const d=n.get(u);if(d){const g=d.getCellValue(l);d.isRequired&&d.isEditable&&Ca(l)&&(a=!0),lt(g)&&(s[to(d)]=g)}}),a||r.added_rows.push(s)}),r.deleted_rows=this.deletedRows,JSON.stringify(r,(o,s)=>s===void 0?null:s)}fromJson(t,n){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const r=JSON.parse(t),i=new Map;n.forEach(s=>{i.set(s.indexNumber,s)});const o=new Map;n.forEach(s=>{o.set(to(s),s)}),Object.keys(r.edited_rows).forEach(s=>{const a=Number(s),l=r.edited_rows[s];Object.keys(l).forEach(u=>{const c=l[u],d=o.get(u);if(d){const g=d.getCell(c);g&&(this.editedCells.has(a)||this.editedCells.set(a,new Map),this.editedCells.get(a)?.set(d.indexNumber,g))}})}),r.added_rows.forEach(s=>{const a=new Map;n.forEach(l=>{a.set(l.indexNumber,l.getCell(null))}),Object.keys(s).forEach(l=>{const u=s[l],c=o.get(l);if(c){const d=c.getCell(u);d&&a.set(c.indexNumber,d)}}),this.addedRows.push(a)}),this.deletedRows=r.deleted_rows}isAddedRow(t){return t>=this.numRows}getCell(t,n){if(this.isAddedRow(n))return this.addedRows[n-this.numRows].get(t);const r=this.editedCells.get(n);if(r!==void 0)return r.get(t)}setCell(t,n,r){if(this.isAddedRow(n)){if(n-this.numRows>=this.addedRows.length)return;this.addedRows[n-this.numRows].set(t,r)}else this.editedCells.get(n)===void 0&&this.editedCells.set(n,new Map),this.editedCells.get(n).set(t,r)}addRow(t){this.addedRows.push(t)}deleteRows(t){t.sort((n,r)=>r-n).forEach(n=>{this.deleteRow(n)})}deleteRow(t){if(!(He(t)||t<0)){if(this.isAddedRow(t)){this.addedRows.splice(t-this.numRows,1);return}this.deletedRows.includes(t)||(this.deletedRows.push(t),this.deletedRows=this.deletedRows.sort((n,r)=>n-r)),this.editedCells.delete(t)}}getOriginalRowIndex(t){let n=t;for(let r=0;r<this.deletedRows.length&&!(this.deletedRows[r]>n);r++)n+=1;return n}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}}const lo=({columnId:e,columnConfigMapping:t,updatedProps:n})=>{const r=new Map(t),i=r.get(e),o={...i||{},...n||{}};return(i?.type_config||n?.type_config)&&(o.type_config={...i?.type_config||{},...n?.type_config||{}}),r.set(e,o),r};function Ob(e){return{changeColumnFormat:f.useCallback((n,r)=>{e(i=>lo({columnId:n,columnConfigMapping:i,updatedProps:{type_config:{format:r}}}))},[e])}}function Pb(e,t,n,r,i,o){const s=f.useMemo(()=>e.filter(c=>c.isPinned).reduce((c,d)=>c+(d.width??r*2),0)>n*.6,[e,n,r]),a=t||s?0:e.filter(c=>c.isPinned).length,l=f.useCallback(c=>{o(d=>lo({columnId:c,columnConfigMapping:d,updatedProps:{pinned:!1}})),i(!0,!1)},[i,o]);return{pinColumn:f.useCallback(c=>{o(d=>lo({columnId:c,columnConfigMapping:d,updatedProps:{pinned:!0}})),i(!0,!1)},[i,o]),unpinColumn:l,freezeColumns:a}}function _b(e,t,n,r,i){return{onColumnMoved:f.useCallback((s,a)=>{const l=[...e],[u]=l.splice(s,1);l.splice(a,0,u),a<t&&!u.isPinned?n(u.id):a>=t&&u.isPinned&&r(u.id),i(l.map(c=>c.id))},[e,t,n,r,i])}}function Fb(e){const[t,n]=f.useState(()=>new Map),r=f.useCallback((o,s,a,l)=>{o.id&&n(new Map(t).set(o.id,l))},[t]);return{columns:f.useMemo(()=>e.map(o=>o.id&&t.has(o.id)&&t.get(o.id)!==void 0?{...o,width:t.get(o.id),grow:0}:o),[e,t]),onColumnResize:r}}function Lb(e){switch(e.kind){case J.Number:return e.data?.toString()??"";case J.Boolean:return e.data?.toString()??"";case J.Markdown:case J.RowID:case J.Text:case J.Uri:return e.data??"";case J.Bubble:case J.Image:return e.data.join("");case J.Drilldown:return e.data.map(t=>t.text).join("");case J.Protected:case J.Loading:return"";case J.Custom:return e.copyData}}function Ju(e){if(typeof e=="number")return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function Ab(e,t){return e=Ju(e),t=Ju(t),typeof e=="string"&&typeof t=="string"?e.localeCompare(t):typeof e=="number"&&typeof t=="number"?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}function Hb(e,t){return e>t?1:e===t?0:-1}function zb(e){const{sort:t,rows:n,getCellContent:r}=e,i=f.useMemo(()=>t===void 0?[]:Array.isArray(t)?t:[t],[t]),o=f.useMemo(()=>i.map(u=>{const c=e.columns.findIndex(d=>u.column===d||d.id!==void 0&&u.column.id===d.id);return c===-1?void 0:c}),[i,e.columns]),s=f.useMemo(()=>{const u=i.map((d,g)=>({sort:d,col:o[g]})).filter(d=>d.col!==void 0);if(u.length===0)return;const c=u.map(()=>new Array(n));for(let d=0;d<u.length;d++){const{col:g}=u[d],h=[g,0];for(let m=0;m<n;m++)h[1]=m,c[d][m]=Lb(r(h))}return kr(n).sort((d,g)=>{for(let h=0;h<u.length;h++){const{sort:m}=u[h],p=c[h][d],v=c[h][g];let w;if(m.mode==="raw"?w=Hb(p,v):m.mode==="smart"?w=Ab(p,v):w=p.localeCompare(v),w!==0)return(m.direction??"asc")==="desc"&&(w=-w),w}return 0})},[r,n,i,o]),a=f.useCallback(u=>s===void 0?u:s[u],[s]),l=f.useCallback(([u,c])=>s===void 0?r([u,c]):(c=s[c],r([u,c])),[r,s]);return s===void 0?{getCellContent:e.getCellContent,getOriginalIndex:a}:{getOriginalIndex:a,getCellContent:l}}function Vb(e,t){return t===void 0?e:e.map(n=>n.id===t.column.id?{...n,title:t.direction==="asc"?`↑ ${n.title}`:`↓ ${n.title}`}:n)}function $b(e,t,n){const[r,i]=f.useState(),{getCellContent:o,getOriginalIndex:s}=zb({columns:t.map(u=>Os(u)),getCellContent:n,rows:e,sort:r}),a=f.useMemo(()=>Vb(t,r),[t,r]),l=f.useCallback((u,c,d)=>{const g=a[u];let h;c==="auto"?(h="asc",r&&r.column.id===g.id&&(r.direction==="asc"?h="desc":h=void 0)):h=c,h===void 0||d&&h===r?.direction?i(void 0):i({column:Os(g),direction:h,mode:g.sortMode})},[r,a]);return{columns:a,sortColumn:l,getOriginalIndex:s,getCellContent:o}}function Nb(e,t){const n=f.useCallback(i=>{t(o=>lo({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!0}})),e(!0,!1)},[e,t]),r=f.useCallback(i=>{t(o=>lo({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!1}})),e(!0,!1)},[e,t]);return{hideColumn:n,showColumn:r}}function Bb(){return{provideEditor:f.useCallback(t=>{if(t.kind===J.Text&&t.readonly&&ab(t.data))return{editor:hb}},[])}}const Wb={kind:J.Custom,isMatch:e=>e.data.kind==="sparkline-cell",needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{const{ctx:n,theme:r,rect:i,hoverAmount:o,hoverX:s}=e;let{values:a,yAxis:l,color:u,graphKind:c="area",displayValues:d,hideAxis:g}=t.data;const[h,m]=l;if(a.length===0)return!0;a=a.map(M=>Math.min(1,Math.max(0,(M-h)/(m-h))));const p=r.cellHorizontalPadding,v=p+i.x,w=i.y+3,b=i.height-6,x=i.width-p*2,O=m-h,R=m<=0?w:h>=0?w+b:w+b*(m/O);if(!g&&h<=0&&m>=0&&(n.beginPath(),n.moveTo(v,R),n.lineTo(v+x,R),n.globalAlpha=.4,n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.globalAlpha=1),c==="bar"){n.beginPath();const M=2,_=(a.length-1)*M,I=(x-_)/a.length;let k=v;for(const A of a){const D=w+b-A*b;n.moveTo(k,R),n.lineTo(k+I,R),n.lineTo(k+I,D),n.lineTo(k,D),k+=I+M}n.fillStyle=t.data.color??r.accentColor,n.fill()}else{a.length===1&&(a=[a[0],a[0]],d&&(d=[d[0],d[0]])),n.beginPath();const M=(i.width-16)/(a.length-1),_=a.map((k,A)=>({x:v+M*A,y:w+b-k*b}));n.moveTo(_[0].x,_[0].y);let I=0;if(_.length>2)for(I=1;I<_.length-2;I++){const k=(_[I].x+_[I+1].x)/2,A=(_[I].y+_[I+1].y)/2;n.quadraticCurveTo(_[I].x,_[I].y,k,A)}if(n.quadraticCurveTo(_[I].x,_[I].y,_[I+1].x,_[I+1].y),n.strokeStyle=u??r.accentColor,n.lineWidth=1+o*.5,n.stroke(),n.lineTo(i.x+i.width-p,R),n.lineTo(i.x+p,R),n.closePath(),c==="area"){n.globalAlpha=.2+.2*o;const k=n.createLinearGradient(0,w,0,w+b*1.4);k.addColorStop(0,u??r.accentColor);const[A,D,C]=ia(u??r.accentColor);k.addColorStop(1,`rgba(${A}, ${D}, ${C}, 0)`),n.fillStyle=k,n.fill(),n.globalAlpha=1}if(s!==void 0&&(c==="line"||c==="area")&&d!==void 0){n.beginPath();const k=Math.min(a.length-1,Math.max(0,Math.round((s-p)/M)));n.moveTo(v+k*M,i.y+1),n.lineTo(v+k*M,i.y+i.height),n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.save(),n.font=`8px ${r.fontFamily}`,n.fillStyle=r.textMedium,n.textBaseline="top",n.fillText(d[k],v,i.y+r.cellVerticalPadding),n.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t};function Qu(e,t,n,r,i,o){if(!(r<=0||i<=0)){if(typeof o=="number"&&o<=0){e.rect(t,n,r,i);return}typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},o.tl=Math.max(0,o.tl),o.tr=Math.max(0,o.tr),o.br=Math.max(0,o.br),o.bl=Math.max(0,o.bl),e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}}function ec(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Ae(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?ec(Object(n),!0).forEach(function(r){ji(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ec(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var Ub=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function qb(e){var t=e.defaultInputValue,n=t===void 0?"":t,r=e.defaultMenuIsOpen,i=r===void 0?!1:r,o=e.defaultValue,s=o===void 0?null:o,a=e.inputValue,l=e.menuIsOpen,u=e.onChange,c=e.onInputChange,d=e.onMenuClose,g=e.onMenuOpen,h=e.value,m=ur(e,Ub),p=f.useState(a!==void 0?a:n),v=or(p,2),w=v[0],b=v[1],x=f.useState(l!==void 0?l:i),O=or(x,2),R=O[0],M=O[1],_=f.useState(h!==void 0?h:s),I=or(_,2),k=I[0],A=I[1],D=f.useCallback(function(Y,ae){typeof u=="function"&&u(Y,ae),A(Y)},[u]),C=f.useCallback(function(Y,ae){var Q;typeof c=="function"&&(Q=c(Y,ae)),b(Q!==void 0?Q:Y)},[c]),E=f.useCallback(function(){typeof g=="function"&&g(),M(!0)},[g]),T=f.useCallback(function(){typeof d=="function"&&d(),M(!1)},[d]),S=a!==void 0?a:w,B=l!==void 0?l:R,X=h!==void 0?h:k;return Ae(Ae({},m),{},{inputValue:S,menuIsOpen:B,onChange:D,onInputChange:C,onMenuClose:T,onMenuOpen:E,value:X})}function Gb(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}const Xb=Math.min,Yb=Math.max,ca=Math.round,Go=Math.floor,da=e=>({x:e,y:e});function jb(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function Sa(){return typeof window<"u"}function rf(e){return af(e)?(e.nodeName||"").toLowerCase():"#document"}function sr(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function of(e){var t;return(t=(af(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function af(e){return Sa()?e instanceof Node||e instanceof sr(e).Node:!1}function Kb(e){return Sa()?e instanceof Element||e instanceof sr(e).Element:!1}function cl(e){return Sa()?e instanceof HTMLElement||e instanceof sr(e).HTMLElement:!1}function tc(e){return!Sa()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof sr(e).ShadowRoot}function sf(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=dl(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function Zb(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Jb(e){return["html","body","#document"].includes(rf(e))}function dl(e){return sr(e).getComputedStyle(e)}function Qb(e){if(rf(e)==="html")return e;const t=e.assignedSlot||e.parentNode||tc(e)&&e.host||of(e);return tc(t)?t.host:t}function lf(e){const t=Qb(e);return Jb(t)?e.ownerDocument?e.ownerDocument.body:e.body:cl(t)&&sf(t)?t:lf(t)}function fa(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=lf(e),o=i===((r=e.ownerDocument)==null?void 0:r.body),s=sr(i);if(o){const a=Ps(s);return t.concat(s,s.visualViewport||[],sf(i)?i:[],a&&n?fa(a):[])}return t.concat(i,fa(i,[],n))}function Ps(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function e1(e){const t=dl(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=cl(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=ca(n)!==o||ca(r)!==s;return a&&(n=o,r=s),{width:n,height:r,$:a}}function fl(e){return Kb(e)?e:e.contextElement}function nc(e){const t=fl(e);if(!cl(t))return da(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=e1(t);let s=(o?ca(n.width):n.width)/r,a=(o?ca(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const t1=da(0);function n1(e){const t=sr(e);return!Zb()||!t.visualViewport?t1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function r1(e,t,n){return!1}function rc(e,t,n,r){t===void 0&&(t=!1);const i=e.getBoundingClientRect(),o=fl(e);let s=da(1);t&&(s=nc(e));const a=r1()?n1(o):da(0);let l=(i.left+a.x)/s.x,u=(i.top+a.y)/s.y,c=i.width/s.x,d=i.height/s.y;if(o){const g=sr(o),h=r;let m=g,p=Ps(m);for(;p&&r&&h!==m;){const v=nc(p),w=p.getBoundingClientRect(),b=dl(p),x=w.left+(p.clientLeft+parseFloat(b.paddingLeft))*v.x,O=w.top+(p.clientTop+parseFloat(b.paddingTop))*v.y;l*=v.x,u*=v.y,c*=v.x,d*=v.y,l+=x,u+=O,m=sr(p),p=Ps(m)}}return jb({width:c,height:d,x:l,y:u})}function i1(e,t){let n=null,r;const i=of(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const{left:u,top:c,width:d,height:g}=e.getBoundingClientRect();if(a||t(),!d||!g)return;const h=Go(c),m=Go(i.clientWidth-(u+d)),p=Go(i.clientHeight-(c+g)),v=Go(u),b={rootMargin:-h+"px "+-m+"px "+-p+"px "+-v+"px",threshold:Yb(0,Xb(1,l))||1};let x=!0;function O(R){const M=R[0].intersectionRatio;if(M!==l){if(!x)return s();M?s(!1,M):r=setTimeout(()=>{s(!1,1e-7)},1e3)}x=!1}try{n=new IntersectionObserver(O,{...b,root:i.ownerDocument})}catch{n=new IntersectionObserver(O,b)}n.observe(e)}return s(!0),o}function o1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=fl(e),c=i||o?[...u?fa(u):[],...fa(t)]:[];c.forEach(w=>{i&&w.addEventListener("scroll",n,{passive:!0}),o&&w.addEventListener("resize",n)});const d=u&&a?i1(u,n):null;let g=-1,h=null;s&&(h=new ResizeObserver(w=>{let[b]=w;b&&b.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var x;(x=h)==null||x.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let m,p=l?rc(e):null;l&&v();function v(){const w=rc(e);p&&(w.x!==p.x||w.y!==p.y||w.width!==p.width||w.height!==p.height)&&n(),p=w,m=requestAnimationFrame(v)}return n(),()=>{var w;c.forEach(b=>{i&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),d?.(),(w=h)==null||w.disconnect(),h=null,l&&cancelAnimationFrame(m)}}var _s=f.useLayoutEffect,a1=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],ha=function(){};function s1(e,t){return t?t[0]==="-"?e+t:e+"__"+t:e}function l1(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=[].concat(r);if(t&&e)for(var s in t)t.hasOwnProperty(s)&&t[s]&&o.push("".concat(s1(e,s)));return o.filter(function(a){return a}).map(function(a){return String(a).trim()}).join(" ")}var ic=function(t){return v1(t)?t.filter(Boolean):rg(t)==="object"&&t!==null?[t]:[]},uf=function(t){t.className,t.clearValue,t.cx,t.getStyles,t.getClassNames,t.getValue,t.hasValue,t.isMulti,t.isRtl,t.options,t.selectOption,t.selectProps,t.setValue,t.theme;var n=ur(t,a1);return Ae({},n)},Kt=function(t,n,r){var i=t.cx,o=t.getStyles,s=t.getClassNames,a=t.className;return{css:o(n,t),className:i(r??{},s(n,t),a)}};function xa(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function u1(e){return xa(e)?window.innerHeight:e.clientHeight}function cf(e){return xa(e)?window.pageYOffset:e.scrollTop}function ga(e,t){if(xa(e)){window.scrollTo(0,t);return}e.scrollTop=t}function c1(e){var t=getComputedStyle(e),n=t.position==="absolute",r=/(auto|scroll)/;if(t.position==="fixed")return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),!(n&&t.position==="static")&&r.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}function d1(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function Xo(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:ha,i=cf(e),o=t-i,s=10,a=0;function l(){a+=s;var u=d1(a,i,o,n);ga(e,u),a<n?window.requestAnimationFrame(l):r(e)}l()}function oc(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),i=t.offsetHeight/3;r.bottom+i>n.bottom?ga(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):r.top-i<n.top&&ga(e,Math.max(t.offsetTop-i,0))}function f1(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}function ac(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function h1(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var df=!1,g1={get passive(){return df=!0}},Yo=typeof window<"u"?window:{};Yo.addEventListener&&Yo.removeEventListener&&(Yo.addEventListener("p",ha,g1),Yo.removeEventListener("p",ha,!1));var m1=df;function p1(e){return e!=null}function v1(e){return Array.isArray(e)}function jo(e,t,n){return e?t:n}var b1=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=Object.entries(t).filter(function(s){var a=or(s,1),l=a[0];return!r.includes(l)});return o.reduce(function(s,a){var l=or(a,2),u=l[0],c=l[1];return s[u]=c,s},{})},w1=["children","innerProps"],y1=["children","innerProps"];function C1(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,i=e.placement,o=e.shouldScroll,s=e.isFixedPosition,a=e.controlHeight,l=c1(n),u={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return u;var c=l.getBoundingClientRect(),d=c.height,g=n.getBoundingClientRect(),h=g.bottom,m=g.height,p=g.top,v=n.offsetParent.getBoundingClientRect(),w=v.top,b=s?window.innerHeight:u1(l),x=cf(l),O=parseInt(getComputedStyle(n).marginBottom,10),R=parseInt(getComputedStyle(n).marginTop,10),M=w-R,_=b-p,I=M+x,k=d-x-p,A=h-b+x+O,D=x+p-R,C=160;switch(i){case"auto":case"bottom":if(_>=m)return{placement:"bottom",maxHeight:t};if(k>=m&&!s)return o&&Xo(l,A,C),{placement:"bottom",maxHeight:t};if(!s&&k>=r||s&&_>=r){o&&Xo(l,A,C);var E=s?_-O:k-O;return{placement:"bottom",maxHeight:E}}if(i==="auto"||s){var T=t,S=s?M:I;return S>=r&&(T=Math.min(S-O-a,t)),{placement:"top",maxHeight:T}}if(i==="bottom")return o&&ga(l,A),{placement:"bottom",maxHeight:t};break;case"top":if(M>=m)return{placement:"top",maxHeight:t};if(I>=m&&!s)return o&&Xo(l,D,C),{placement:"top",maxHeight:t};if(!s&&I>=r||s&&M>=r){var B=t;return(!s&&I>=r||s&&M>=r)&&(B=s?M-R:I-R),o&&Xo(l,D,C),{placement:"top",maxHeight:B}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return u}function S1(e){var t={bottom:"top",top:"bottom"};return e?t[e]:"bottom"}var ff=function(t){return t==="auto"?"bottom":t},x1=function(t,n){var r,i=t.placement,o=t.theme,s=o.borderRadius,a=o.spacing,l=o.colors;return Ae((r={label:"menu"},ji(r,S1(i),"100%"),ji(r,"position","absolute"),ji(r,"width","100%"),ji(r,"zIndex",1),r),n?{}:{backgroundColor:l.neutral0,borderRadius:s,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:a.menuGutter,marginTop:a.menuGutter})},hf=f.createContext(null),k1=function(t){var n=t.children,r=t.minMenuHeight,i=t.maxMenuHeight,o=t.menuPlacement,s=t.menuPosition,a=t.menuShouldScrollIntoView,l=t.theme,u=f.useContext(hf)||{},c=u.setPortalPlacement,d=f.useRef(null),g=f.useState(i),h=or(g,2),m=h[0],p=h[1],v=f.useState(null),w=or(v,2),b=w[0],x=w[1],O=l.spacing.controlHeight;return _s(function(){var R=d.current;if(R){var M=s==="fixed",_=a&&!M,I=C1({maxHeight:i,menuEl:R,minHeight:r,placement:o,shouldScroll:_,isFixedPosition:M,controlHeight:O});p(I.maxHeight),x(I.placement),c?.(I.placement)}},[i,o,s,a,r,c,O]),n({ref:d,placerProps:Ae(Ae({},t),{},{placement:b||ff(o),maxHeight:m})})},M1=function(t){var n=t.children,r=t.innerRef,i=t.innerProps;return _e("div",Ne({},Kt(t,"menu",{menu:!0}),{ref:r},i),n)},R1=M1,I1=function(t,n){var r=t.maxHeight,i=t.theme.spacing.baseUnit;return Ae({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},n?{}:{paddingBottom:i,paddingTop:i})},E1=function(t){var n=t.children,r=t.innerProps,i=t.innerRef,o=t.isMulti;return _e("div",Ne({},Kt(t,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:i},r),n)},gf=function(t,n){var r=t.theme,i=r.spacing.baseUnit,o=r.colors;return Ae({textAlign:"center"},n?{}:{color:o.neutral40,padding:"".concat(i*2,"px ").concat(i*3,"px")})},T1=gf,D1=gf,O1=function(t){var n=t.children,r=n===void 0?"No options":n,i=t.innerProps,o=ur(t,w1);return _e("div",Ne({},Kt(Ae(Ae({},o),{},{children:r,innerProps:i}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),i),r)},P1=function(t){var n=t.children,r=n===void 0?"Loading...":n,i=t.innerProps,o=ur(t,y1);return _e("div",Ne({},Kt(Ae(Ae({},o),{},{children:r,innerProps:i}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),i),r)},_1=function(t){var n=t.rect,r=t.offset,i=t.position;return{left:n.left,position:i,top:r,width:n.width,zIndex:1}},F1=function(t){var n=t.appendTo,r=t.children,i=t.controlElement,o=t.innerProps,s=t.menuPlacement,a=t.menuPosition,l=f.useRef(null),u=f.useRef(null),c=f.useState(ff(s)),d=or(c,2),g=d[0],h=d[1],m=f.useMemo(function(){return{setPortalPlacement:h}},[]),p=f.useState(null),v=or(p,2),w=v[0],b=v[1],x=f.useCallback(function(){if(i){var _=f1(i),I=a==="fixed"?0:window.pageYOffset,k=_[g]+I;(k!==w?.offset||_.left!==w?.rect.left||_.width!==w?.rect.width)&&b({offset:k,rect:_})}},[i,a,g,w?.offset,w?.rect.left,w?.rect.width]);_s(function(){x()},[x]);var O=f.useCallback(function(){typeof u.current=="function"&&(u.current(),u.current=null),i&&l.current&&(u.current=o1(i,l.current,x,{elementResize:"ResizeObserver"in window}))},[i,x]);_s(function(){O()},[O]);var R=f.useCallback(function(_){l.current=_,O()},[O]);if(!n&&a!=="fixed"||!w)return null;var M=_e("div",Ne({ref:R},Kt(Ae(Ae({},t),{},{offset:w.offset,position:a,rect:w.rect}),"menuPortal",{"menu-portal":!0}),o),r);return _e(hf.Provider,{value:m},n?Hc.createPortal(M,n):M)},L1=function(t){var n=t.isDisabled,r=t.isRtl;return{label:"container",direction:r?"rtl":void 0,pointerEvents:n?"none":void 0,position:"relative"}},A1=function(t){var n=t.children,r=t.innerProps,i=t.isDisabled,o=t.isRtl;return _e("div",Ne({},Kt(t,"container",{"--is-disabled":i,"--is-rtl":o}),r),n)},H1=function(t,n){var r=t.theme.spacing,i=t.isMulti,o=t.hasValue,s=t.selectProps.controlShouldRenderValue;return Ae({alignItems:"center",display:i&&o&&s?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},n?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(r.baseUnit*2,"px")})},z1=function(t){var n=t.children,r=t.innerProps,i=t.isMulti,o=t.hasValue;return _e("div",Ne({},Kt(t,"valueContainer",{"value-container":!0,"value-container--is-multi":i,"value-container--has-value":o}),r),n)},V1=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},$1=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"indicatorsContainer",{indicators:!0}),r),n)},sc,N1=["size"],B1=["innerProps","isRtl","size"],W1={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},mf=function(t){var n=t.size,r=ur(t,N1);return _e("svg",Ne({height:n,width:n,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:W1},r))},hl=function(t){return _e(mf,Ne({size:20},t),_e("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},pf=function(t){return _e(mf,Ne({size:20},t),_e("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},vf=function(t,n){var r=t.isFocused,i=t.theme,o=i.spacing.baseUnit,s=i.colors;return Ae({label:"indicatorContainer",display:"flex",transition:"color 150ms"},n?{}:{color:r?s.neutral60:s.neutral20,padding:o*2,":hover":{color:r?s.neutral80:s.neutral40}})},U1=vf,q1=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),n||_e(pf,null))},G1=vf,X1=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),n||_e(hl,null))},Y1=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing.baseUnit,s=i.colors;return Ae({label:"indicatorSeparator",alignSelf:"stretch",width:1},n?{}:{backgroundColor:r?s.neutral10:s.neutral20,marginBottom:o*2,marginTop:o*2})},j1=function(t){var n=t.innerProps;return _e("span",Ne({},n,Kt(t,"indicatorSeparator",{"indicator-separator":!0})))},K1=ig(sc||(sc=Gb([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),Z1=function(t,n){var r=t.isFocused,i=t.size,o=t.theme,s=o.colors,a=o.spacing.baseUnit;return Ae({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:i,lineHeight:1,marginRight:i,textAlign:"center",verticalAlign:"middle"},n?{}:{color:r?s.neutral60:s.neutral20,padding:a*2})},gs=function(t){var n=t.delay,r=t.offset;return _e("span",{css:zc({animation:"".concat(K1," 1s ease-in-out ").concat(n,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},J1=function(t){var n=t.innerProps,r=t.isRtl,i=t.size,o=i===void 0?4:i,s=ur(t,B1);return _e("div",Ne({},Kt(Ae(Ae({},s),{},{innerProps:n,isRtl:r,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),n),_e(gs,{delay:0,offset:r}),_e(gs,{delay:160,offset:!0}),_e(gs,{delay:320,offset:!r}))},Q1=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.theme,s=o.colors,a=o.borderRadius,l=o.spacing;return Ae({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:l.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},n?{}:{backgroundColor:r?s.neutral5:s.neutral0,borderColor:r?s.neutral10:i?s.primary:s.neutral20,borderRadius:a,borderStyle:"solid",borderWidth:1,boxShadow:i?"0 0 0 1px ".concat(s.primary):void 0,"&:hover":{borderColor:i?s.primary:s.neutral30}})},ew=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.innerRef,s=t.innerProps,a=t.menuIsOpen;return _e("div",Ne({ref:o},Kt(t,"control",{control:!0,"control--is-disabled":r,"control--is-focused":i,"control--menu-is-open":a}),s,{"aria-disabled":r||void 0}),n)},tw=ew,nw=["data"],rw=function(t,n){var r=t.theme.spacing;return n?{}:{paddingBottom:r.baseUnit*2,paddingTop:r.baseUnit*2}},iw=function(t){var n=t.children,r=t.cx,i=t.getStyles,o=t.getClassNames,s=t.Heading,a=t.headingProps,l=t.innerProps,u=t.label,c=t.theme,d=t.selectProps;return _e("div",Ne({},Kt(t,"group",{group:!0}),l),_e(s,Ne({},a,{selectProps:d,theme:c,getStyles:i,getClassNames:o,cx:r}),u),_e("div",null,n))},ow=function(t,n){var r=t.theme,i=r.colors,o=r.spacing;return Ae({label:"group",cursor:"default",display:"block"},n?{}:{color:i.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:o.baseUnit*3,paddingRight:o.baseUnit*3,textTransform:"uppercase"})},aw=function(t){var n=uf(t);n.data;var r=ur(n,nw);return _e("div",Ne({},Kt(t,"groupHeading",{"group-heading":!0}),r))},sw=iw,lw=["innerRef","isDisabled","isHidden","inputClassName"],uw=function(t,n){var r=t.isDisabled,i=t.value,o=t.theme,s=o.spacing,a=o.colors;return Ae(Ae({visibility:r?"hidden":"visible",transform:i?"translateZ(0)":""},cw),n?{}:{margin:s.baseUnit/2,paddingBottom:s.baseUnit/2,paddingTop:s.baseUnit/2,color:a.neutral80})},bf={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},cw={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Ae({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},bf)},dw=function(t){return Ae({label:"input",color:"inherit",background:0,opacity:t?0:1,width:"100%"},bf)},fw=function(t){var n=t.cx,r=t.value,i=uf(t),o=i.innerRef,s=i.isDisabled,a=i.isHidden,l=i.inputClassName,u=ur(i,lw);return _e("div",Ne({},Kt(t,"input",{"input-container":!0}),{"data-value":r||""}),_e("input",Ne({className:n({input:!0},l),ref:o,style:dw(a),disabled:s},u)))},hw=fw,gw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,s=r.colors;return Ae({label:"multiValue",display:"flex",minWidth:0},n?{}:{backgroundColor:s.neutral10,borderRadius:o/2,margin:i.baseUnit/2})},mw=function(t,n){var r=t.theme,i=r.borderRadius,o=r.colors,s=t.cropWithEllipsis;return Ae({overflow:"hidden",textOverflow:s||s===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},n?{}:{borderRadius:i/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},pw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,s=r.colors,a=t.isFocused;return Ae({alignItems:"center",display:"flex"},n?{}:{borderRadius:o/2,backgroundColor:a?s.dangerLight:void 0,paddingLeft:i.baseUnit,paddingRight:i.baseUnit,":hover":{backgroundColor:s.dangerLight,color:s.danger}})},wf=function(t){var n=t.children,r=t.innerProps;return _e("div",r,n)},vw=wf,bw=wf;function ww(e){var t=e.children,n=e.innerProps;return _e("div",Ne({role:"button"},n),t||_e(hl,{size:14}))}var yw=function(t){var n=t.children,r=t.components,i=t.data,o=t.innerProps,s=t.isDisabled,a=t.removeProps,l=t.selectProps,u=r.Container,c=r.Label,d=r.Remove;return _e(u,{data:i,innerProps:Ae(Ae({},Kt(t,"multiValue",{"multi-value":!0,"multi-value--is-disabled":s})),o),selectProps:l},_e(c,{data:i,innerProps:Ae({},Kt(t,"multiValueLabel",{"multi-value__label":!0})),selectProps:l},n),_e(d,{data:i,innerProps:Ae(Ae({},Kt(t,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(n||"option")},a),selectProps:l}))},Cw=yw,Sw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.isSelected,s=t.theme,a=s.spacing,l=s.colors;return Ae({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},n?{}:{backgroundColor:o?l.primary:i?l.primary25:"transparent",color:r?l.neutral20:o?l.neutral0:"inherit",padding:"".concat(a.baseUnit*2,"px ").concat(a.baseUnit*3,"px"),":active":{backgroundColor:r?void 0:o?l.primary:l.primary50}})},xw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.isSelected,s=t.innerRef,a=t.innerProps;return _e("div",Ne({},Kt(t,"option",{option:!0,"option--is-disabled":r,"option--is-focused":i,"option--is-selected":o}),{ref:s,"aria-disabled":r},a),n)},kw=xw,Mw=function(t,n){var r=t.theme,i=r.spacing,o=r.colors;return Ae({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},n?{}:{color:o.neutral50,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},Rw=function(t){var n=t.children,r=t.innerProps;return _e("div",Ne({},Kt(t,"placeholder",{placeholder:!0}),r),n)},Iw=Rw,Ew=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing,s=i.colors;return Ae({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n?{}:{color:r?s.neutral40:s.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},Tw=function(t){var n=t.children,r=t.isDisabled,i=t.innerProps;return _e("div",Ne({},Kt(t,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),i),n)},Dw=Tw,yf={ClearIndicator:X1,Control:tw,DropdownIndicator:q1,DownChevron:pf,CrossIcon:hl,Group:sw,GroupHeading:aw,IndicatorsContainer:$1,IndicatorSeparator:j1,Input:hw,LoadingIndicator:J1,Menu:R1,MenuList:E1,MenuPortal:F1,LoadingMessage:P1,NoOptionsMessage:O1,MultiValue:Cw,MultiValueContainer:vw,MultiValueLabel:bw,MultiValueRemove:ww,Option:kw,Placeholder:Iw,SelectContainer:A1,SingleValue:Dw,ValueContainer:z1},Ow=function(t){return Ae(Ae({},yf),t.components)},lc=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function Pw(e,t){return!!(e===t||lc(e)&&lc(t))}function _w(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!Pw(e[n],t[n]))return!1;return!0}function Fw(e,t){t===void 0&&(t=_w);var n=null;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;var s=e.apply(this,i);return n={lastResult:s,lastArgs:i,lastThis:this},s}return r.clear=function(){n=null},r}var Lw={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},Aw=function(t){return _e("span",Ne({css:Lw},t))},uc=Aw,Hw={guidance:function(t){var n=t.isSearchable,r=t.isMulti,i=t.tabSelectsValue,o=t.context,s=t.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(i?", press Tab to select the option and exit the menu":"",".");case"input":return s?"".concat(t["aria-label"]||"Select"," is focused ").concat(n?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(t){var n=t.action,r=t.label,i=r===void 0?"":r,o=t.labels,s=t.isDisabled;switch(n){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(i,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return s?"option ".concat(i," is disabled. Select another option."):"option ".concat(i,", selected.");default:return""}},onFocus:function(t){var n=t.context,r=t.focused,i=t.options,o=t.label,s=o===void 0?"":o,a=t.selectValue,l=t.isDisabled,u=t.isSelected,c=t.isAppleDevice,d=function(p,v){return p&&p.length?"".concat(p.indexOf(v)+1," of ").concat(p.length):""};if(n==="value"&&a)return"value ".concat(s," focused, ").concat(d(a,r),".");if(n==="menu"&&c){var g=l?" disabled":"",h="".concat(u?" selected":"").concat(g);return"".concat(s).concat(h,", ").concat(d(i,r),".")}return""},onFilter:function(t){var n=t.inputValue,r=t.resultsMessage;return"".concat(r).concat(n?" for search term "+n:"",".")}},zw=function(t){var n=t.ariaSelection,r=t.focusedOption,i=t.focusedValue,o=t.focusableOptions,s=t.isFocused,a=t.selectValue,l=t.selectProps,u=t.id,c=t.isAppleDevice,d=l.ariaLiveMessages,g=l.getOptionLabel,h=l.inputValue,m=l.isMulti,p=l.isOptionDisabled,v=l.isSearchable,w=l.menuIsOpen,b=l.options,x=l.screenReaderStatus,O=l.tabSelectsValue,R=l.isLoading,M=l["aria-label"],_=l["aria-live"],I=f.useMemo(function(){return Ae(Ae({},Hw),d||{})},[d]),k=f.useMemo(function(){var S="";if(n&&I.onChange){var B=n.option,X=n.options,Y=n.removedValue,ae=n.removedValues,Q=n.value,ee=function(P){return Array.isArray(P)?null:P},re=Y||B||ee(Q),te=re?g(re):"",ue=X||ae||void 0,fe=ue?ue.map(g):[],se=Ae({isDisabled:re&&p(re,a),label:te,labels:fe},n);S=I.onChange(se)}return S},[n,I,p,a,g]),A=f.useMemo(function(){var S="",B=r||i,X=!!(r&&a&&a.includes(r));if(B&&I.onFocus){var Y={focused:B,label:g(B),isDisabled:p(B,a),isSelected:X,options:o,context:B===r?"menu":"value",selectValue:a,isAppleDevice:c};S=I.onFocus(Y)}return S},[r,i,g,p,I,o,a,c]),D=f.useMemo(function(){var S="";if(w&&b.length&&!R&&I.onFilter){var B=x({count:o.length});S=I.onFilter({inputValue:h,resultsMessage:B})}return S},[o,h,w,I,b,x,R]),C=n?.action==="initial-input-focus",E=f.useMemo(function(){var S="";if(I.guidance){var B=i?"value":w?"menu":"input";S=I.guidance({"aria-label":M,context:B,isDisabled:r&&p(r,a),isMulti:m,isSearchable:v,tabSelectsValue:O,isInitialFocus:C})}return S},[M,r,i,m,p,v,w,I,a,O,C]),T=_e(f.Fragment,null,_e("span",{id:"aria-selection"},k),_e("span",{id:"aria-focused"},A),_e("span",{id:"aria-results"},D),_e("span",{id:"aria-guidance"},E));return _e(f.Fragment,null,_e(uc,{id:u},C&&T),_e(uc,{"aria-live":_,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},s&&!C&&T))},Vw=zw,Fs=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],$w=new RegExp("["+Fs.map(function(e){return e.letters}).join("")+"]","g"),Cf={};for(var ms=0;ms<Fs.length;ms++)for(var ps=Fs[ms],vs=0;vs<ps.letters.length;vs++)Cf[ps.letters[vs]]=ps.base;var Sf=function(t){return t.replace($w,function(n){return Cf[n]})},Nw=Fw(Sf),cc=function(t){return t.replace(/^\s+|\s+$/g,"")},Bw=function(t){return"".concat(t.label," ").concat(t.value)},Ww=function(t){return function(n,r){if(n.data.__isNew__)return!0;var i=Ae({ignoreCase:!0,ignoreAccents:!0,stringify:Bw,trim:!0,matchFrom:"any"},t),o=i.ignoreCase,s=i.ignoreAccents,a=i.stringify,l=i.trim,u=i.matchFrom,c=l?cc(r):r,d=l?cc(a(n)):a(n);return o&&(c=c.toLowerCase(),d=d.toLowerCase()),s&&(c=Nw(c),d=Sf(d)),u==="start"?d.substr(0,c.length)===c:d.indexOf(c)>-1}},Uw=["innerRef"];function qw(e){var t=e.innerRef,n=ur(e,Uw),r=b1(n,"onExited","in","enter","exit","appear");return _e("input",Ne({ref:t},r,{css:zc({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var Gw=function(t){t.cancelable&&t.preventDefault(),t.stopPropagation()};function Xw(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,i=e.onTopArrive,o=e.onTopLeave,s=f.useRef(!1),a=f.useRef(!1),l=f.useRef(0),u=f.useRef(null),c=f.useCallback(function(v,w){if(u.current!==null){var b=u.current,x=b.scrollTop,O=b.scrollHeight,R=b.clientHeight,M=u.current,_=w>0,I=O-R-x,k=!1;I>w&&s.current&&(r&&r(v),s.current=!1),_&&a.current&&(o&&o(v),a.current=!1),_&&w>I?(n&&!s.current&&n(v),M.scrollTop=O,k=!0,s.current=!0):!_&&-w>x&&(i&&!a.current&&i(v),M.scrollTop=0,k=!0,a.current=!0),k&&Gw(v)}},[n,r,i,o]),d=f.useCallback(function(v){c(v,v.deltaY)},[c]),g=f.useCallback(function(v){l.current=v.changedTouches[0].clientY},[]),h=f.useCallback(function(v){var w=l.current-v.changedTouches[0].clientY;c(v,w)},[c]),m=f.useCallback(function(v){if(v){var w=m1?{passive:!1}:!1;v.addEventListener("wheel",d,w),v.addEventListener("touchstart",g,w),v.addEventListener("touchmove",h,w)}},[h,g,d]),p=f.useCallback(function(v){v&&(v.removeEventListener("wheel",d,!1),v.removeEventListener("touchstart",g,!1),v.removeEventListener("touchmove",h,!1))},[h,g,d]);return f.useEffect(function(){if(t){var v=u.current;return m(v),function(){p(v)}}},[t,m,p]),function(v){u.current=v}}var dc=["boxSizing","height","overflow","paddingRight","position"],fc={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function hc(e){e.cancelable&&e.preventDefault()}function gc(e){e.stopPropagation()}function mc(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;e===0?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function pc(){return"ontouchstart"in window||navigator.maxTouchPoints}var vc=!!(typeof window<"u"&&window.document&&window.document.createElement),Xi=0,li={capture:!1,passive:!1};function Yw(e){var t=e.isEnabled,n=e.accountForScrollbars,r=n===void 0?!0:n,i=f.useRef({}),o=f.useRef(null),s=f.useCallback(function(l){if(vc){var u=document.body,c=u&&u.style;if(r&&dc.forEach(function(m){var p=c&&c[m];i.current[m]=p}),r&&Xi<1){var d=parseInt(i.current.paddingRight,10)||0,g=document.body?document.body.clientWidth:0,h=window.innerWidth-g+d||0;Object.keys(fc).forEach(function(m){var p=fc[m];c&&(c[m]=p)}),c&&(c.paddingRight="".concat(h,"px"))}u&&pc()&&(u.addEventListener("touchmove",hc,li),l&&(l.addEventListener("touchstart",mc,li),l.addEventListener("touchmove",gc,li))),Xi+=1}},[r]),a=f.useCallback(function(l){if(vc){var u=document.body,c=u&&u.style;Xi=Math.max(Xi-1,0),r&&Xi<1&&dc.forEach(function(d){var g=i.current[d];c&&(c[d]=g)}),u&&pc()&&(u.removeEventListener("touchmove",hc,li),l&&(l.removeEventListener("touchstart",mc,li),l.removeEventListener("touchmove",gc,li)))}},[r]);return f.useEffect(function(){if(t){var l=o.current;return s(l),function(){a(l)}}},[t,s,a]),function(l){o.current=l}}var jw=function(t){var n=t.target;return n.ownerDocument.activeElement&&n.ownerDocument.activeElement.blur()},Kw={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function Zw(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,i=r===void 0?!0:r,o=e.onBottomArrive,s=e.onBottomLeave,a=e.onTopArrive,l=e.onTopLeave,u=Xw({isEnabled:i,onBottomArrive:o,onBottomLeave:s,onTopArrive:a,onTopLeave:l}),c=Yw({isEnabled:n}),d=function(h){u(h),c(h)};return _e(f.Fragment,null,n&&_e("div",{onClick:jw,css:Kw}),t(d))}var Jw={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},Qw=function(t){var n=t.name,r=t.onFocus;return _e("input",{required:!0,name:n,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:Jw,value:"",onChange:function(){}})},ey=Qw;function gl(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function ty(){return gl(/^iPhone/i)}function xf(){return gl(/^Mac/i)}function ny(){return gl(/^iPad/i)||xf()&&navigator.maxTouchPoints>1}function ry(){return ty()||ny()}function iy(){return xf()||ry()}var oy=function(t){return t.label},ay=function(t){return t.label},sy=function(t){return t.value},ly=function(t){return!!t.isDisabled},uy={clearIndicator:G1,container:L1,control:Q1,dropdownIndicator:U1,group:rw,groupHeading:ow,indicatorsContainer:V1,indicatorSeparator:Y1,input:uw,loadingIndicator:Z1,loadingMessage:D1,menu:x1,menuList:I1,menuPortal:_1,multiValue:gw,multiValueLabel:mw,multiValueRemove:pw,noOptionsMessage:T1,option:Sw,placeholder:Mw,singleValue:Ew,valueContainer:H1},cy={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},dy=4,kf=4,fy=38,hy=kf*2,gy={baseUnit:kf,controlHeight:fy,menuGutter:hy},bs={borderRadius:dy,colors:cy,spacing:gy},my={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:ac(),captureMenuScroll:!ac(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:Ww(),formatGroupLabel:oy,getOptionLabel:ay,getOptionValue:sy,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:ly,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!h1(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(t){var n=t.count;return"".concat(n," result").concat(n!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function bc(e,t,n,r){var i=If(e,t,n),o=Ef(e,t,n),s=Rf(e,t),a=ma(e,t);return{type:"option",data:t,isDisabled:i,isSelected:o,label:s,value:a,index:r}}function ea(e,t){return e.options.map(function(n,r){if("options"in n){var i=n.options.map(function(s,a){return bc(e,s,t,a)}).filter(function(s){return yc(e,s)});return i.length>0?{type:"group",data:n,options:i,index:r}:void 0}var o=bc(e,n,t,r);return yc(e,o)?o:void 0}).filter(p1)}function Mf(e){return e.reduce(function(t,n){return n.type==="group"?t.push.apply(t,Hs(n.options.map(function(r){return r.data}))):t.push(n.data),t},[])}function wc(e,t){return e.reduce(function(n,r){return r.type==="group"?n.push.apply(n,Hs(r.options.map(function(i){return{data:i.data,id:"".concat(t,"-").concat(r.index,"-").concat(i.index)}}))):n.push({data:r.data,id:"".concat(t,"-").concat(r.index)}),n},[])}function py(e,t){return Mf(ea(e,t))}function yc(e,t){var n=e.inputValue,r=n===void 0?"":n,i=t.data,o=t.isSelected,s=t.label,a=t.value;return(!Df(e)||!o)&&Tf(e,{label:s,value:a,data:i},r)}function vy(e,t){var n=e.focusedValue,r=e.selectValue,i=r.indexOf(n);if(i>-1){var o=t.indexOf(n);if(o>-1)return n;if(i<t.length)return t[i]}return null}function by(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}var ws=function(t,n){var r,i=(r=t.find(function(o){return o.data===n}))===null||r===void 0?void 0:r.id;return i||null},Rf=function(t,n){return t.getOptionLabel(n)},ma=function(t,n){return t.getOptionValue(n)};function If(e,t,n){return typeof e.isOptionDisabled=="function"?e.isOptionDisabled(t,n):!1}function Ef(e,t,n){if(n.indexOf(t)>-1)return!0;if(typeof e.isOptionSelected=="function")return e.isOptionSelected(t,n);var r=ma(e,t);return n.some(function(i){return ma(e,i)===r})}function Tf(e,t,n){return e.filterOption?e.filterOption(t,n):!0}var Df=function(t){var n=t.hideSelectedOptions,r=t.isMulti;return n===void 0?r:n},wy=1,Of=function(e){kg(n,e);var t=Ig(n);function n(r){var i;if(Rg(this,n),i=t.call(this,r),i.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},i.blockOptionHover=!1,i.isComposing=!1,i.commonProps=void 0,i.initialTouchX=0,i.initialTouchY=0,i.openAfterFocus=!1,i.scrollToFocusedOptionOnUpdate=!1,i.userIsDragging=void 0,i.isAppleDevice=iy(),i.controlRef=null,i.getControlRef=function(l){i.controlRef=l},i.focusedOptionRef=null,i.getFocusedOptionRef=function(l){i.focusedOptionRef=l},i.menuListRef=null,i.getMenuListRef=function(l){i.menuListRef=l},i.inputRef=null,i.getInputRef=function(l){i.inputRef=l},i.focus=i.focusInput,i.blur=i.blurInput,i.onChange=function(l,u){var c=i.props,d=c.onChange,g=c.name;u.name=g,i.ariaOnChange(l,u),d(l,u)},i.setValue=function(l,u,c){var d=i.props,g=d.closeMenuOnSelect,h=d.isMulti,m=d.inputValue;i.onInputChange("",{action:"set-value",prevInputValue:m}),g&&(i.setState({inputIsHiddenAfterUpdate:!h}),i.onMenuClose()),i.setState({clearFocusValueOnUpdate:!0}),i.onChange(l,{action:u,option:c})},i.selectOption=function(l){var u=i.props,c=u.blurInputOnSelect,d=u.isMulti,g=u.name,h=i.state.selectValue,m=d&&i.isOptionSelected(l,h),p=i.isOptionDisabled(l,h);if(m){var v=i.getOptionValue(l);i.setValue(h.filter(function(w){return i.getOptionValue(w)!==v}),"deselect-option",l)}else if(!p)d?i.setValue([].concat(Hs(h),[l]),"select-option",l):i.setValue(l,"select-option");else{i.ariaOnChange(l,{action:"select-option",option:l,name:g});return}c&&i.blurInput()},i.removeValue=function(l){var u=i.props.isMulti,c=i.state.selectValue,d=i.getOptionValue(l),g=c.filter(function(m){return i.getOptionValue(m)!==d}),h=jo(u,g,g[0]||null);i.onChange(h,{action:"remove-value",removedValue:l}),i.focusInput()},i.clearValue=function(){var l=i.state.selectValue;i.onChange(jo(i.props.isMulti,[],null),{action:"clear",removedValues:l})},i.popValue=function(){var l=i.props.isMulti,u=i.state.selectValue,c=u[u.length-1],d=u.slice(0,u.length-1),g=jo(l,d,d[0]||null);c&&i.onChange(g,{action:"pop-value",removedValue:c})},i.getFocusedOptionId=function(l){return ws(i.state.focusableOptionsWithIds,l)},i.getFocusableOptionsWithIds=function(){return wc(ea(i.props,i.state.selectValue),i.getElementId("option"))},i.getValue=function(){return i.state.selectValue},i.cx=function(){for(var l=arguments.length,u=new Array(l),c=0;c<l;c++)u[c]=arguments[c];return l1.apply(void 0,[i.props.classNamePrefix].concat(u))},i.getOptionLabel=function(l){return Rf(i.props,l)},i.getOptionValue=function(l){return ma(i.props,l)},i.getStyles=function(l,u){var c=i.props.unstyled,d=uy[l](u,c);d.boxSizing="border-box";var g=i.props.styles[l];return g?g(d,u):d},i.getClassNames=function(l,u){var c,d;return(c=(d=i.props.classNames)[l])===null||c===void 0?void 0:c.call(d,u)},i.getElementId=function(l){return"".concat(i.state.instancePrefix,"-").concat(l)},i.getComponents=function(){return Ow(i.props)},i.buildCategorizedOptions=function(){return ea(i.props,i.state.selectValue)},i.getCategorizedOptions=function(){return i.props.menuIsOpen?i.buildCategorizedOptions():[]},i.buildFocusableOptions=function(){return Mf(i.buildCategorizedOptions())},i.getFocusableOptions=function(){return i.props.menuIsOpen?i.buildFocusableOptions():[]},i.ariaOnChange=function(l,u){i.setState({ariaSelection:Ae({value:l},u)})},i.onMenuMouseDown=function(l){l.button===0&&(l.stopPropagation(),l.preventDefault(),i.focusInput())},i.onMenuMouseMove=function(l){i.blockOptionHover=!1},i.onControlMouseDown=function(l){if(!l.defaultPrevented){var u=i.props.openMenuOnClick;i.state.isFocused?i.props.menuIsOpen?l.target.tagName!=="INPUT"&&l.target.tagName!=="TEXTAREA"&&i.onMenuClose():u&&i.openMenu("first"):(u&&(i.openAfterFocus=!0),i.focusInput()),l.target.tagName!=="INPUT"&&l.target.tagName!=="TEXTAREA"&&l.preventDefault()}},i.onDropdownIndicatorMouseDown=function(l){if(!(l&&l.type==="mousedown"&&l.button!==0)&&!i.props.isDisabled){var u=i.props,c=u.isMulti,d=u.menuIsOpen;i.focusInput(),d?(i.setState({inputIsHiddenAfterUpdate:!c}),i.onMenuClose()):i.openMenu("first"),l.preventDefault()}},i.onClearIndicatorMouseDown=function(l){l&&l.type==="mousedown"&&l.button!==0||(i.clearValue(),l.preventDefault(),i.openAfterFocus=!1,l.type==="touchend"?i.focusInput():setTimeout(function(){return i.focusInput()}))},i.onScroll=function(l){typeof i.props.closeMenuOnScroll=="boolean"?l.target instanceof HTMLElement&&xa(l.target)&&i.props.onMenuClose():typeof i.props.closeMenuOnScroll=="function"&&i.props.closeMenuOnScroll(l)&&i.props.onMenuClose()},i.onCompositionStart=function(){i.isComposing=!0},i.onCompositionEnd=function(){i.isComposing=!1},i.onTouchStart=function(l){var u=l.touches,c=u&&u.item(0);c&&(i.initialTouchX=c.clientX,i.initialTouchY=c.clientY,i.userIsDragging=!1)},i.onTouchMove=function(l){var u=l.touches,c=u&&u.item(0);if(c){var d=Math.abs(c.clientX-i.initialTouchX),g=Math.abs(c.clientY-i.initialTouchY),h=5;i.userIsDragging=d>h||g>h}},i.onTouchEnd=function(l){i.userIsDragging||(i.controlRef&&!i.controlRef.contains(l.target)&&i.menuListRef&&!i.menuListRef.contains(l.target)&&i.blurInput(),i.initialTouchX=0,i.initialTouchY=0)},i.onControlTouchEnd=function(l){i.userIsDragging||i.onControlMouseDown(l)},i.onClearIndicatorTouchEnd=function(l){i.userIsDragging||i.onClearIndicatorMouseDown(l)},i.onDropdownIndicatorTouchEnd=function(l){i.userIsDragging||i.onDropdownIndicatorMouseDown(l)},i.handleInputChange=function(l){var u=i.props.inputValue,c=l.currentTarget.value;i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange(c,{action:"input-change",prevInputValue:u}),i.props.menuIsOpen||i.onMenuOpen()},i.onInputFocus=function(l){i.props.onFocus&&i.props.onFocus(l),i.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(i.openAfterFocus||i.props.openMenuOnFocus)&&i.openMenu("first"),i.openAfterFocus=!1},i.onInputBlur=function(l){var u=i.props.inputValue;if(i.menuListRef&&i.menuListRef.contains(document.activeElement)){i.inputRef.focus();return}i.props.onBlur&&i.props.onBlur(l),i.onInputChange("",{action:"input-blur",prevInputValue:u}),i.onMenuClose(),i.setState({focusedValue:null,isFocused:!1})},i.onOptionHover=function(l){if(!(i.blockOptionHover||i.state.focusedOption===l)){var u=i.getFocusableOptions(),c=u.indexOf(l);i.setState({focusedOption:l,focusedOptionId:c>-1?i.getFocusedOptionId(l):null})}},i.shouldHideSelectedOptions=function(){return Df(i.props)},i.onValueInputFocus=function(l){l.preventDefault(),l.stopPropagation(),i.focus()},i.onKeyDown=function(l){var u=i.props,c=u.isMulti,d=u.backspaceRemovesValue,g=u.escapeClearsValue,h=u.inputValue,m=u.isClearable,p=u.isDisabled,v=u.menuIsOpen,w=u.onKeyDown,b=u.tabSelectsValue,x=u.openMenuOnFocus,O=i.state,R=O.focusedOption,M=O.focusedValue,_=O.selectValue;if(!p&&!(typeof w=="function"&&(w(l),l.defaultPrevented))){switch(i.blockOptionHover=!0,l.key){case"ArrowLeft":if(!c||h)return;i.focusValue("previous");break;case"ArrowRight":if(!c||h)return;i.focusValue("next");break;case"Delete":case"Backspace":if(h)return;if(M)i.removeValue(M);else{if(!d)return;c?i.popValue():m&&i.clearValue()}break;case"Tab":if(i.isComposing||l.shiftKey||!v||!b||!R||x&&i.isOptionSelected(R,_))return;i.selectOption(R);break;case"Enter":if(l.keyCode===229)break;if(v){if(!R||i.isComposing)return;i.selectOption(R);break}return;case"Escape":v?(i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange("",{action:"menu-close",prevInputValue:h}),i.onMenuClose()):m&&g&&i.clearValue();break;case" ":if(h)return;if(!v){i.openMenu("first");break}if(!R)return;i.selectOption(R);break;case"ArrowUp":v?i.focusOption("up"):i.openMenu("last");break;case"ArrowDown":v?i.focusOption("down"):i.openMenu("first");break;case"PageUp":if(!v)return;i.focusOption("pageup");break;case"PageDown":if(!v)return;i.focusOption("pagedown");break;case"Home":if(!v)return;i.focusOption("first");break;case"End":if(!v)return;i.focusOption("last");break;default:return}l.preventDefault()}},i.state.instancePrefix="react-select-"+(i.props.instanceId||++wy),i.state.selectValue=ic(r.value),r.menuIsOpen&&i.state.selectValue.length){var o=i.getFocusableOptionsWithIds(),s=i.buildFocusableOptions(),a=s.indexOf(i.state.selectValue[0]);i.state.focusableOptionsWithIds=o,i.state.focusedOption=s[a],i.state.focusedOptionId=ws(o,s[a])}return i}return Mg(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&oc(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(i){var o=this.props,s=o.isDisabled,a=o.menuIsOpen,l=this.state.isFocused;(l&&!s&&i.isDisabled||l&&a&&!i.menuIsOpen)&&this.focusInput(),l&&s&&!i.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!l&&!s&&i.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(oc(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(i,o){this.props.onInputChange(i,o)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(i){var o=this,s=this.state,a=s.selectValue,l=s.isFocused,u=this.buildFocusableOptions(),c=i==="first"?0:u.length-1;if(!this.props.isMulti){var d=u.indexOf(a[0]);d>-1&&(c=d)}this.scrollToFocusedOptionOnUpdate=!(l&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:u[c],focusedOptionId:this.getFocusedOptionId(u[c])},function(){return o.onMenuOpen()})}},{key:"focusValue",value:function(i){var o=this.state,s=o.selectValue,a=o.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var l=s.indexOf(a);a||(l=-1);var u=s.length-1,c=-1;if(s.length){switch(i){case"previous":l===0?c=0:l===-1?c=u:c=l-1;break;case"next":l>-1&&l<u&&(c=l+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:s[c]})}}}},{key:"focusOption",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",o=this.props.pageSize,s=this.state.focusedOption,a=this.getFocusableOptions();if(a.length){var l=0,u=a.indexOf(s);s||(u=-1),i==="up"?l=u>0?u-1:a.length-1:i==="down"?l=(u+1)%a.length:i==="pageup"?(l=u-o,l<0&&(l=0)):i==="pagedown"?(l=u+o,l>a.length-1&&(l=a.length-1)):i==="last"&&(l=a.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:a[l],focusedValue:null,focusedOptionId:this.getFocusedOptionId(a[l])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(bs):Ae(Ae({},bs),this.props.theme):bs}},{key:"getCommonProps",value:function(){var i=this.clearValue,o=this.cx,s=this.getStyles,a=this.getClassNames,l=this.getValue,u=this.selectOption,c=this.setValue,d=this.props,g=d.isMulti,h=d.isRtl,m=d.options,p=this.hasValue();return{clearValue:i,cx:o,getStyles:s,getClassNames:a,getValue:l,hasValue:p,isMulti:g,isRtl:h,options:m,selectOption:u,selectProps:d,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var i=this.state.selectValue;return i.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var i=this.props,o=i.isClearable,s=i.isMulti;return o===void 0?s:o}},{key:"isOptionDisabled",value:function(i,o){return If(this.props,i,o)}},{key:"isOptionSelected",value:function(i,o){return Ef(this.props,i,o)}},{key:"filterOption",value:function(i,o){return Tf(this.props,i,o)}},{key:"formatOptionLabel",value:function(i,o){if(typeof this.props.formatOptionLabel=="function"){var s=this.props.inputValue,a=this.state.selectValue;return this.props.formatOptionLabel(i,{context:o,inputValue:s,selectValue:a})}else return this.getOptionLabel(i)}},{key:"formatGroupLabel",value:function(i){return this.props.formatGroupLabel(i)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var i=this.props,o=i.isDisabled,s=i.isSearchable,a=i.inputId,l=i.inputValue,u=i.tabIndex,c=i.form,d=i.menuIsOpen,g=i.required,h=this.getComponents(),m=h.Input,p=this.state,v=p.inputIsHidden,w=p.ariaSelection,b=this.commonProps,x=a||this.getElementId("input"),O=Ae(Ae(Ae({"aria-autocomplete":"list","aria-expanded":d,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":g,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},d&&{"aria-controls":this.getElementId("listbox")}),!s&&{"aria-readonly":!0}),this.hasValue()?w?.action==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return s?f.createElement(m,Ne({},b,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:x,innerRef:this.getInputRef,isDisabled:o,isHidden:v,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:l},O)):f.createElement(qw,Ne({id:x,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:ha,onFocus:this.onInputFocus,disabled:o,tabIndex:u,inputMode:"none",form:c,value:""},O))}},{key:"renderPlaceholderOrValue",value:function(){var i=this,o=this.getComponents(),s=o.MultiValue,a=o.MultiValueContainer,l=o.MultiValueLabel,u=o.MultiValueRemove,c=o.SingleValue,d=o.Placeholder,g=this.commonProps,h=this.props,m=h.controlShouldRenderValue,p=h.isDisabled,v=h.isMulti,w=h.inputValue,b=h.placeholder,x=this.state,O=x.selectValue,R=x.focusedValue,M=x.isFocused;if(!this.hasValue()||!m)return w?null:f.createElement(d,Ne({},g,{key:"placeholder",isDisabled:p,isFocused:M,innerProps:{id:this.getElementId("placeholder")}}),b);if(v)return O.map(function(I,k){var A=I===R,D="".concat(i.getOptionLabel(I),"-").concat(i.getOptionValue(I));return f.createElement(s,Ne({},g,{components:{Container:a,Label:l,Remove:u},isFocused:A,isDisabled:p,key:D,index:k,removeProps:{onClick:function(){return i.removeValue(I)},onTouchEnd:function(){return i.removeValue(I)},onMouseDown:function(E){E.preventDefault()}},data:I}),i.formatOptionLabel(I,"value"))});if(w)return null;var _=O[0];return f.createElement(c,Ne({},g,{data:_,isDisabled:p}),this.formatOptionLabel(_,"value"))}},{key:"renderClearIndicator",value:function(){var i=this.getComponents(),o=i.ClearIndicator,s=this.commonProps,a=this.props,l=a.isDisabled,u=a.isLoading,c=this.state.isFocused;if(!this.isClearable()||!o||l||!this.hasValue()||u)return null;var d={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(o,Ne({},s,{innerProps:d,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var i=this.getComponents(),o=i.LoadingIndicator,s=this.commonProps,a=this.props,l=a.isDisabled,u=a.isLoading,c=this.state.isFocused;if(!o||!u)return null;var d={"aria-hidden":"true"};return f.createElement(o,Ne({},s,{innerProps:d,isDisabled:l,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator,s=i.IndicatorSeparator;if(!o||!s)return null;var a=this.commonProps,l=this.props.isDisabled,u=this.state.isFocused;return f.createElement(s,Ne({},a,{isDisabled:l,isFocused:u}))}},{key:"renderDropdownIndicator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator;if(!o)return null;var s=this.commonProps,a=this.props.isDisabled,l=this.state.isFocused,u={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(o,Ne({},s,{innerProps:u,isDisabled:a,isFocused:l}))}},{key:"renderMenu",value:function(){var i=this,o=this.getComponents(),s=o.Group,a=o.GroupHeading,l=o.Menu,u=o.MenuList,c=o.MenuPortal,d=o.LoadingMessage,g=o.NoOptionsMessage,h=o.Option,m=this.commonProps,p=this.state.focusedOption,v=this.props,w=v.captureMenuScroll,b=v.inputValue,x=v.isLoading,O=v.loadingMessage,R=v.minMenuHeight,M=v.maxMenuHeight,_=v.menuIsOpen,I=v.menuPlacement,k=v.menuPosition,A=v.menuPortalTarget,D=v.menuShouldBlockScroll,C=v.menuShouldScrollIntoView,E=v.noOptionsMessage,T=v.onMenuScrollToTop,S=v.onMenuScrollToBottom;if(!_)return null;var B=function(te,ue){var fe=te.type,se=te.data,H=te.isDisabled,P=te.isSelected,G=te.label,ce=te.value,he=p===se,Ie=H?void 0:function(){return i.onOptionHover(se)},me=H?void 0:function(){return i.selectOption(se)},Qe="".concat(i.getElementId("option"),"-").concat(ue),xe={id:Qe,onClick:me,onMouseMove:Ie,onMouseOver:Ie,tabIndex:-1,role:"option","aria-selected":i.isAppleDevice?void 0:P};return f.createElement(h,Ne({},m,{innerProps:xe,data:se,isDisabled:H,isSelected:P,key:Qe,label:G,type:fe,value:ce,isFocused:he,innerRef:he?i.getFocusedOptionRef:void 0}),i.formatOptionLabel(te.data,"menu"))},X;if(this.hasOptions())X=this.getCategorizedOptions().map(function(re){if(re.type==="group"){var te=re.data,ue=re.options,fe=re.index,se="".concat(i.getElementId("group"),"-").concat(fe),H="".concat(se,"-heading");return f.createElement(s,Ne({},m,{key:se,data:te,options:ue,Heading:a,headingProps:{id:H,data:re.data},label:i.formatGroupLabel(re.data)}),re.options.map(function(P){return B(P,"".concat(fe,"-").concat(P.index))}))}else if(re.type==="option")return B(re,"".concat(re.index))});else if(x){var Y=O({inputValue:b});if(Y===null)return null;X=f.createElement(d,m,Y)}else{var ae=E({inputValue:b});if(ae===null)return null;X=f.createElement(g,m,ae)}var Q={minMenuHeight:R,maxMenuHeight:M,menuPlacement:I,menuPosition:k,menuShouldScrollIntoView:C},ee=f.createElement(k1,Ne({},m,Q),function(re){var te=re.ref,ue=re.placerProps,fe=ue.placement,se=ue.maxHeight;return f.createElement(l,Ne({},m,Q,{innerRef:te,innerProps:{onMouseDown:i.onMenuMouseDown,onMouseMove:i.onMenuMouseMove},isLoading:x,placement:fe}),f.createElement(Zw,{captureEnabled:w,onTopArrive:T,onBottomArrive:S,lockEnabled:D},function(H){return f.createElement(u,Ne({},m,{innerRef:function(G){i.getMenuListRef(G),H(G)},innerProps:{role:"listbox","aria-multiselectable":m.isMulti,id:i.getElementId("listbox")},isLoading:x,maxHeight:se,focusedOption:p}),X)}))});return A||k==="fixed"?f.createElement(c,Ne({},m,{appendTo:A,controlElement:this.controlRef,menuPlacement:I,menuPosition:k}),ee):ee}},{key:"renderFormField",value:function(){var i=this,o=this.props,s=o.delimiter,a=o.isDisabled,l=o.isMulti,u=o.name,c=o.required,d=this.state.selectValue;if(c&&!this.hasValue()&&!a)return f.createElement(ey,{name:u,onFocus:this.onValueInputFocus});if(!(!u||a))if(l)if(s){var g=d.map(function(p){return i.getOptionValue(p)}).join(s);return f.createElement("input",{name:u,type:"hidden",value:g})}else{var h=d.length>0?d.map(function(p,v){return f.createElement("input",{key:"i-".concat(v),name:u,type:"hidden",value:i.getOptionValue(p)})}):f.createElement("input",{name:u,type:"hidden",value:""});return f.createElement("div",null,h)}else{var m=d[0]?this.getOptionValue(d[0]):"";return f.createElement("input",{name:u,type:"hidden",value:m})}}},{key:"renderLiveRegion",value:function(){var i=this.commonProps,o=this.state,s=o.ariaSelection,a=o.focusedOption,l=o.focusedValue,u=o.isFocused,c=o.selectValue,d=this.getFocusableOptions();return f.createElement(Vw,Ne({},i,{id:this.getElementId("live-region"),ariaSelection:s,focusedOption:a,focusedValue:l,isFocused:u,selectValue:c,focusableOptions:d,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var i=this.getComponents(),o=i.Control,s=i.IndicatorsContainer,a=i.SelectContainer,l=i.ValueContainer,u=this.props,c=u.className,d=u.id,g=u.isDisabled,h=u.menuIsOpen,m=this.state.isFocused,p=this.commonProps=this.getCommonProps();return f.createElement(a,Ne({},p,{className:c,innerProps:{id:d,onKeyDown:this.onKeyDown},isDisabled:g,isFocused:m}),this.renderLiveRegion(),f.createElement(o,Ne({},p,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:g,isFocused:m,menuIsOpen:h}),f.createElement(l,Ne({},p,{isDisabled:g}),this.renderPlaceholderOrValue(),this.renderInput()),f.createElement(s,Ne({},p,{isDisabled:g}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(i,o){var s=o.prevProps,a=o.clearFocusValueOnUpdate,l=o.inputIsHiddenAfterUpdate,u=o.ariaSelection,c=o.isFocused,d=o.prevWasFocused,g=o.instancePrefix,h=i.options,m=i.value,p=i.menuIsOpen,v=i.inputValue,w=i.isMulti,b=ic(m),x={};if(s&&(m!==s.value||h!==s.options||p!==s.menuIsOpen||v!==s.inputValue)){var O=p?py(i,b):[],R=p?wc(ea(i,b),"".concat(g,"-option")):[],M=a?vy(o,b):null,_=by(o,O),I=ws(R,_);x={selectValue:b,focusedOption:_,focusedOptionId:I,focusableOptionsWithIds:R,focusedValue:M,clearFocusValueOnUpdate:!1}}var k=l!=null&&i!==s?{inputIsHidden:l,inputIsHiddenAfterUpdate:void 0}:{},A=u,D=c&&d;return c&&!D&&(A={value:jo(w,b,b[0]||null),options:b,action:"initial-input-focus"},D=!d),u?.action==="initial-input-focus"&&(A=null),Ae(Ae(Ae({},x),k),{},{prevProps:i,ariaSelection:A,prevWasFocused:D})}}]),n}(f.Component);Of.defaultProps=my;var yy=f.forwardRef(function(e,t){var n=qb(e);return f.createElement(Of,Ne({ref:t},n))}),Cy=yy;const Sy=e=>{const{Menu:t}=yf,{children:n,...r}=e;return f.createElement(t,{...r},n)},xy=un("div")({name:"Wrap",class:"gdg-wghi2zc",propsAsIs:!1}),ky=un("div")({name:"PortalWrap",class:"gdg-p13nj8j0",propsAsIs:!1}),My=un("div")({name:"ReadOnlyWrap",class:"gdg-r6sia3g",propsAsIs:!1}),Ry=e=>{const{value:t,onFinishedEditing:n,initialValue:r,portalElementRef:i}=e,{allowedValues:o,value:s}=t.data,[a,l]=f.useState(s),[u,c]=f.useState(r??""),d=Qm(),g=f.useMemo(()=>o.map(h=>typeof h=="string"||h===null||h===void 0?{value:h,label:h?.toString()??""}:h),[o]);return t.readonly?f.createElement(My,null,f.createElement(qr,{highlight:!0,autoFocus:!1,disabled:!0,value:a??"",onChange:()=>{}})):f.createElement(xy,null,f.createElement(Cy,{className:"glide-select",inputValue:u,onInputChange:c,menuPlacement:"auto",value:g.find(h=>h.value===a),styles:{control:h=>({...h,border:0,boxShadow:"none"}),option:(h,{isFocused:m})=>({...h,fontSize:d.editorFontSize,fontFamily:d.fontFamily,cursor:m?"pointer":void 0,paddingLeft:d.cellHorizontalPadding,paddingRight:d.cellHorizontalPadding,":active":{...h[":active"],color:d.accentFg},":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})},theme:h=>({...h,colors:{...h.colors,neutral0:d.bgCell,neutral5:d.bgCell,neutral10:d.bgCell,neutral20:d.bgCellMedium,neutral30:d.bgCellMedium,neutral40:d.bgCellMedium,neutral50:d.textLight,neutral60:d.textMedium,neutral70:d.textMedium,neutral80:d.textDark,neutral90:d.textDark,neutral100:d.textDark,primary:d.accentColor,primary75:d.accentColor,primary50:d.accentColor,primary25:d.accentLight}}),menuPortalTarget:i?.current??document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:h=>f.createElement(ky,null,f.createElement(Sy,{className:"click-outside-ignore",...h}))},options:g,onChange:async h=>{h!==null&&(l(h.value),await new Promise(m=>window.requestAnimationFrame(m)),n({...t,data:{...t.data,value:h.value}}))}}))},Iy={kind:J.Custom,isMatch:e=>e.data.kind==="dropdown-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{value:o}=t.data,s=t.data.allowedValues.find(l=>typeof l=="string"||l===null||l===void 0?l===o:l.value===o),a=typeof s=="string"?s:s?.label??"";return a&&(n.fillStyle=r.textDark,n.fillText(a,i.x+r.cellHorizontalPadding,i.y+i.height/2+Qn(n,r))),!0},measure:(e,t,n)=>{const{value:r}=t.data;return(r?e.measureText(r).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Ry,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.some(n=>n==null?!1:typeof n=="string"?n===e:n.value===e)?e:t.value})};function Ey(e,t){const n=/(\d+\.?\d*)\s*(px|rem|em|%|pt)/,r=e.match(n);if(r){const i=parseFloat(r[1]),o=r[2],s=i*t;return e.replace(n,`${Number(s.toPrecision(3))}${o}`)}return e}const Ty={marginRight:8},Dy={display:"flex",alignItems:"center",flexGrow:1},Oy={kind:J.Custom,isMatch:e=>e.data.kind==="range-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{min:o,max:s,value:a,label:l,measureLabel:u}=t.data,c=i.x+r.cellHorizontalPadding,d=i.y+i.height/2,g=s-o,h=(a-o)/g,m=`${Ey(r.baseFontStyle,.9)} ${r.fontFamily}`,v=Gs(n,m)/2;n.save();let w=0;l!==void 0&&(n.font=m,w=Xr(u??l,n,m).width+r.cellHorizontalPadding);const b=i.width-r.cellHorizontalPadding*2-w;if(b>=v){const x=n.createLinearGradient(c,d,c+b,d);x.addColorStop(0,r.accentColor),x.addColorStop(h,r.accentColor),x.addColorStop(h,r.bgBubble),x.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=x,Qu(n,c,d-v/2,b,v,v/2),n.fill(),n.beginPath(),Qu(n,c+.5,d-v/2+.5,b-1,v-1,(v-1)/2),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return l!==void 0&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(l,i.x+i.width-r.cellHorizontalPadding,d+Qn(n,m))),n.restore(),!0},provideEditor:()=>e=>{const{data:t,readonly:n}=e.value,r=t.value.toString(),i=t.min.toString(),o=t.max.toString(),s=t.step.toString(),a=l=>{e.onChange({...e.value,data:{...t,value:Number(l.target.value)}})};return f.createElement("label",{style:Dy},f.createElement("input",{style:Ty,type:"range",value:r,min:i,max:o,step:s,onChange:a,disabled:n}),r)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}},Py=un("input")({name:"StyledInputBox",class:"gdg-s1wtovjx",propsAsIs:!1}),ys=(e,t,n)=>{if(t==null)return"";n&&(t=new Date(t.getTime()+n));const r=t.toISOString();switch(e){case"date":return r.split("T")[0];case"datetime-local":return r.replace("Z","");case"time":return r.split("T")[1].replace("Z","");default:throw new Error(`Unknown date kind ${e}`)}},_y=e=>{const t=e.value.data,{format:n,displayDate:r}=t,i=t.step!==void 0&&!Number.isNaN(Number(t.step))?Number(t.step):void 0,o=t.timezoneOffset?t.timezoneOffset*60*1e3:0,s=t.min instanceof Date?ys(n,t.min,o):t.min,a=t.max instanceof Date?ys(n,t.max,o):t.max,l=ys(n,t.date,o);return e.value.readonly?Vt.createElement(qr,{highlight:!0,autoFocus:!1,disabled:!0,value:r??"",onChange:()=>{}}):Vt.createElement(Py,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:l,min:s,max:a,step:i,autoFocus:!0,onChange:u=>{isNaN(u.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(u.target.valueAsNumber-o)}})}})},Fy={kind:J.Custom,isMatch:e=>e.data.kind==="date-picker-cell",draw:(e,t)=>{const{displayDate:n}=t.data;return qs(e,n,t.contentAlign),!0},measure:(e,t,n)=>{const{displayDate:r}=t.data;return e.measureText(r).width+n.cellHorizontalPadding*2},provideEditor:()=>({editor:_y}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),t.format==="time"&&Number.isNaN(n)&&(n=Date.parse(`1970-01-01T${e}Z`)))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}},Ly="None";function Cc(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-n.cellHorizontalPadding,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+n.cellHorizontalPadding),e.fillStyle=n.accentColor,e.fill(),e.restore()}const Ay=e=>{const{cell:t,theme:n,ctx:r}=e;qs({...e,theme:{...n,textDark:n.textLight,headerFontFull:`${n.headerFontStyle} ${n.fontFamily}`,baseFontFull:`${n.baseFontStyle} ${n.fontFamily}`,markerFontFull:`${n.markerFontStyle} ${n.fontFamily}`}},Ly,t.contentAlign),r.fillStyle=n.textDark};function Hy(e){const t=f.useCallback((r,i)=>{const{cell:o,theme:s,ctx:a,rect:l}=r,u=r.col;if(pi(o))Cc(a,l,s);else if(Ca(o)&&u<e.length){const c=e[u];["checkbox","line_chart","bar_chart","progress"].includes(c.kind)?i():Ay(r),c.isRequired&&c.isEditable&&Cc(a,l,s);return}i()},[e]),n=f.useMemo(()=>[Wb,Iy,Oy,Fy,...vb],[]);return{drawCell:t,customRenderers:n}}function zy(){const e=Wr();return f.useMemo(()=>{const n={editable:i=>`<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="${i.bgColor}"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>`};return{glideTheme:{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:ui(e.colors.primary,.9),borderColor:e.colors.dataframeBorderColor,horizontalBorderColor:e.colors.dataframeBorderColor,fontFamily:e.genericFonts.bodyFont,bgSearchResult:ui(e.colors.primary,.9),resizeIndicatorColor:e.colors.primary,bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.dataframeHeaderBackgroundColor,bgHeaderHasFocus:ui(e.colors.darkenedBgMix100,.9),bgHeaderHovered:ui(e.colors.darkenedBgMix100,.9),textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerIconSize:Math.round(an("1.125rem")),headerFontStyle:`${e.fontWeights.normal} ${an(e.fontSizes.sm)}px`,baseFontStyle:`${e.fontWeights.normal} ${an(e.fontSizes.sm)}px`,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:ui(e.colors.bodyText,.2),textLight:e.colors.fadedText40,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:Math.round(an(e.spacing.sm)),cellVerticalPadding:Math.round(an("0.1875rem")),textBubble:e.colors.fadedText60,bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,bubbleHeight:Math.round(an("1.25rem")),bubblePadding:Math.round(an(e.spacing.sm)),bubbleMargin:Math.round(an(e.spacing.twoXS)),linkColor:e.colors.link,drilldownBorder:e.colors.darkenedBgMix25,checkboxMaxSize:Math.round(an(e.sizes.checkbox))},tableBorderRadius:e.radii.default,tableBorderWidth:1,defaultTableHeight:Math.round(an("25rem")),minColumnWidth:Math.round(an("3.125rem")),maxColumnWidth:Math.round(an("62.5rem")),maxColumnAutoWidth:Math.round(an("31.25rem")),defaultRowHeight:Math.round(an("2.1875rem")),defaultHeaderHeight:Math.round(an("2.1875rem")),bgRowHovered:og(e.colors.bgColor,e.colors.secondaryBg,.3),headerIcons:n}},[e])}const Vy=pa.getLogger("useDataEditor");function $y(e,t,n,r,i,o,s,a,l){const u=f.useCallback(([p,v],w)=>{const b=e[p];if(!b.isEditable)return;const x=b.indexNumber,O=n.current.getOriginalRowIndex(i(v)),R=r([p,v]),M=b.getCellValue(R),_=b.getCellValue(w);if(!pi(R)&&_===M)return;const I=b.getCell(_,!0);pi(I)?Vy.warn(`Not applying the cell edit since it causes this error:
 ${I.data}`):(n.current.setCell(x,O,{...I,lastUpdated:performance.now()}),a())},[e,n,i,r,a]),c=f.useCallback(()=>{if(t)return;const p=new Map;e.forEach(v=>{p.set(v.indexNumber,v.getCell(v.defaultValue))}),n.current.addRow(p),s()},[e,n,t,s]),d=f.useCallback(()=>{t||(c(),a())},[c,a,t]),g=f.useCallback(p=>{if(p.rows.length>0){if(t)return!0;const v=p.rows.toArray().map(w=>n.current.getOriginalRowIndex(i(w)));return n.current.deleteRows(v),s(),l(),a(),!1}if(p.current?.range){const v=[],w=p.current.range;for(let b=w.y;b<w.y+w.height;b++)for(let x=w.x;x<w.x+w.width;x++){const O=e[x];O.isEditable&&!O.isRequired&&(v.push({cell:[x,b]}),u([x,b],O.getCell(null)))}return v.length>0&&(a(),o(v)),!1}return!0},[e,n,t,o,i,a,u,l,s]),h=f.useCallback((p,v)=>{const[w,b]=p,x=[];for(let O=0;O<v.length;O++){const R=v[O];if(O+b>=n.current.getNumRows()){if(t)break;c()}for(let M=0;M<R.length;M++){const _=R[M],I=O+b,k=M+w;if(k>=e.length)break;const A=e[k];if(A.isEditable){const D=A.getCell(_,!0);if(lt(D)&&!pi(D)){const C=A.indexNumber,E=n.current.getOriginalRowIndex(i(I)),T=A.getCellValue(r([k,I]));A.getCellValue(D)!==T&&(n.current.setCell(C,E,{...D,lastUpdated:performance.now()}),x.push({cell:[k,I]}))}}}x.length>0&&(a(),o(x))}return!1},[e,n,t,i,r,c,a,o]),m=f.useCallback((p,v)=>{const w=p[0];if(w>=e.length)return!0;const b=e[w];if(b.validateInput){const x=b.validateInput(b.getCellValue(v));return x===!0||x===!1?x:b.getCell(x)}return!0},[e]);return{onCellEdited:u,onPaste:h,onRowAppended:d,onDelete:g,validateCell:m}}const Pf=",",Ki='"',Ny='"',_f=`
`,By="\uFEFF",Wy=new RegExp(`[${[Pf,Ki,_f].join("")}]`),Sc=pa.getLogger("useDataExporter");function xc(e){return e.map(t=>Uy(t)).join(Pf)+_f}function Uy(e){if(He(e))return"";const t=mt(e);return Wy.test(t)?`${Ki}${t.replace(new RegExp(Ki,"g"),Ny+Ki)}${Ki}`:t}async function kc(e,t,n,r){const i=new TextEncoder;await e.write(i.encode(By));const o=n.map(s=>s.name);await e.write(i.encode(xc(o)));for(let s=0;s<r;s++){const a=[];n.forEach((l,u,c)=>{a.push(l.getCellValue(t([u,s])))}),await e.write(i.encode(xc(a)))}await e.close()}function qy(e,t,n,r){return{exportToCsv:f.useCallback(async()=>{const s=`${new Date().toISOString().slice(0,16).replace(":","-")}_export.csv`;try{const u=await(await(await As(()=>import("./es6.CMaUdEZ5.js").then(c=>c.a),__vite__mapDeps([15,1,2]),import.meta.url)).showSaveFilePicker({suggestedName:s,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1})).createWritable();await kc(u,e,t,n)}catch(a){if(a instanceof Error&&a.name==="AbortError")return;try{Sc.warn("Failed to export data as CSV with FileSystem API, trying fallback method",a);let l="";const u=new WritableStream({write:h=>{l+=new TextDecoder("utf-8").decode(h)},close:async()=>{}});await kc(u.getWriter(),e,t,n);const c=new Blob([l],{type:"text/csv;charset=utf-8;"}),d=URL.createObjectURL(c),g=Sg({enforceDownloadInNewTab:r,url:d,filename:s});g.style.display="none",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(d)}catch(l){Sc.error("Failed to export data as CSV",l)}}},[t,n,e,r])}}function Gy(e,t,n,r){return{getCellContent:f.useCallback(([o,s])=>{if(o>t.length-1)return Et("Column index out of bounds","This error should never happen. Please report this bug.");if(s>n-1)return Et("Row index out of bounds","This error should never happen. Please report this bug.");try{const a=t[o],l=a.indexNumber,u=r.current.getOriginalRowIndex(s),c=r.current.isAddedRow(u);if(a.isEditable||c){const h=r.current.getCell(l,u);if(lt(h))return{...a.getCell(a.getCellValue(h),!1),lastUpdated:h.lastUpdated};if(c)return Et("Error during cell creation",`This error should never happen. Please report this bug. No cell found for an added row: col=${l}; row=${u}`)}const d=e.getCell(u,l),g=ag(e,u,l);return Ib(a,d,g,e.styler?.cssStyles)}catch(a){return Et("Error during cell creation",`This error should never happen. Please report this bug. 
Error: ${a}`)}},[t,n,e,r])}}function Xy(e){const[t,n]=f.useState(void 0),r=f.useCallback(o=>{if(o.kind!=="cell")n(void 0);else{const[,s]=o.location;n(s)}},[n]);return{getRowThemeOverride:f.useCallback(o=>{if(o===t)return{bgCell:e.bgRowHovered,bgCellMedium:e.bgRowHovered}},[e.bgRowHovered,t]),onItemHovered:r}}function Yy(e,t,n,r,i){const[o,s]=f.useState({columns:ot.empty(),rows:ot.empty(),current:void 0}),a=!t&&!n&&(e.selectionMode.includes(wn.SelectionMode.MULTI_ROW)||e.selectionMode.includes(wn.SelectionMode.SINGLE_ROW)),l=a&&e.selectionMode.includes(wn.SelectionMode.MULTI_ROW),u=!t&&!n&&(e.selectionMode.includes(wn.SelectionMode.SINGLE_COLUMN)||e.selectionMode.includes(wn.SelectionMode.MULTI_COLUMN)),c=u&&e.selectionMode.includes(wn.SelectionMode.MULTI_COLUMN),d=o.rows.length>0,g=o.columns.length>0,h=o.current!==void 0,m=f.useCallback(v=>{const w=!Ga(v.rows.toArray(),o.rows.toArray()),b=!Ga(v.columns.toArray(),o.columns.toArray()),x=!Ga(v.current,o.current);let O=a&&w||u&&b,R=v;if((a||u)&&v.current!==void 0&&x&&(R={...v,rows:o.rows,columns:o.columns},O=!1),w&&v.rows.length>0&&b&&v.columns.length===0&&(R={...R,columns:o.columns},O=!0),b&&v.columns.length>0&&w&&v.rows.length===0&&(R={...R,rows:o.rows},O=!0),b&&R.columns.length>=0){let M=R.columns;r.forEach((_,I)=>{_.isIndex&&(M=M.remove(I))}),M.length<R.columns.length&&(R={...R,columns:M})}s(R),O&&i(R)},[o,a,u,i,r]),p=f.useCallback((v=!1,w=!1)=>{const b={columns:w?o.columns:ot.empty(),rows:v?o.rows:ot.empty(),current:void 0};s(b),(!v&&a||!w&&u)&&i(b)},[o,a,u,i]);return{gridSelection:o,isRowSelectionActivated:a,isMultiRowSelectionActivated:l,isColumnSelectionActivated:u,isMultiColumnSelectionActivated:c,isRowSelected:d,isColumnSelected:g,isCellSelected:h,clearSelection:p,processSelectionChange:m}}function jy(e,t,n,r,i,o,s){const a=e.rowHeight??t.defaultRowHeight,l=t.defaultHeaderHeight+a+2*t.tableBorderWidth,u=r?2:1,c=e.editingMode===wn.EditingMode.DYNAMIC?1:0,d=n+c;let g=Math.max(d*a+u*t.defaultHeaderHeight+2*t.tableBorderWidth,l),h=Math.min(g,t.defaultTableHeight);e.height&&(h=Math.max(e.height,l),g=Math.max(e.height,g)),o&&(h=Math.min(h,o),g=Math.min(g,o),e.height||(h=g));const m=t.minColumnWidth+2*t.tableBorderWidth,p=Math.max(i,m);let v,w=p;e.useContainerWidth?v=p:e.width&&(v=Math.min(Math.max(e.width,m),p),w=Math.min(Math.max(e.width,w),p));const[b,x]=f.useState({width:v||"100%",height:h});return f.useLayoutEffect(()=>{e.useContainerWidth&&b.width==="100%"&&x(O=>({...O,width:p}))},[p]),f.useLayoutEffect(()=>{x(O=>({...O,width:v||"100%"}))},[v]),f.useLayoutEffect(()=>{x(O=>({...O,height:h}))},[h,n]),f.useLayoutEffect(()=>{if(s){const O=e.useContainerWidth||lt(e.width)&&e.width>0;x({width:O?w:"100%",height:g})}else x({width:v||"100%",height:h})},[s]),{minHeight:l,maxHeight:g,minWidth:m,maxWidth:w,rowHeight:a,resizableSize:b,setResizableSize:x}}const Ky=600,Zy="⚠️ Please fill out this cell.";function Jy(e,t,n=[]){const[r,i]=f.useState(),o=f.useRef(null),s=f.useCallback(l=>{if(clearTimeout(o.current),o.current=0,i(void 0),(l.kind==="header"||l.kind==="cell")&&l.location){const u=l.location[0],c=l.location[1];let d;if(u<0||u>=e.length||n.includes(c))return;const g=e[u];if(l.kind==="header"&&lt(g))d=g.help;else if(l.kind==="cell"){const h=t([u,c]);pi(h)?d=h.errorDetails:g.isRequired&&g.isEditable&&Ca(h)?d=Zy:ib(h)&&(d=h.tooltip)}d&&(o.current=setTimeout(()=>{d&&i({content:d,left:l.bounds.x+l.bounds.width/2,top:l.bounds.y})},Ky))}},[e,t,i,o,n]),a=f.useCallback(()=>{i(void 0)},[i]);return{tooltip:r,clearTooltip:a,onItemHovered:s}}const Ff=Un("div",{target:"e32gfpu0"})(({theme:e})=>({paddingTop:e.spacing.xs,paddingBottom:e.spacing.xs})),xr=Un("div",{target:"e32gfpu1"})(({theme:e,isActive:t,hasSubmenu:n})=>({display:"flex",alignItems:"center",justifyContent:"flex-start",gap:e.spacing.sm,paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm,paddingTop:e.spacing.twoXS,paddingBottom:e.spacing.twoXS,cursor:"pointer",backgroundColor:t?e.colors.darkenedBgMix15:void 0,"&:hover":{backgroundColor:e.colors.darkenedBgMix15},minWidth:e.sizes.minMenuWidth,...n&&{justifyContent:"space-between","& > :first-of-type":{display:"flex",alignItems:"center",gap:e.spacing.sm}}})),Qy=Un("div",{target:"e32gfpu2"})(({theme:e})=>({height:e.sizes.borderWidth,backgroundColor:e.colors.borderColor,marginTop:e.spacing.xs,marginBottom:e.spacing.xs})),eC=Un("div",{target:"e32gfpu3"})(({theme:e})=>({display:"flex",alignItems:"center",padding:`${e.spacing.sm} ${e.spacing.sm}`,cursor:"default",gap:e.spacing.twoXS})),tC=Un("div",{target:"e32gfpu4"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",padding:e.spacing.twoXS,border:`${e.sizes.borderWidth} solid ${e.colors.borderColor}`,borderRadius:e.radii.md,backgroundColor:"transparent",color:e.colors.bodyText,height:"fit-content"})),nC=Un("div",{target:"e32gfpu5"})(({theme:e})=>({display:"flex",alignItems:"center",flexGrow:1,padding:`${e.spacing.threeXS} ${e.spacing.threeXS}`,border:`${e.sizes.borderWidth} solid ${e.colors.borderColor}`,borderRadius:e.radii.md,backgroundColor:e.colors.secondaryBg,minWidth:0,overflow:"hidden"})),rC=Un("span",{target:"e32gfpu6"})(({theme:e})=>({whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",flexGrow:1,margin:`0 ${e.spacing.xs}`,fontSize:e.fontSizes.twoSm,maxWidth:"4rem"})),iC=Un("button",{target:"e32gfpu7"})(({theme:e})=>({background:"none",border:"none",padding:e.spacing.twoXS,cursor:"pointer",color:e.colors.bodyText,display:"flex",alignItems:"center",justifyContent:"center",borderRadius:e.radii.md,transition:"background-color 0.2s ease","&:hover":{backgroundColor:e.colors.fadedText05},"&:active":{backgroundColor:e.colors.fadedText10}})),Mc=[{format:"",label:"Automatic",icon:":material/123:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"plain",label:"Plain",icon:":material/speed_1_75:"},{format:"compact",label:"Compact",icon:":material/1k:"},{format:"dollar",label:"Dollar",icon:":material/attach_money:"},{format:"euro",label:"Euro",icon:":material/euro:"},{format:"yen",label:"Yen",icon:":material/currency_yen:"},{format:"percent",label:"Percent",icon:":material/percent:"},{format:"scientific",label:"Scientific",icon:":material/experiment:"},{format:"accounting",label:"Accounting",icon:":material/finance_chip:"}],oC={number:Mc,progress:Mc,datetime:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"},{format:"calendar",label:"Calendar",icon:":material/today:"}],date:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"}],time:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"}]};function aC({columnKind:e,isOpen:t,onMouseEnter:n,onMouseLeave:r,onChangeFormat:i,onCloseMenu:o,children:s}){const a=Wr(),{colors:l,fontSizes:u,radii:c,fontWeights:d}=a,g=oC[e]||[];return g.length===0?Ue(Vc,{}):Ue(wa,{triggerType:$c.hover,returnFocus:!0,autoFocus:!0,focusLock:!0,isOpen:t,onMouseEnter:n,onMouseLeave:r,ignoreBoundary:!0,content:Ue(Ff,{role:"menu",children:g.map(h=>vn(xr,{onClick:()=>{i(h.format),o()},role:"menuitem",children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:h.icon}),h.label]},h.format))}),placement:va.right,showArrow:!1,popoverMargin:2,overrides:{Body:{props:{"data-testid":"stDataFrameColumnFormattingMenu"},style:{borderTopLeftRadius:c.default,borderTopRightRadius:c.default,borderBottomLeftRadius:c.default,borderBottomRightRadius:c.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${a.sizes.borderWidth} solid ${a.colors.borderColor}`}},Inner:{style:{backgroundColor:ba(a)?l.bgColor:l.secondaryBg,color:l.bodyText,fontSize:u.sm,fontWeight:d.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:s})}const sC=f.memo(aC),Rc=pa.getLogger("ColumnMenu");function lC({top:e,left:t,isColumnPinned:n,onPinColumn:r,onUnpinColumn:i,onCloseMenu:o,onSortColumn:s,onHideColumn:a,column:l,onChangeFormat:u,onAutosize:c}){const d=Wr(),[g,h]=f.useState(!1),{colors:m,fontSizes:p,radii:v,fontWeights:w}=d;f.useEffect(()=>{function O(R){R.preventDefault()}return document.addEventListener("wheel",O,{passive:!1}),document.addEventListener("touchmove",O,{passive:!1}),()=>{document.removeEventListener("wheel",O),document.removeEventListener("touchmove",O)}},[]);const b=f.useCallback(()=>{o()},[o]),x=f.useCallback(()=>{navigator.clipboard?navigator.clipboard.writeText(l.title).catch(O=>{Rc.error("Failed to copy column name to clipboard:",O)}):Rc.error("Clipboard API not supported.")},[l.title]);return Ue(wa,{autoFocus:!0,"aria-label":"Dataframe column menu",content:vn(Ff,{children:[vn(eC,{children:[Ue(tC,{title:l.kind,children:Ue(Bn,{iconValue:l.typeIcon||":material/notes:",size:"base",color:"inherit"})}),vn(nC,{title:l.title,children:[Ue(rC,{children:l.title}),Ue(iC,{onClick:x,title:"Copy column name","aria-label":"Copy column name",children:Ue(Bn,{iconValue:":material/content_copy:",size:"sm",margin:"0",color:"inherit"})})]})]}),s&&vn(Vc,{children:[vn(xr,{onClick:()=>{s("asc"),b()},role:"menuitem",children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_upward:"}),"Sort ascending"]}),vn(xr,{onClick:()=>{s("desc"),b()},role:"menuitem",children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_downward:"}),"Sort descending"]}),Ue(Qy,{})]}),u&&Ue(sC,{columnKind:l.kind,isOpen:g,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),onChangeFormat:u,onCloseMenu:b,children:vn(xr,{onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),isActive:g,hasSubmenu:!0,children:[vn("div",{children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/format_list_numbered:"}),"Format"]}),Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/chevron_right:"})]})}),c&&vn(xr,{onClick:()=>{c(),b()},children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrows_outward:"}),"Autosize"]}),n&&vn(xr,{onClick:()=>{i(),b()},children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep_off:"}),"Unpin column"]}),!n&&vn(xr,{onClick:()=>{r(),b()},children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep:"}),"Pin column"]}),a&&vn(xr,{onClick:()=>{a(),b()},children:[Ue(Bn,{size:"base",margin:"0",color:"inherit",iconValue:":material/visibility_off:"}),"Hide column"]})]}),placement:va.bottomRight,accessibilityType:Nc.menu,showArrow:!1,popoverMargin:an("0.375rem"),onClickOutside:g?void 0:b,onEsc:b,overrides:{Body:{props:{"data-testid":"stDataFrameColumnMenu"},style:{paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{border:`${d.sizes.borderWidth} solid ${d.colors.borderColor}`,backgroundColor:ba(d)?m.bgColor:m.secondaryBg,color:m.bodyText,fontSize:p.sm,fontWeight:w.normal,borderTopLeftRadius:v.default,borderTopRightRadius:v.default,borderBottomLeftRadius:v.default,borderBottomRightRadius:v.default,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:!0,children:Ue("div",{"data-testid":"stDataFrameColumnMenuTarget",style:{position:"fixed",top:e,left:t,visibility:"hidden",transform:"unset"}})})}const uC=f.memo(lC),cC="(index)",dC=({label:e,initialValue:t,onChange:n})=>{const r=Wr();return Ue(Cg,{checked:t,onChange:i=>{n(i.target.checked)},"aria-label":e,checkmarkType:yg.default,labelPlacement:wg.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:r.spacing.none,marginTop:r.spacing.none,paddingLeft:r.spacing.md,paddingRight:r.spacing.md,paddingTop:r.spacing.twoXS,paddingBottom:r.spacing.twoXS,backgroundColor:i?r.colors.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Checkmark:{style:({$isFocusVisible:i,$checked:o})=>{const s=o?r.colors.primary:r.colors.fadedText40;return{outline:0,width:r.sizes.checkbox,height:r.sizes.checkbox,marginTop:r.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&o?`0 0 0 0.2rem ${ui(r.colors.primary,.5)}`:"",borderLeftWidth:r.sizes.borderWidth,borderRightWidth:r.sizes.borderWidth,borderTopWidth:r.sizes.borderWidth,borderBottomWidth:r.sizes.borderWidth,borderLeftColor:s,borderRightColor:s,borderTopColor:s,borderBottomColor:s}}},Label:{style:{lineHeight:r.lineHeights.small,paddingLeft:r.spacing.sm,position:"relative",color:r.colors.bodyText,fontSize:r.fontSizes.sm,fontWeight:r.fontWeights.normal}}},children:e})},fC=({columns:e,columnOrder:t,setColumnOrder:n,hideColumn:r,showColumn:i,children:o,isOpen:s,onClose:a})=>{const l=Wr();return Ue(wa,{triggerType:$c.click,placement:va.bottomRight,autoFocus:!0,focusLock:!0,content:()=>Ue("div",{style:{paddingTop:l.spacing.sm,paddingBottom:l.spacing.sm},children:e.map(u=>{const c=t.length&&!u.isIndex?!t.includes(u.id)&&!t.includes(u.name):!1;return Ue(dC,{label:!u.title&&u.isIndex?cC:u.title,initialValue:!(u.isHidden===!0||c),onChange:d=>{d?(i(u.id),c&&n(g=>[...g,u.id])):r(u.id)}},u.id)})}),isOpen:s,onClickOutside:a,onClick:()=>s?a():void 0,onEsc:a,ignoreBoundary:!1,overrides:{Body:{props:{"data-testid":"stDataFrameColumnVisibilityMenu"},style:{borderTopLeftRadius:l.radii.default,borderTopRightRadius:l.radii.default,borderBottomLeftRadius:l.radii.default,borderBottomRightRadius:l.radii.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${l.sizes.borderWidth} solid ${l.colors.borderColor}`}},Inner:{style:{backgroundColor:ba(l)?l.colors.bgColor:l.colors.secondaryBg,color:l.colors.bodyText,fontSize:l.fontSizes.sm,fontWeight:l.fontWeights.normal,minWidth:l.sizes.minMenuWidth,maxWidth:`calc(${l.sizes.minMenuWidth} * 2)`,maxHeight:l.sizes.maxDropdownHeight,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:Ue("div",{children:o})})},hC=f.memo(fC),Ic=Un("div",{target:"e8es05k0"})(({theme:e,isInHorizontalLayout:t})=>({position:"relative",display:t?"flex":"inline-block","& .stDataFrameGlideDataEditor":{height:"100%",minWidth:"100%",borderRadius:e.radii.default},"& .dvn-scroller":{overflowX:"auto !important",overflowY:"auto !important"},"& .gdg-search-bar":{maxWidth:"19rem",width:"80%",minWidth:"6rem",top:e.spacing.sm,right:e.spacing.sm,padding:e.spacing.sm,borderRadius:e.radii.default,"& .gdg-search-status":{paddingTop:e.spacing.twoXS,fontSize:e.fontSizes.twoSm},"& .gdg-search-progress":{display:"none"},"& input":{width:"100%"},"& button":{width:e.iconSizes.xl,height:e.iconSizes.xl,"& .button-icon":{width:e.iconSizes.base,height:e.iconSizes.base}}}}));function gC({top:e,left:t,content:n,clearTooltip:r}){const[i,o]=f.useState(!0),s=Wr(),{colors:a,fontSizes:l,radii:u,fontWeights:c}=s,d=f.useCallback(()=>{o(!1),r()},[r,o]);return Ue(wa,{content:Ue(sg,{"data-testid":"stDataFrameTooltipContent",children:Ue(lg,{style:{fontSize:l.sm},source:n,allowHTML:!1})}),placement:va.top,accessibilityType:Nc.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:d,onEsc:d,overrides:{Body:{style:{borderTopLeftRadius:u.default,borderTopRightRadius:u.default,borderBottomLeftRadius:u.default,borderBottomRightRadius:u.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:ba(s)?a.bgColor:a.secondaryBg,color:a.bodyText,fontSize:l.sm,fontWeight:c.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:i,children:Ue("div",{"data-testid":"stDataFrameTooltipTarget",style:{position:"fixed",top:e,left:t}})})}const mC=f.memo(gC),Ec=150,pC=15e4,vC="0.5rem";function bC({element:e,data:t,disabled:n,widgetMgr:r,disableFullscreenMode:i,fragmentId:o}){const{expanded:s,expand:a,collapse:l,width:u,height:c}=Pl(gg),{isInHorizontalLayout:d}=Pl(ug),g=f.useRef(null),h=f.useRef(null),m=f.useRef(null),p=cg(),v=zy(),{getRowThemeOverride:w,onItemHovered:b}=Xy(v),{libConfig:{enforceDownloadInNewTab:x=!1}}=f.useContext(dg),[O,R]=f.useState(!0),[M,_]=f.useState(!1),[I,k]=f.useState(!1),[A,D]=f.useState(!1),[C,E]=f.useState(),[T,S]=f.useState(!1),B=f.useCallback(()=>S(be=>!be),[]),X=f.useCallback(()=>S(!1),[]),Y=f.useMemo(()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches,[]);He(e.editingMode)&&(e.editingMode=wn.EditingMode.READ_ONLY);const{READ_ONLY:ae,DYNAMIC:Q}=wn.EditingMode,ee=t.dimensions,re=Math.max(0,ee.numDataRows),te=re===0&&!(e.editingMode===Q&&ee.numDataColumns>0),ue=re>pC,fe=!ue&&!te&&e.editingMode!==Q,se=!te&&e.editingMode===Q&&!n,H=f.useRef(new qo(re)),[P,G]=f.useState(H.current.getNumRows());f.useEffect(()=>{H.current=new qo(re),G(H.current.getNumRows())},[re]);const ce=f.useCallback(()=>{H.current=new qo(re),G(H.current.getNumRows())},[re]),[he,Ie]=f.useState(e.columnOrder);f.useEffect(()=>{Ie(e.columnOrder)},[e.columnOrder.join(",")]);const{columns:me,allColumns:Qe,setColumnConfigMapping:xe}=Db(e,t,n,he);f.useEffect(()=>{if(e.editingMode===ae)return;const be=r.getStringValue({id:e.id,formId:e.formId});be&&(H.current.fromJson(be,me),G(H.current.getNumRows()))},[]);const{getCellContent:At}=Gy(t,me,P,H),{columns:Xe,sortColumn:qe,getOriginalIndex:ne,getCellContent:ge}=$b(re,me,At),Ce=f.useCallback(be=>{const dt={selection:{rows:[],columns:[]}};dt.selection.rows=be.rows.toArray().map(Gt=>ne(Gt)),dt.selection.columns=be.columns.toArray().map(Gt=>to(Xe[Gt]));const bt=JSON.stringify(dt),jt=r.getStringValue({id:e.id,formId:e.formId});(jt===void 0||jt!==bt)&&r.setStringValue({id:e.id,formId:e.formId},bt,{fromUi:!0},o)},[Xe,e.id,e.formId,r,o,ne]),{debouncedCallback:de}=$u(Ce,Ec),{gridSelection:Se,isRowSelectionActivated:Ee,isMultiRowSelectionActivated:De,isColumnSelectionActivated:tt,isMultiColumnSelectionActivated:Oe,isRowSelected:Fe,isColumnSelected:Ve,isCellSelected:Te,clearSelection:Me,processSelectionChange:pt}=Yy(e,te,n,Xe,de);f.useEffect(()=>{Me(!0,!0)},[s]);const ht=f.useCallback(be=>{h.current?.updateCells(be)},[]);f.useEffect(()=>{if(!Ee&&!tt)return;const be=r.getStringValue({id:e.id,formId:e.formId});if(be){const dt=Xe.map(qn=>to(qn)),bt=JSON.parse(be);let jt=ot.empty(),Gt=ot.empty();bt.selection?.rows?.forEach(qn=>{jt=jt.add(qn)}),bt.selection?.columns?.forEach(qn=>{Gt=Gt.add(dt.indexOf(qn))}),(jt.length>0||Gt.length>0)&&pt({rows:jt,columns:Gt,current:void 0})}},[]);const Ye=f.useCallback(()=>{P!==H.current.getNumRows()&&G(H.current.getNumRows())},[P]),xt=f.useCallback(()=>{const be=H.current.toJson(Xe);let dt=r.getStringValue({id:e.id,formId:e.formId});dt===void 0&&(dt=new qo(0).toJson([])),be!==dt&&r.setStringValue({id:e.id,formId:e.formId},be,{fromUi:!0},o)},[Xe,e.id,e.formId,r,o]),{debouncedCallback:Yt}=$u(xt,Ec),{exportToCsv:Ht}=qy(ge,Xe,P,x),{onCellEdited:Mt,onPaste:cn,onRowAppended:Ut,onDelete:tn,validateCell:Tt}=$y(Xe,e.editingMode!==Q,H,ge,ne,ht,Ye,Yt,Me),yt=f.useMemo(()=>te?[0]:se?[P]:[],[te,se,P]),{tooltip:nn,clearTooltip:ut,onItemHovered:rn}=Jy(Xe,ge,yt),{drawCell:Nt,customRenderers:Pe}=Hy(Xe),{provideEditor:Dt}=Bb(),Rt=f.useCallback(be=>({...be,hasMenu:!te}),[te]),Mn=f.useMemo(()=>Xe.map(be=>Rt(Os(be))),[Xe,Rt]),{columns:qt,onColumnResize:dn}=Fb(Mn),Rn=t.dimensions.numHeaderRows>1,{minHeight:Zt,maxHeight:Jt,minWidth:We,maxWidth:Ot,rowHeight:yn,resizableSize:vt,setResizableSize:_t}=jy(e,v,P,Rn,u||0,c,s),Cn=f.useCallback(([be,dt])=>({...ob(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:v.glideTheme.textLight},span:[0,Math.max(Xe.length-1,0)]}),[Xe,v.glideTheme.textLight]),er=f.useCallback(()=>{ce(),Me()},[ce,Me]);pg({element:e,widgetMgr:r,onFormCleared:er});const{pinColumn:zn,unpinColumn:$,freezeColumns:$e}=Pb(Xe,te,u||0,v.minColumnWidth,Me,xe),{changeColumnFormat:Ge}=Ob(xe),{hideColumn:Ct,showColumn:mn}=Nb(Me,xe),{onColumnMoved:sn}=_b(Xe,$e,zn,$,Ie);return f.useEffect(()=>{let be;const dt=requestAnimationFrame(()=>{be=setTimeout(()=>{if(m.current&&h.current){const bt=m.current?.querySelector(".dvn-stack")?.getBoundingClientRect();bt&&(k(bt.height>m.current.clientHeight),D(bt.width>m.current.clientWidth))}},0)});return()=>{cancelAnimationFrame(dt),be&&clearTimeout(be)}},[vt,P,qt]),f.useEffect(()=>{Qe.length==Xe.length&&S(!1)},[Qe.length,Xe.length]),vn(Ic,{className:"stDataFrame","data-testid":"stDataFrame",ref:m,isInHorizontalLayout:d,onPointerDown:be=>{if(m.current){const dt=m.current.getBoundingClientRect(),bt=(p||Math.round(an(vC)))+1;A&&dt.height-bt<be.clientY-dt.top&&be.stopPropagation(),I&&dt.width-bt<be.clientX-dt.left&&be.stopPropagation()}},onBlur:be=>{!O&&!Y&&!be.currentTarget.contains(be.relatedTarget)&&Me(!0,!0)},children:[vn(mg,{isFullScreen:s,disableFullscreenMode:i,locked:Fe&&!Ee||Te||Y&&O||T,onExpand:a,onCollapse:l,target:Ic,children:[(Ee&&Fe||tt&&Ve)&&Ue(si,{label:"Clear selection",icon:xg,onClick:()=>{Me(),ut()}}),se&&Fe&&Ue(si,{label:"Delete row(s)",icon:Eg,onClick:()=>{tn&&(tn(Se),ut())}}),se&&!Fe&&Ue(si,{label:"Add row",icon:Bc,onClick:()=>{Ut&&(R(!0),Ut(),ut(),h.current?.scrollTo(0,P,"vertical"))}}),!te&&Qe.length>Xe.length&&Ue(hC,{columns:Qe,columnOrder:he,setColumnOrder:Ie,hideColumn:Ct,showColumn:mn,isOpen:T,onClose:X,children:Ue(si,{label:"Show/hide columns",icon:Uc,onClick:B})}),!ue&&!te&&Ue(si,{label:"Download as CSV",icon:Tg,onClick:Ht}),!te&&Ue(si,{label:"Search",icon:Wc,onClick:()=>{M?_(!1):(R(!0),_(!0)),ut()}})]}),Ue(fg,{"data-testid":"stDataFrameResizable",ref:g,defaultSize:vt,style:{border:`${v.tableBorderWidth}px solid ${v.glideTheme.borderColor}`,borderRadius:`${v.tableBorderRadius}`},minHeight:Zt,maxHeight:Jt,minWidth:We,maxWidth:d?void 0:Ot,size:vt,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!d,bottomLeft:!1,topLeft:!1},grid:[1,yn],snapGap:yn/3,onResizeStop:(be,dt,bt,jt)=>{if(g.current){const Gt=2*v.tableBorderWidth;_t({width:g.current.size.width,height:Jt-g.current.size.height===Gt?g.current.size.height+Gt:g.current.size.height})}},children:Ue(Zv,{className:"stDataFrameGlideDataEditor","data-testid":"stDataFrameGlideDataEditor",ref:h,columns:qt,rows:te?1:P,minColumnWidth:v.minColumnWidth,maxColumnWidth:v.maxColumnWidth,maxColumnAutoWidth:v.maxColumnAutoWidth,rowHeight:yn,headerHeight:v.defaultHeaderHeight,getCellContent:te?Cn:ge,onColumnResize:Y?void 0:dn,resizeIndicator:"header",freezeColumns:$e,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:!0,getCellsForSelection:!0,rowMarkers:"none",rangeSelect:Y?"cell":"rect",columnSelect:"none",rowSelect:"none",onColumnMoved:tt?void 0:sn,onItemHovered:be=>{b?.(be),rn?.(be)},keybindings:{downFill:!0},onKeyDown:be=>{(be.ctrlKey||be.metaKey)&&be.key==="f"&&(_(dt=>!dt),be.stopPropagation(),be.preventDefault())},showSearch:M,searchResults:M?void 0:[],onSearchClose:()=>{_(!1),ut()},onHeaderClicked:(be,dt)=>{!fe||tt||(M&&_(!1),Ee&&Fe?Me():Me(!0,!0),qe(be,"auto"))},gridSelection:Se,onGridSelectionChange:be=>{(O||Y)&&(pt(be),nn!==void 0&&ut())},theme:v.glideTheme,getRowThemeOverride:w,onMouseMove:be=>{be.kind==="out-of-bounds"&&O?R(!1):be.kind!=="out-of-bounds"&&!O&&R(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:0,paddingBottom:A?-p:void 0,paddingRight:I?-p:void 0},provideEditor:Dt,drawCell:Nt,customRenderers:Pe,imageEditorOverride:pb,headerIcons:v.headerIcons,validateCell:Tt,onHeaderMenuClick:(be,dt)=>{E({columnIdx:be,headerBounds:dt})},onPaste:!1,...Ee&&{rowMarkers:{kind:"checkbox-visible",checkboxStyle:"square",theme:{bgCell:v.glideTheme.bgHeader,bgCellMedium:v.glideTheme.bgHeader,textMedium:v.glideTheme.textLight}},rowSelectionMode:De?"multi":"auto",rowSelect:n?"none":De?"multi":"single",rowSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...tt&&{columnSelect:n?"none":Oe?"multi":"single",columnSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...!te&&e.editingMode!==ae&&!n&&{fillHandle:!Y,onCellEdited:Mt,onPaste:cn,onDelete:tn},...!te&&e.editingMode===Q&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkers:{kind:"checkbox",checkboxStyle:"square",theme:{bgCell:v.glideTheme.bgHeader,bgCellMedium:v.glideTheme.bgHeader}},rowSelectionMode:"multi",rowSelect:n?"none":"multi",onRowAppended:n?void 0:Ut,onHeaderClicked:void 0}})}),nn&&nn.content&&Ue(mC,{top:nn.top,left:nn.left,content:nn.content,clearTooltip:ut}),C&&Hc.createPortal(Ue(uC,{top:C.headerBounds.y+C.headerBounds.height,left:C.headerBounds.x+C.headerBounds.width,column:me[C.columnIdx],onCloseMenu:()=>E(void 0),onSortColumn:fe?be=>{M&&_(!1),Me(!(Ee&&Fe),!0),qe(C.columnIdx,be,!0)}:void 0,isColumnPinned:me[C.columnIdx].isPinned,onUnpinColumn:()=>{$(me[C.columnIdx].id)},onPinColumn:()=>{zn(me[C.columnIdx].id)},onHideColumn:()=>{Ct(me[C.columnIdx].id)},onChangeFormat:be=>{Ge(me[C.columnIdx].id,be),setTimeout(()=>{h.current?.remeasureColumns(ot.fromSingleSelection(C.columnIdx))},100)},onAutosize:()=>{h.current?.remeasureColumns(ot.fromSingleSelection(C.columnIdx))}}),document.querySelector("#portal"))]})}const wC=hg(bC),yC=f.memo(wC),_C=Object.freeze(Object.defineProperty({__proto__:null,default:yC},Symbol.toStringTag,{value:"Module"}));export{O0 as C,Sd as T,hi as a,Lg as b,_C as c,ci as i,Jm as m,un as s};
