import{r as d,E as w,_ as U,s as c,aq as q,aE as p,j as a,bq as m,br as F,b7 as _,b8 as S,bk as K,F as G,bJ as J,aA as Y,bm as Q,bK as z,D as h,b as f,bo as Z,bn as ee,bu as te,bL as ie,bv as le,bc as se,bw as ne}from"./index.DKN5MVff.js";import{F as ae}from"./FormClearHelper.DF4gFAOO.js";import{g as C,F as y,b as oe,D as re,I as de,C as ce,a as pe,s as ge}from"./FileHelper.BrQvUXVD.js";import{S as ue,P as he}from"./ProgressBar.COK9j1l0.js";import{u as me}from"./Hooks.DEoLCfOE.js";import{U as v}from"./UploadFileInfo.C-jY39rj.js";var b=d.forwardRef(function(e,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(w,U({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:l}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12l4.58-4.59z"}))});b.displayName="ChevronLeft";var M=d.forwardRef(function(e,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(w,U({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:l}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6-6-6z"}))});M.displayName="ChevronRight";var V=d.forwardRef(function(e,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(w,U({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:l}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M19.35 10.04A7.49 7.49 0 0012 4C9.11 4 6.6 5.64 5.35 8.04A5.994 5.994 0 000 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95A5.469 5.469 0 0112 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11A2.98 2.98 0 0122 15c0 1.65-1.35 3-3 3zM8 13h2.55v3h2.9v-3H16l-4-4z"}))});V.displayName="CloudUpload";var B=d.forwardRef(function(e,l){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(w,U({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:l}),d.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"}))});B.displayName="Error";const D=c("section",{target:"eamlidg0"})(({isDisabled:e,theme:l})=>({display:"flex",alignItems:"center",padding:l.spacing.lg,backgroundColor:l.colors.secondaryBg,borderRadius:l.radii.default,border:l.colors.widgetBorderColor?`${l.sizes.borderWidth} solid ${l.colors.widgetBorderColor}`:void 0,height:l.sizes.largestElementHeight,":focus":{outline:"none"},":focus-visible":{boxShadow:`0 0 0 1px ${l.colors.primary}`},cursor:e?"not-allowed":"pointer"})),L=c("div",{target:"eamlidg1"})({marginRight:"auto",alignItems:"center",display:"flex"}),E=c("span",{target:"eamlidg2"})(({theme:e})=>({color:e.colors.darkenedBgMix100,marginRight:e.spacing.lg})),P=c("span",{target:"eamlidg3"})(({theme:e,disabled:l})=>({color:l?e.colors.fadedText40:e.colors.bodyText})),fe=c("span",{target:"eamlidg4"})(({theme:e,disabled:l})=>({fontSize:e.fontSizes.sm,color:l?e.colors.fadedText40:e.colors.fadedText60})),Fe=c("div",{target:"eamlidg5"})({display:"flex",flexDirection:"column"}),A=c("div",{target:"eamlidg6"})(({theme:e})=>({left:0,right:0,lineHeight:e.lineHeights.tight,paddingTop:e.spacing.md,paddingLeft:e.spacing.lg,paddingRight:e.spacing.lg})),Se=c("ul",{target:"eamlidg7"})(({theme:e})=>({listStyleType:"none",margin:e.spacing.none,padding:e.spacing.none})),T=c("li",{target:"eamlidg8"})(({theme:e})=>({margin:e.spacing.none,padding:e.spacing.none})),H=c("div",{target:"eamlidg9"})(({theme:e})=>({display:"flex",alignItems:"baseline",flex:1,paddingLeft:e.spacing.lg,overflow:"hidden"})),N=c("div",{target:"eamlidg10"})(({theme:e})=>({marginRight:e.spacing.sm,marginBottom:e.spacing.twoXS,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"})),W=c("div",{target:"eamlidg11"})(({theme:e})=>({display:"flex",alignItems:"center",marginBottom:e.spacing.twoXS})),ye=c("span",{target:"eamlidg12"})(({theme:e})=>({marginRight:e.spacing.twoXS})),we=c("div",{target:"eamlidg13"})(({theme:e})=>({display:"flex",padding:e.spacing.twoXS,color:e.colors.darkenedBgMix100})),k=c("small",{target:"eamlidg14"})(({theme:e})=>({color:e.colors.red,fontSize:e.fontSizes.sm,height:e.fontSizes.sm,lineHeight:e.fontSizes.sm,display:"flex",alignItems:"center",whiteSpace:"nowrap"})),R=c("span",{target:"eamlidg15"})({}),Ue=e=>({[D]:{display:"flex",flexDirection:"column",alignItems:"flex-start",height:"auto"},[L]:{marginBottom:e.spacing.sm},[E]:{display:"none"},[P]:{marginBottom:e.spacing.twoXS},[A]:{paddingRight:e.spacing.lg},[W]:{maxWidth:"inherit",flex:1,alignItems:"flex-start",marginBottom:e.spacing.sm},[N]:{width:e.sizes.full},[H]:{flexDirection:"column"},[k]:{height:"auto",whiteSpace:"initial"},[R]:{display:"none"},[T]:{margin:e.spacing.none,padding:e.spacing.none}}),xe=c("div",{target:"eamlidg16"})(({theme:e,width:l})=>{if(l<q("23rem"))return Ue(e)}),ve=({multiple:e,acceptedExtensions:l,maxSizeBytes:t,disabled:i})=>p(L,{"data-testid":"stFileUploaderDropzoneInstructions",children:[a(E,{children:a(m,{content:V,size:"threeXL"})}),p(Fe,{children:[p(P,{disabled:i,children:["Drag and drop file",e?"s":""," here"]}),p(fe,{disabled:i,children:[`Limit ${C(t,y.Byte,0)} per file`,l.length?` • ${l.map(s=>s.replace(/^\./,"").toUpperCase()).join(", ")}`:null]})]})]}),ze=d.memo(ve),Ie=({onDrop:e,multiple:l,acceptedExtensions:t,maxSizeBytes:i,disabled:s,label:o})=>a(re,{onDrop:e,multiple:l,accept:oe(t),maxSize:i,disabled:s,useFsAccessApi:!1,children:({getRootProps:n,getInputProps:r})=>p(D,{...n(),"data-testid":"stFileUploaderDropzone",isDisabled:s,"aria-label":o,children:[a("input",{"data-testid":"stFileUploaderDropzoneInput",...r()}),a(ze,{multiple:l,acceptedExtensions:t,maxSizeBytes:i,disabled:s}),a(F,{kind:S.SECONDARY,disabled:s,size:_.SMALL,children:"Browse files"})]})}),Ce=d.memo(Ie),$=c("small",{target:"ejh2rmr0"})(({kind:e,theme:l})=>{const{danger:t,fadedText60:i}=l.colors;return{color:e==="danger"?t:i,fontSize:l.fontSizes.sm,lineHeight:l.lineHeights.tight}}),be=({fileInfo:e})=>e.status.type==="uploading"?a(he,{value:e.status.progress,size:ue.SMALL}):e.status.type==="error"?p(k,{children:[a(ye,{"data-testid":"stFileUploaderFileErrorMessage",children:e.status.errorMessage}),a(R,{children:a(m,{content:B,size:"lg"})})]}):e.status.type==="uploaded"?a($,{children:C(e.size,y.Byte)}):null,Me=({fileInfo:e,onDelete:l})=>p(W,{className:"stFileUploaderFile","data-testid":"stFileUploaderFile",children:[a(we,{children:a(m,{content:de,size:"twoXL"})}),p(H,{className:"stFileUploaderFileData",children:[a(N,{className:"stFileUploaderFileName","data-testid":"stFileUploaderFileName",title:e.name,children:e.name}),a(be,{fileInfo:e})]}),a("div",{"data-testid":"stFileUploaderDeleteBtn",children:a(F,{onClick:()=>l(e.id),kind:S.MINIMAL,children:a(m,{content:ce,size:"lg"})})})]}),Ve=d.memo(Me),Be=c("div",{target:"egc9vxm0"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"space-between",paddingBottom:e.spacing.twoXS,marginBottom:e.spacing.twoXS})),De=c("div",{target:"egc9vxm1"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",color:e.colors.fadedText40})),Le=({currentPage:e,totalPages:l,onNext:t,onPrevious:i})=>p(Be,{"data-testid":"stFileUploaderPagination",children:[a($,{children:`Showing page ${e} of ${l}`}),p(De,{children:[a(F,{onClick:i,kind:S.MINIMAL,children:a(m,{content:b,size:"xl"})}),a(F,{onClick:t,kind:S.MINIMAL,children:a(m,{content:M,size:"xl"})})]})]}),Ee=d.memo(Le),I=(e,l)=>Math.ceil(e.length/l),Pe=e=>K(({pageSize:t,items:i,resetOnAdd:s,...o})=>{const[n,r]=d.useState(0),[g,x]=d.useState(()=>I(i,t)),u=me(i);d.useEffect(()=>{u&&u.length!==i.length&&x(I(i,t)),u&&u.length<i.length?s&&r(0):n+1>=g&&r(g-1)},[i,n,t,u,s,g]);const X=()=>{r(Math.min(n+1,g-1))},O=()=>{r(Math.max(0,n-1))},j=i.slice(n*t,n*t+t);return p(G,{children:[a(e,{items:j,...o}),i.length>t?a(Ee,{pageSize:t,totalPages:g,currentPage:n+1,onNext:X,onPrevious:O}):null]})},e),Ae=({items:e,onDelete:l})=>a(Se,{children:e.map(t=>a(T,{children:a(Ve,{fileInfo:t,onDelete:l})},t.id))}),Te=Pe(Ae),He=e=>a(A,{children:a(Te,{...e})}),Ne=d.memo(He);class We extends d.PureComponent{constructor(l){super(l),this.formClearHelper=new ae,this.localFileIdCounter=1,this.forceUpdatingStatus=!1,this.componentDidUpdate=()=>{if(this.status!=="ready")return;const t=this.createWidgetValue(),{element:i,widgetMgr:s,fragmentId:o}=this.props,n=s.getFileUploaderStateValue(i);Y(t,n)||s.setFileUploaderStateValue(i,t,{fromUi:!0},o)},this.dropHandler=(t,i)=>{const{element:s}=this.props,{multipleFiles:o}=s;if(!o&&t.length===0&&i.length>1){const n=i.findIndex(r=>r.errors.length===1&&r.errors[0].code==="too-many-files");n>=0&&(t.push(i[n].file),i.splice(n,1))}if(this.props.uploadClient.fetchFileURLs(t).then(n=>{if(!o&&t.length>0){const r=this.state.files.find(g=>g.status.type!=="error");r&&(this.forceUpdatingStatus=!0,this.deleteFile(r.id),this.forceUpdatingStatus=!1)}Q(n,t).forEach(([r,g])=>{this.uploadFile(r,g)})}).catch(n=>{this.addFiles(t.map(r=>new v(r.name,r.size,this.nextLocalFileId(),{type:"error",errorMessage:n})))}),i.length>0){const n=i.map(r=>pe(r,this.nextLocalFileId(),this.maxUploadSizeInBytes));this.addFiles(n)}},this.uploadFile=(t,i)=>{const s=z.CancelToken.source(),o=new v(i.name,i.size,this.nextLocalFileId(),{type:"uploading",cancelToken:s,progress:1});this.addFile(o),this.props.uploadClient.uploadFile(this.props.element,t.uploadUrl,i,n=>this.onUploadProgress(n,o.id),s.token).then(()=>this.onUploadComplete(o.id,t)).catch(n=>{z.isCancel(n)||this.updateFile(o.id,o.setStatus({type:"error",errorMessage:n?n.toString():"Unknown error"}))})},this.onUploadComplete=(t,i)=>{const s=this.getFile(t);h(s)||s.status.type!=="uploading"||this.updateFile(s.id,s.setStatus({type:"uploaded",fileId:i.fileId,fileUrls:i}))},this.deleteFile=t=>{const i=this.getFile(t);h(i)||(i.status.type==="uploading"&&i.status.cancelToken.cancel(),i.status.type==="uploaded"&&i.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(i.status.fileUrls.deleteUrl),this.removeFile(t))},this.addFile=t=>{f.flushSync(()=>{this.setState(i=>({files:[...i.files,t]}))})},this.addFiles=t=>{f.flushSync(()=>{this.setState(i=>({files:[...i.files,...t]}))})},this.removeFile=t=>{f.flushSync(()=>{this.setState(i=>({files:i.files.filter(s=>s.id!==t)}))})},this.getFile=t=>this.state.files.find(i=>i.id===t),this.updateFile=(t,i)=>{f.flushSync(()=>{this.setState(s=>({files:s.files.map(o=>o.id===t?i:o)}))})},this.onUploadProgress=(t,i)=>{const s=this.getFile(i);if(h(s)||s.status.type!=="uploading")return;const o=Math.round(t.loaded*100/t.total);s.status.progress!==o&&this.updateFile(i,s.setStatus({type:"uploading",cancelToken:s.status.cancelToken,progress:o}))},this.onFormCleared=()=>{f.flushSync(()=>{this.setState({files:[]},()=>{const t=this.createWidgetValue();if(h(t))return;const{widgetMgr:i,element:s,fragmentId:o}=this.props;i.setFileUploaderStateValue(s,t,{fromUi:!0},o)})})},this.state=this.initialValue}get initialValue(){const l={files:[]},{widgetMgr:t,element:i}=this.props,s=t.getFileUploaderStateValue(i);if(h(s))return l;const{uploadedFileInfo:o}=s;return h(o)||o.length===0?l:{files:o.map(n=>{const r=n.name,g=n.size,x=n.fileId,u=n.fileUrls;return new v(r,g,this.nextLocalFileId(),{type:"uploaded",fileId:x,fileUrls:u})})}}componentWillUnmount(){this.formClearHelper.disconnect()}get maxUploadSizeInBytes(){const l=this.props.element.maxUploadSizeMb;return ge(l,y.Megabyte,y.Byte)}get status(){const l=t=>t.status.type==="uploading";return this.state.files.some(l)||this.forceUpdatingStatus?"updating":"ready"}componentDidMount(){const l=this.createWidgetValue(),{element:t,widgetMgr:i,fragmentId:s}=this.props;i.getFileUploaderStateValue(t)===void 0&&i.setFileUploaderStateValue(t,l,{fromUi:!1},s)}createWidgetValue(){const l=this.state.files.filter(t=>t.status.type==="uploaded").map(t=>{const{name:i,size:s,status:o}=t,{fileId:n,fileUrls:r}=o;return new Z({fileId:n,fileUrls:r,name:i,size:s})});return new ee({uploadedFileInfo:l})}render(){const{files:l}=this.state,{element:t,disabled:i,widgetMgr:s,width:o}=this.props,n=t.type;this.formClearHelper.manageFormClearListener(s,t.formId,this.onFormCleared);const r=l.slice().reverse();return p(xe,{className:"stFileUploader","data-testid":"stFileUploader",width:o,children:[a(ne,{label:t.label,disabled:i,labelVisibility:te(t.labelVisibility?.value),children:t.help&&a(ie,{children:a(le,{content:t.help,placement:se.TOP_RIGHT})})}),a(Ce,{onDrop:this.dropHandler,multiple:t.multipleFiles,acceptedExtensions:n,maxSizeBytes:this.maxUploadSizeInBytes,label:t.label,disabled:i}),r.length>0&&a(Ne,{items:r,pageSize:3,onDelete:this.deleteFile,resetOnAdd:!0})]})}nextLocalFileId(){return this.localFileIdCounter++}}const qe=J(d.memo(We));export{qe as default};
