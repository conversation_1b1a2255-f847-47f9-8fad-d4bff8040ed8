# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/SessionStatus.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#streamlit/proto/SessionStatus.proto\"?\n\rSessionStatus\x12\x13\n\x0brun_on_save\x18\x01 \x01(\x08\x12\x19\n\x11script_is_running\x18\x02 \x01(\x08\x42\x32\n\x1c\x63om.snowflake.apps.streamlitB\x12SessionStatusProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.SessionStatus_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\022SessionStatusProto'
  _globals['_SESSIONSTATUS']._serialized_start=39
  _globals['_SESSIONSTATUS']._serialized_end=102
# @@protoc_insertion_point(module_scope)
