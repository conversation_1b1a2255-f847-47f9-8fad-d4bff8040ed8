import{s as l,r as i,u as v,j as f,l as g}from"./index.DKN5MVff.js";const T=l("div",{target:"ea9qfvi0"})(()=>({lineHeight:0})),y=l("audio",{target:"ea9qfvi1"})(({theme:t})=>({width:"100%",height:t.sizes.minElementHeight,margin:0,padding:0})),h=g.get<PERSON>ogger("Audio");function L({element:t,endpoints:a,elementMgr:s}){const o=i.useRef(null),{startTime:n,endTime:d,loop:u,autoplay:p}=t,E=i.useMemo(()=>{if(!t.id)return!0;const e=s.getElementState(t.id,"preventAutoplay");return e||s.setElementState(t.id,"preventAutoplay",!0),e??!1},[t.id,s]);i.useEffect(()=>{o.current&&(o.current.currentTime=n)},[n]),i.useEffect(()=>{const e=o.current,r=()=>{e&&(e.currentTime=t.startTime)};return e&&e.addEventListener("loadedmetadata",r),()=>{e&&e.removeEventListener("loadedmetadata",r)}},[t]),i.useEffect(()=>{const e=o.current;if(!e)return;let r=!1;const c=()=>{d>0&&e.currentTime>=d&&(u?(e.currentTime=n||0,e.play()):r||(r=!0,e.pause()))};return d>0&&e.addEventListener("timeupdate",c),()=>{e&&d>0&&e.removeEventListener("timeupdate",c)}},[d,u,n]),i.useEffect(()=>{const e=o.current;if(!e)return;const r=()=>{u&&(e.currentTime=n||0,e.play())};return e.addEventListener("ended",r),()=>{e&&e.removeEventListener("ended",r)}},[u,n]);const m=v(t.url),A=a.buildMediaURL(t.url);return f(T,{children:f(y,{className:"stAudio","data-testid":"stAudio",ref:o,controls:!0,autoPlay:p&&!E,src:A,onError:e=>{const r=e.currentTarget.src;h.error(`Client Error: Audio source error - ${r}`),a.sendClientErrorToHost("Audio","Audio source failed to load","onerror triggered",r)},crossOrigin:m})})}const C=i.memo(L);export{C as default};
