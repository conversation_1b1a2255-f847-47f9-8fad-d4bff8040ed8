{"python.defaultInterpreterPath": "./.venv/Scripts/python.exe", "python.terminal.activateEnvironment": false, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests"], "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, ".venv": false}, "python.analysis.extraPaths": ["./src", "./app_pages"], "streamlit.runOnSave": false, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}/src:${workspaceFolder}"}}