import{r as o,L as h,b8 as i,j as e,b9 as k,br as g,b7 as R,ba as C}from"./index.DKN5MVff.js";import{c as L}from"./createDownloadLinkElement.ZaXNnPK4.js";function x(d){const{disabled:c,element:l,widgetMgr:u,endpoints:t,fragmentId:f}=d,{help:m,label:B,icon:b,ignoreRerun:w,type:s,url:r}=l,{libConfig:{enforceDownloadInNewTab:D=!1}}=o.useContext(h);let n=i.SECONDARY;s==="primary"?n=i.PRIMARY:s==="tertiary"&&(n=i.TERTIARY);const a=o.useMemo(()=>t.buildDownloadUrl(r),[t,r]);o.useEffect(()=>{t.checkSourceUrlResponse(a,"Download Button")},[a,t]);const p=()=>{w||u.setTriggerValue(l,{fromUi:!0},f),L({filename:"",url:a,enforceDownloadInNewTab:D}).click()};return e("div",{className:"stDownloadButton","data-testid":"stDownloadButton",children:e(k,{help:m,containerWidth:!0,children:e(g,{kind:n,size:R.SMALL,disabled:c,onClick:p,containerWidth:!0,children:e(C,{icon:b,label:B})})})})}const A=o.memo(x);export{A as default};
