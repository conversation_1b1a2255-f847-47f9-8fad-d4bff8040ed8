import{s as i,k as m,r as c,j as n,a as p}from"./index.DKN5MVff.js";import{P as f,R as d}from"./Particles.CCFySwdL.js";const g=""+new URL("../media/balloon-0.Czj7AKwE.png",import.meta.url).href,B=""+new URL("../media/balloon-1.CNvFFrND.png",import.meta.url).href,L=""+new URL("../media/balloon-2.DTvC6B1t.png",import.meta.url).href,h=""+new URL("../media/balloon-3.CgSk4tbL.png",import.meta.url).href,u=""+new URL("../media/balloon-4.mbtFrzxf.png",import.meta.url).href,x=""+new URL("../media/balloon-5.CSwkUfRA.png",import.meta.url).href,t=300,e=121,s=20,A=80,_=1e3,I=m("from{transform:translateY(calc(100vh + ",t,"px));}to{transform:translateY(0);}"),M=i("img",{target:"eosrfo90"})(({theme:o})=>({position:"fixed",top:`${-t}px`,marginLeft:`${-e/2}px`,zIndex:o.zIndices.balloons,left:`${Math.random()*(A-s)+s}vw`,animationDelay:`${Math.random()*_}ms`,height:`${t}px`,width:`${e}px`,pointerEvents:"none",animationDuration:"750ms",animationName:I,animationTimingFunction:"ease-in",animationDirection:"normal",animationIterationCount:1,opacity:1})),b=30,l=[g,B,L,h,u,x],w=l.length,E=({particleType:o,resourceCrossOriginMode:r})=>{const a=l[o];return n(M,{src:a,crossOrigin:p(r,a)})},N=({scriptRunId:o})=>n(d,{children:n(f,{className:"stBalloons","data-testid":"stBalloons",scriptRunId:o,numParticleTypes:w,numParticles:b,ParticleComponent:E})}),P=c.memo(N);export{b as NUM_BALLOONS,P as default};
