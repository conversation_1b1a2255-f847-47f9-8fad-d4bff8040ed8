import{r as h,E as z,_ as H,L as _t,D as q,H as X,bd as ut,bm as $t,bn as zt,bo as Ht,s as D,j as g,bp as Vt,aE as N,b8 as jt,bq as Gt,br as qt,l as Xt,bs as Yt,ap as Kt,bt as tt,aq as Jt,aA as Qt,bu as Zt,bv as te,bc as ee,bw as ie}from"./index.DKN5MVff.js";import{T as se,a as pt}from"./Toolbar.Dt4jIKlY.js";import{u as re,F as ne}from"./FormClearHelper.DF4gFAOO.js";import{c as oe}from"./createDownloadLinkElement.ZaXNnPK4.js";import{u as ae}from"./Hooks.DEoLCfOE.js";import{F as le,D as ce}from"./FileDownload.esm.Bz9nxNC5.js";var gt=h.forwardRef(function(r,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement(z,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},r,{ref:t}),h.createElement("g",{fill:"none"},h.createElement("rect",{width:24,height:24}),h.createElement("rect",{width:24,height:24}),h.createElement("rect",{width:24,height:24})),h.createElement("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),h.createElement("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}))});gt.displayName="Mic";var bt=h.forwardRef(function(r,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement(z,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},r,{ref:t}),h.createElement("rect",{width:24,height:24,fill:"none"}),h.createElement("path",{d:"M8 19c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2s-2 .9-2 2v10c0 1.1.9 2 2 2zm6-12v10c0 1.1.9 2 2 2s2-.9 2-2V7c0-1.1-.9-2-2-2s-2 .9-2 2z"}))});bt.displayName="Pause";var yt=h.forwardRef(function(r,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement(z,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},r,{ref:t}),h.createElement("rect",{width:24,height:24,fill:"none"}),h.createElement("path",{d:"M8 6.82v10.36c0 .79.87 1.27 1.54.84l8.14-5.18a1 1 0 000-1.69L9.54 5.98A.998.998 0 008 6.82z"}))});yt.displayName="PlayArrow";var wt=h.forwardRef(function(r,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement(z,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},r,{ref:t}),h.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),h.createElement("path",{d:"M17.65 6.35a7.95 7.95 0 00-6.48-2.31c-3.67.37-6.69 3.35-7.1 7.02C3.52 15.91 7.27 20 12 20a7.98 7.98 0 007.21-4.56c.32-.67-.16-1.44-.9-1.44-.37 0-.72.2-.88.53a5.994 5.994 0 01-6.8 3.31c-2.22-.49-4.01-2.3-4.48-4.52A6.002 6.002 0 0112 6c1.66 0 3.14.69 4.22 1.78l-1.51 1.51c-.63.63-.19 1.71.7 1.71H19c.55 0 1-.45 1-1V6.41c0-.89-1.08-1.34-1.71-.71l-.64.65z"}))});wt.displayName="Refresh";var St=h.forwardRef(function(r,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement(z,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},r,{ref:t}),h.createElement("g",{fill:"none"},h.createElement("rect",{width:24,height:24}),h.createElement("rect",{width:24,height:24})),h.createElement("path",{fillRule:"evenodd",d:"M9 16h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1zm3-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"}))});St.displayName="StopCircle";function P(r,t,e,i){return new(e||(e=Promise))(function(s,n){function o(c){try{d(i.next(c))}catch(l){n(l)}}function a(c){try{d(i.throw(c))}catch(l){n(l)}}function d(c){var l;c.done?s(c.value):(l=c.value,l instanceof e?l:new e(function(p){p(l)})).then(o,a)}d((i=i.apply(r,t||[])).next())})}let V=class{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),i?.once){const s=()=>{this.un(t,s),this.un(t,e)};return this.on(t,s),s}return()=>this.un(t,e)}un(t,e){var i;(i=this.listeners[t])===null||i===void 0||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(i=>i(...e))}};const j={decode:function(r,t){return P(this,void 0,void 0,function*(){const e=new AudioContext({sampleRate:t});return e.decodeAudioData(r).finally(()=>e.close())})},createBuffer:function(r,t){return typeof r[0]=="number"&&(r=[r]),function(e){const i=e[0];if(i.some(s=>s>1||s<-1)){const s=i.length;let n=0;for(let o=0;o<s;o++){const a=Math.abs(i[o]);a>n&&(n=a)}for(const o of e)for(let a=0;a<s;a++)o[a]/=n}}(r),{duration:t,length:r[0].length,sampleRate:r[0].length/t,numberOfChannels:r.length,getChannelData:e=>r?.[e],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}};function Ct(r,t){const e=t.xmlns?document.createElementNS(t.xmlns,r):document.createElement(r);for(const[i,s]of Object.entries(t))if(i==="children"&&s)for(const[n,o]of Object.entries(s))o instanceof Node?e.appendChild(o):typeof o=="string"?e.appendChild(document.createTextNode(o)):e.appendChild(Ct(n,o));else i==="style"?Object.assign(e.style,s):i==="textContent"?e.textContent=s:e.setAttribute(i,s.toString());return e}function mt(r,t,e){const i=Ct(r,t||{});return e?.appendChild(i),i}var de=Object.freeze({__proto__:null,createElement:mt,default:mt});const he={fetchBlob:function(r,t,e){return P(this,void 0,void 0,function*(){const i=yield fetch(r,e);if(i.status>=400)throw new Error(`Failed to fetch ${r}: ${i.status} (${i.statusText})`);return function(s,n){P(this,void 0,void 0,function*(){if(!s.body||!s.headers)return;const o=s.body.getReader(),a=Number(s.headers.get("Content-Length"))||0;let d=0;const c=p=>P(this,void 0,void 0,function*(){d+=p?.length||0;const u=Math.round(d/a*100);n(u)}),l=()=>P(this,void 0,void 0,function*(){let p;try{p=yield o.read()}catch{return}p.done||(c(p.value),yield l())});l()})}(i.clone(),t),i.blob()})}};class ue extends V{constructor(t){super(),this.isExternalMedia=!1,t.media?(this.media=t.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),t.mediaControls&&(this.media.controls=!0),t.autoplay&&(this.media.autoplay=!0),t.playbackRate!=null&&this.onMediaEvent("canplay",()=>{t.playbackRate!=null&&(this.media.playbackRate=t.playbackRate)},{once:!0})}onMediaEvent(t,e,i){return this.media.addEventListener(t,e,i),()=>this.media.removeEventListener(t,e,i)}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const t=this.getSrc();t.startsWith("blob:")&&URL.revokeObjectURL(t)}canPlayType(t){return this.media.canPlayType(t)!==""}setSrc(t,e){const i=this.getSrc();if(t&&i===t)return;this.revokeSrc();const s=e instanceof Blob&&(this.canPlayType(e.type)||!t)?URL.createObjectURL(e):t;if(i&&this.media.removeAttribute("src"),s||t)try{this.media.src=s}catch{this.media.src=t}}destroy(){this.isExternalMedia||(this.media.pause(),this.media.remove(),this.revokeSrc(),this.media.removeAttribute("src"),this.media.load())}setMediaElement(t){this.media=t}play(){return P(this,void 0,void 0,function*(){try{return yield this.media.play()}catch(t){if(t instanceof DOMException&&t.name==="AbortError")return;throw t}})}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(t){this.media.currentTime=Math.max(0,Math.min(t,this.getDuration()))}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(t){this.media.volume=t}getMuted(){return this.media.muted}setMuted(t){this.media.muted=t}getPlaybackRate(){return this.media.playbackRate}isSeeking(){return this.media.seeking}setPlaybackRate(t,e){e!=null&&(this.media.preservesPitch=e),this.media.playbackRate=t}getMediaElement(){return this.media}setSinkId(t){return this.media.setSinkId(t)}}class U extends V{constructor(t,e){super(),this.timeouts=[],this.isScrollable=!1,this.audioData=null,this.resizeObserver=null,this.lastContainerWidth=0,this.isDragging=!1,this.subscriptions=[],this.unsubscribeOnScroll=[],this.subscriptions=[],this.options=t;const i=this.parentFromOptionsContainer(t.container);this.parent=i;const[s,n]=this.initHtml();i.appendChild(s),this.container=s,this.scrollContainer=n.querySelector(".scroll"),this.wrapper=n.querySelector(".wrapper"),this.canvasWrapper=n.querySelector(".canvases"),this.progressWrapper=n.querySelector(".progress"),this.cursor=n.querySelector(".cursor"),e&&n.appendChild(e),this.initEvents()}parentFromOptionsContainer(t){let e;if(typeof t=="string"?e=document.querySelector(t):t instanceof HTMLElement&&(e=t),!e)throw new Error("Container not found");return e}initEvents(){const t=e=>{const i=this.wrapper.getBoundingClientRect(),s=e.clientX-i.left,n=e.clientY-i.top;return[s/i.width,n/i.height]};if(this.wrapper.addEventListener("click",e=>{const[i,s]=t(e);this.emit("click",i,s)}),this.wrapper.addEventListener("dblclick",e=>{const[i,s]=t(e);this.emit("dblclick",i,s)}),this.options.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:e,scrollWidth:i,clientWidth:s}=this.scrollContainer,n=e/i,o=(e+s)/i;this.emit("scroll",n,o,e,e+s)}),typeof ResizeObserver=="function"){const e=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{e().then(()=>this.onContainerResize()).catch(()=>{})}),this.resizeObserver.observe(this.scrollContainer)}}onContainerResize(){const t=this.parent.clientWidth;t===this.lastContainerWidth&&this.options.height!=="auto"||(this.lastContainerWidth=t,this.reRender())}initDrag(){this.subscriptions.push(function(t,e,i,s,n=3,o=0,a=100){if(!t)return()=>{};const d=matchMedia("(pointer: coarse)").matches;let c=()=>{};const l=p=>{if(p.button!==o)return;p.preventDefault(),p.stopPropagation();let u=p.clientX,m=p.clientY,f=!1;const y=Date.now(),v=w=>{if(w.preventDefault(),w.stopPropagation(),d&&Date.now()-y<a)return;const k=w.clientX,A=w.clientY,M=k-u,T=A-m;if(f||Math.abs(M)>n||Math.abs(T)>n){const O=t.getBoundingClientRect(),{left:F,top:L}=O;f||(i?.(u-F,m-L),f=!0),e(M,T,k-F,A-L),u=k,m=A}},S=w=>{if(f){const k=w.clientX,A=w.clientY,M=t.getBoundingClientRect(),{left:T,top:O}=M;s?.(k-T,A-O)}c()},b=w=>{w.relatedTarget&&w.relatedTarget!==document.documentElement||S(w)},x=w=>{f&&(w.stopPropagation(),w.preventDefault())},R=w=>{f&&w.preventDefault()};document.addEventListener("pointermove",v),document.addEventListener("pointerup",S),document.addEventListener("pointerout",b),document.addEventListener("pointercancel",b),document.addEventListener("touchmove",R,{passive:!1}),document.addEventListener("click",x,{capture:!0}),c=()=>{document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",S),document.removeEventListener("pointerout",b),document.removeEventListener("pointercancel",b),document.removeEventListener("touchmove",R),setTimeout(()=>{document.removeEventListener("click",x,{capture:!0})},10)}};return t.addEventListener("pointerdown",l),()=>{c(),t.removeEventListener("pointerdown",l)}}(this.wrapper,(t,e,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},t=>{this.isDragging=!0,this.emit("dragstart",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))},t=>{this.isDragging=!1,this.emit("dragend",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))}))}getHeight(t,e){var i;const s=((i=this.audioData)===null||i===void 0?void 0:i.numberOfChannels)||1;if(t==null)return 128;if(!isNaN(Number(t)))return Number(t);if(t==="auto"){const n=this.parent.clientHeight||128;return e?.every(o=>!o.overlay)?n/s:n}return 128}initHtml(){const t=document.createElement("div"),e=t.attachShadow({mode:"open"}),i=this.options.cspNonce&&typeof this.options.cspNonce=="string"?this.options.cspNonce.replace(/"/g,""):"";return e.innerHTML=`
      <style${i?` nonce="${i}"`:""}>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight(this.options.height,this.options.splitChannels)}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases" part="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[t,e]}setOptions(t){if(this.options.container!==t.container){const e=this.parentFromOptionsContainer(t.container);e.appendChild(this.container),this.parent=e}t.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.options=t,this.reRender()}getWrapper(){return this.wrapper}getWidth(){return this.scrollContainer.clientWidth}getScroll(){return this.scrollContainer.scrollLeft}setScroll(t){this.scrollContainer.scrollLeft=t}setScrollPercentage(t){const{scrollWidth:e}=this.scrollContainer,i=e*t;this.setScroll(i)}destroy(){var t,e;this.subscriptions.forEach(i=>i()),this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(e=this.unsubscribeOnScroll)===null||e===void 0||e.forEach(i=>i()),this.unsubscribeOnScroll=[]}createDelay(t=10){let e,i;const s=()=>{e&&clearTimeout(e),i&&i()};return this.timeouts.push(s),()=>new Promise((n,o)=>{s(),i=o,e=setTimeout(()=>{e=void 0,i=void 0,n()},t)})}convertColorValues(t){if(!Array.isArray(t))return t||"";if(t.length<2)return t[0]||"";const e=document.createElement("canvas"),i=e.getContext("2d"),s=e.height*(window.devicePixelRatio||1),n=i.createLinearGradient(0,0,0,s),o=1/(t.length-1);return t.forEach((a,d)=>{const c=d*o;n.addColorStop(c,a)}),n}getPixelRatio(){return Math.max(1,window.devicePixelRatio||1)}renderBarWaveform(t,e,i,s){const n=t[0],o=t[1]||t[0],a=n.length,{width:d,height:c}=i.canvas,l=c/2,p=this.getPixelRatio(),u=e.barWidth?e.barWidth*p:1,m=e.barGap?e.barGap*p:e.barWidth?u/2:0,f=e.barRadius||0,y=d/(u+m)/a,v=f&&"roundRect"in i?"roundRect":"rect";i.beginPath();let S=0,b=0,x=0;for(let R=0;R<=a;R++){const w=Math.round(R*y);if(w>S){const M=Math.round(b*l*s),T=M+Math.round(x*l*s)||1;let O=l-M;e.barAlign==="top"?O=0:e.barAlign==="bottom"&&(O=c-T),i[v](S*(u+m),O,u,T,f),S=w,b=0,x=0}const k=Math.abs(n[R]||0),A=Math.abs(o[R]||0);k>b&&(b=k),A>x&&(x=A)}i.fill(),i.closePath()}renderLineWaveform(t,e,i,s){const n=o=>{const a=t[o]||t[0],d=a.length,{height:c}=i.canvas,l=c/2,p=i.canvas.width/d;i.moveTo(0,l);let u=0,m=0;for(let f=0;f<=d;f++){const y=Math.round(f*p);if(y>u){const S=l+(Math.round(m*l*s)||1)*(o===0?-1:1);i.lineTo(u,S),u=y,m=0}const v=Math.abs(a[f]||0);v>m&&(m=v)}i.lineTo(u,l)};i.beginPath(),n(0),n(1),i.fill(),i.closePath()}renderWaveform(t,e,i){if(i.fillStyle=this.convertColorValues(e.waveColor),e.renderFunction)return void e.renderFunction(t,i);let s=e.barHeight||1;if(e.normalize){const n=Array.from(t[0]).reduce((o,a)=>Math.max(o,Math.abs(a)),0);s=n?1/n:1}e.barWidth||e.barGap||e.barAlign?this.renderBarWaveform(t,e,i,s):this.renderLineWaveform(t,e,i,s)}renderSingleCanvas(t,e,i,s,n,o,a){const d=this.getPixelRatio(),c=document.createElement("canvas");c.width=Math.round(i*d),c.height=Math.round(s*d),c.style.width=`${i}px`,c.style.height=`${s}px`,c.style.left=`${Math.round(n)}px`,o.appendChild(c);const l=c.getContext("2d");if(this.renderWaveform(t,e,l),c.width>0&&c.height>0){const p=c.cloneNode(),u=p.getContext("2d");u.drawImage(c,0,0),u.globalCompositeOperation="source-in",u.fillStyle=this.convertColorValues(e.progressColor),u.fillRect(0,0,c.width,c.height),a.appendChild(p)}}renderMultiCanvas(t,e,i,s,n,o){const a=this.getPixelRatio(),{clientWidth:d}=this.scrollContainer,c=i/a;let l=Math.min(U.MAX_CANVAS_WIDTH,d,c),p={};if(e.barWidth||e.barGap){const v=e.barWidth||.5,S=v+(e.barGap||v/2);l%S!=0&&(l=Math.floor(l/S)*S)}if(l===0)return;const u=v=>{if(v<0||v>=m||p[v])return;p[v]=!0;const S=v*l;let b=Math.min(c-S,l);if(e.barWidth||e.barGap){const R=e.barWidth||.5,w=R+(e.barGap||R/2);b=Math.floor(b/w)*w}if(b<=0)return;const x=t.map(R=>{const w=Math.floor(S/c*R.length),k=Math.floor((S+b)/c*R.length);return R.slice(w,k)});this.renderSingleCanvas(x,e,b,s,S,n,o)},m=Math.ceil(c/l);if(!this.isScrollable){for(let v=0;v<m;v++)u(v);return}const f=this.scrollContainer.scrollLeft/c,y=Math.floor(f*m);if(u(y-1),u(y),u(y+1),m>1){const v=this.on("scroll",()=>{const{scrollLeft:S}=this.scrollContainer,b=Math.floor(S/c*m);Object.keys(p).length>U.MAX_NODES&&(n.innerHTML="",o.innerHTML="",p={}),u(b-1),u(b),u(b+1)});this.unsubscribeOnScroll.push(v)}}renderChannel(t,e,i,s){var{overlay:n}=e,o=function(l,p){var u={};for(var m in l)Object.prototype.hasOwnProperty.call(l,m)&&p.indexOf(m)<0&&(u[m]=l[m]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function"){var f=0;for(m=Object.getOwnPropertySymbols(l);f<m.length;f++)p.indexOf(m[f])<0&&Object.prototype.propertyIsEnumerable.call(l,m[f])&&(u[m[f]]=l[m[f]])}return u}(e,["overlay"]);const a=document.createElement("div"),d=this.getHeight(o.height,o.splitChannels);a.style.height=`${d}px`,n&&s>0&&(a.style.marginTop=`-${d}px`),this.canvasWrapper.style.minHeight=`${d}px`,this.canvasWrapper.appendChild(a);const c=a.cloneNode();this.progressWrapper.appendChild(c),this.renderMultiCanvas(t,o,i,d,a,c)}render(t){return P(this,void 0,void 0,function*(){var e;this.timeouts.forEach(d=>d()),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const i=this.getPixelRatio(),s=this.scrollContainer.clientWidth,n=Math.ceil(t.duration*(this.options.minPxPerSec||0));this.isScrollable=n>s;const o=this.options.fillParent&&!this.isScrollable,a=(o?s:n)*i;if(this.wrapper.style.width=o?"100%":`${n}px`,this.scrollContainer.style.overflowX=this.isScrollable?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.audioData=t,this.emit("render"),this.options.splitChannels)for(let d=0;d<t.numberOfChannels;d++){const c=Object.assign(Object.assign({},this.options),(e=this.options.splitChannels)===null||e===void 0?void 0:e[d]);this.renderChannel([t.getChannelData(d)],c,a,d)}else{const d=[t.getChannelData(0)];t.numberOfChannels>1&&d.push(t.getChannelData(1)),this.renderChannel(d,this.options,a,0)}Promise.resolve().then(()=>this.emit("rendered"))})}reRender(){if(this.unsubscribeOnScroll.forEach(i=>i()),this.unsubscribeOnScroll=[],!this.audioData)return;const{scrollWidth:t}=this.scrollContainer,{right:e}=this.progressWrapper.getBoundingClientRect();if(this.render(this.audioData),this.isScrollable&&t!==this.scrollContainer.scrollWidth){const{right:i}=this.progressWrapper.getBoundingClientRect();let s=i-e;s*=2,s=s<0?Math.floor(s):Math.ceil(s),s/=2,this.scrollContainer.scrollLeft+=s}}zoom(t){this.options.minPxPerSec=t,this.reRender()}scrollIntoView(t,e=!1){const{scrollLeft:i,scrollWidth:s,clientWidth:n}=this.scrollContainer,o=t*s,a=i,d=i+n,c=n/2;if(this.isDragging)o+30>d?this.scrollContainer.scrollLeft+=30:o-30<a&&(this.scrollContainer.scrollLeft-=30);else{(o<a||o>d)&&(this.scrollContainer.scrollLeft=o-(this.options.autoCenter?c:0));const l=o-i-c;e&&this.options.autoCenter&&l>0&&(this.scrollContainer.scrollLeft+=Math.min(l,10))}{const l=this.scrollContainer.scrollLeft,p=l/s,u=(l+n)/s;this.emit("scroll",p,u,l,l+n)}}renderProgress(t,e){if(isNaN(t))return;const i=100*t;this.canvasWrapper.style.clipPath=`polygon(${i}% 0%, 100% 0%, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.transform=this.options.cursorWidth?`translateX(-${t*this.options.cursorWidth}px)`:"",this.isScrollable&&this.options.autoScroll&&this.scrollIntoView(t,e)}exportImage(t,e,i){return P(this,void 0,void 0,function*(){const s=this.canvasWrapper.querySelectorAll("canvas");if(!s.length)throw new Error("No waveform data");if(i==="dataURL"){const n=Array.from(s).map(o=>o.toDataURL(t,e));return Promise.resolve(n)}return Promise.all(Array.from(s).map(n=>new Promise((o,a)=>{n.toBlob(d=>{d?o(d):a(new Error("Could not export image"))},t,e)})))})}}U.MAX_CANVAS_WIDTH=8e3,U.MAX_NODES=10;class pe extends V{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}class et extends V{constructor(t=new AudioContext){super(),this.bufferNode=null,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this._playbackRate=1,this._duration=void 0,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.seeking=!1,this.autoplay=!1,this.addEventListener=this.on,this.removeEventListener=this.un,this.audioContext=t,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return P(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(t){if(this.currentSrc=t,this._duration=void 0,!t)return this.buffer=null,void this.emit("emptied");fetch(t).then(e=>{if(e.status>=400)throw new Error(`Failed to fetch ${t}: ${e.status} (${e.statusText})`);return e.arrayBuffer()}).then(e=>this.currentSrc!==t?null:this.audioContext.decodeAudioData(e)).then(e=>{this.currentSrc===t&&(this.buffer=e,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play())})}_play(){var t;if(!this.paused)return;this.paused=!1,(t=this.bufferNode)===null||t===void 0||t.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.buffer&&(this.bufferNode.buffer=this.buffer),this.bufferNode.playbackRate.value=this._playbackRate,this.bufferNode.connect(this.gainNode);let e=this.playedDuration*this._playbackRate;(e>=this.duration||e<0)&&(e=0,this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,e),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))}}_pause(){var t;this.paused=!0,(t=this.bufferNode)===null||t===void 0||t.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime}play(){return P(this,void 0,void 0,function*(){this.paused&&(this._play(),this.emit("play"))})}pause(){this.paused||(this._pause(),this.emit("pause"))}stopAt(t){const e=t-this.currentTime,i=this.bufferNode;i?.stop(this.audioContext.currentTime+e),i?.addEventListener("ended",()=>{i===this.bufferNode&&(this.bufferNode=null,this.pause())},{once:!0})}setSinkId(t){return P(this,void 0,void 0,function*(){return this.audioContext.setSinkId(t)})}get playbackRate(){return this._playbackRate}set playbackRate(t){this._playbackRate=t,this.bufferNode&&(this.bufferNode.playbackRate.value=t)}get currentTime(){return(this.paused?this.playedDuration:this.playedDuration+(this.audioContext.currentTime-this.playStartTime))*this._playbackRate}set currentTime(t){const e=!this.paused;e&&this._pause(),this.playedDuration=t/this._playbackRate,e&&this._play(),this.emit("seeking"),this.emit("timeupdate")}get duration(){var t,e;return(t=this._duration)!==null&&t!==void 0?t:((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}set duration(t){this._duration=t}get volume(){return this.gainNode.gain.value}set volume(t){this.gainNode.gain.value=t,this.emit("volumechange")}get muted(){return this._muted}set muted(t){this._muted!==t&&(this._muted=t,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}canPlayType(t){return/^(audio|video)\//.test(t)}getGainNode(){return this.gainNode}getChannelData(){const t=[];if(!this.buffer)return t;const e=this.buffer.numberOfChannels;for(let i=0;i<e;i++)t.push(this.buffer.getChannelData(i));return t}removeAttribute(t){switch(t){case"src":this.src="";break;case"playbackRate":this.playbackRate=0;break;case"currentTime":this.currentTime=0;break;case"duration":this.duration=0;break;case"volume":this.volume=0;break;case"muted":this.muted=!1}}}const me={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class _ extends ue{static create(t){return new _(t)}constructor(t){const e=t.media||(t.backend==="WebAudio"?new et:void 0);super({media:e,mediaControls:t.mediaControls,autoplay:t.autoplay,playbackRate:t.audioRate}),this.plugins=[],this.decodedData=null,this.stopAtPosition=null,this.subscriptions=[],this.mediaSubscriptions=[],this.abortController=null,this.options=Object.assign({},me,t),this.timer=new pe;const i=e?void 0:this.getMediaElement();this.renderer=new U(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const s=this.options.url||this.getSrc()||"";Promise.resolve().then(()=>{this.emit("init");const{peaks:n,duration:o}=this.options;(s||n&&o)&&this.load(s,n,o).catch(()=>null)})}updateProgress(t=this.getCurrentTime()){return this.renderer.renderProgress(t/this.getDuration(),this.isPlaying()),t}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{if(!this.isSeeking()){const t=this.updateProgress();this.emit("timeupdate",t),this.emit("audioprocess",t),this.stopAtPosition!=null&&this.isPlaying()&&t>=this.stopAtPosition&&this.pause()}}))}initPlayerEvents(){this.isPlaying()&&(this.emit("play"),this.timer.start()),this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const t=this.updateProgress();this.emit("timeupdate",t)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("emptied",()=>{this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("ended",()=>{this.emit("timeupdate",this.getDuration()),this.emit("finish"),this.stopAtPosition=null}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}),this.onMediaEvent("error",()=>{var t;this.emit("error",(t=this.getMediaElement().error)!==null&&t!==void 0?t:new Error("Media error")),this.stopAtPosition=null}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(t,e)=>{this.options.interact&&(this.seekTo(t),this.emit("interaction",t*this.getDuration()),this.emit("click",t,e))}),this.renderer.on("dblclick",(t,e)=>{this.emit("dblclick",t,e)}),this.renderer.on("scroll",(t,e,i,s)=>{const n=this.getDuration();this.emit("scroll",t*n,e*n,i,s)}),this.renderer.on("render",()=>{this.emit("redraw")}),this.renderer.on("rendered",()=>{this.emit("redrawcomplete")}),this.renderer.on("dragstart",t=>{this.emit("dragstart",t)}),this.renderer.on("dragend",t=>{this.emit("dragend",t)}));{let t;this.subscriptions.push(this.renderer.on("drag",e=>{if(!this.options.interact)return;let i;this.renderer.renderProgress(e),clearTimeout(t),this.isPlaying()?i=0:this.options.dragToSeek===!0?i=200:typeof this.options.dragToSeek=="object"&&this.options.dragToSeek!==void 0&&(i=this.options.dragToSeek.debounceTime),t=setTimeout(()=>{this.seekTo(e)},i),this.emit("interaction",e*this.getDuration()),this.emit("drag",e)}))}}initPlugins(){var t;!((t=this.options.plugins)===null||t===void 0)&&t.length&&this.options.plugins.forEach(e=>{this.registerPlugin(e)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(t=>t()),this.mediaSubscriptions=[]}setOptions(t){this.options=Object.assign({},this.options,t),t.duration&&!t.peaks&&(this.decodedData=j.createBuffer(this.exportPeaks(),t.duration)),t.peaks&&t.duration&&(this.decodedData=j.createBuffer(t.peaks,t.duration)),this.renderer.setOptions(this.options),t.audioRate&&this.setPlaybackRate(t.audioRate),t.mediaControls!=null&&(this.getMediaElement().controls=t.mediaControls)}registerPlugin(t){if(this.plugins.includes(t))return t;t._init(this),this.plugins.push(t);const e=t.once("destroy",()=>{this.plugins=this.plugins.filter(i=>i!==t),this.subscriptions=this.subscriptions.filter(i=>i!==e)});return this.subscriptions.push(e),t}unregisterPlugin(t){this.plugins=this.plugins.filter(e=>e!==t),t.destroy()}getWrapper(){return this.renderer.getWrapper()}getWidth(){return this.renderer.getWidth()}getScroll(){return this.renderer.getScroll()}setScroll(t){return this.renderer.setScroll(t)}setScrollTime(t){const e=t/this.getDuration();this.renderer.setScrollPercentage(e)}getActivePlugins(){return this.plugins}loadAudio(t,e,i,s){return P(this,void 0,void 0,function*(){var n;if(this.emit("load",t),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,this.stopAtPosition=null,!e&&!i){const a=this.options.fetchParams||{};window.AbortController&&!a.signal&&(this.abortController=new AbortController,a.signal=(n=this.abortController)===null||n===void 0?void 0:n.signal);const d=l=>this.emit("loading",l);e=yield he.fetchBlob(t,d,a);const c=this.options.blobMimeType;c&&(e=new Blob([e],{type:c}))}this.setSrc(t,e);const o=yield new Promise(a=>{const d=s||this.getDuration();d?a(d):this.mediaSubscriptions.push(this.onMediaEvent("loadedmetadata",()=>a(this.getDuration()),{once:!0}))});if(!t&&!e){const a=this.getMediaElement();a instanceof et&&(a.duration=o)}if(i)this.decodedData=j.createBuffer(i,o||0);else if(e){const a=yield e.arrayBuffer();this.decodedData=yield j.decode(a,this.options.sampleRate)}this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData)),this.emit("ready",this.getDuration())})}load(t,e,i){return P(this,void 0,void 0,function*(){try{return yield this.loadAudio(t,void 0,e,i)}catch(s){throw this.emit("error",s),s}})}loadBlob(t,e,i){return P(this,void 0,void 0,function*(){try{return yield this.loadAudio("",t,e,i)}catch(s){throw this.emit("error",s),s}})}zoom(t){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(t),this.emit("zoom",t)}getDecodedData(){return this.decodedData}exportPeaks({channels:t=2,maxLength:e=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const s=Math.min(t,this.decodedData.numberOfChannels),n=[];for(let o=0;o<s;o++){const a=this.decodedData.getChannelData(o),d=[],c=a.length/e;for(let l=0;l<e;l++){const p=a.slice(Math.floor(l*c),Math.ceil((l+1)*c));let u=0;for(let m=0;m<p.length;m++){const f=p[m];Math.abs(f)>Math.abs(u)&&(u=f)}d.push(Math.round(u*i)/i)}n.push(d)}return n}getDuration(){let t=super.getDuration()||0;return t!==0&&t!==1/0||!this.decodedData||(t=this.decodedData.duration),t}toggleInteraction(t){this.options.interact=t}setTime(t){this.stopAtPosition=null,super.setTime(t),this.updateProgress(t),this.emit("timeupdate",t)}seekTo(t){const e=this.getDuration()*t;this.setTime(e)}play(t,e){const i=Object.create(null,{play:{get:()=>super.play}});return P(this,void 0,void 0,function*(){t!=null&&this.setTime(t);const s=yield i.play.call(this);return e!=null&&(this.media instanceof et?this.media.stopAt(e):this.stopAtPosition=e),s})}playPause(){return P(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(t){this.setTime(this.getCurrentTime()+t)}empty(){this.load("",[[0]],.001)}setMediaElement(t){this.unsubscribePlayerEvents(),super.setMediaElement(t),this.initPlayerEvents()}exportImage(){return P(this,arguments,void 0,function*(t="image/png",e=1,i="dataURL"){return this.renderer.exportImage(t,e,i)})}destroy(){var t;this.emit("destroy"),(t=this.abortController)===null||t===void 0||t.abort(),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}_.BasePlugin=class extends V{constructor(r){super(),this.subscriptions=[],this.isDestroyed=!1,this.options=r}onInit(){}_init(r){this.isDestroyed&&(this.subscriptions=[],this.isDestroyed=!1),this.wavesurfer=r,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(r=>r()),this.subscriptions=[],this.isDestroyed=!0,this.wavesurfer=void 0}},_.dom=de;function it(r,t,e,i){return new(e||(e=Promise))(function(s,n){function o(c){try{d(i.next(c))}catch(l){n(l)}}function a(c){try{d(i.throw(c))}catch(l){n(l)}}function d(c){var l;c.done?s(c.value):(l=c.value,l instanceof e?l:new e(function(p){p(l)})).then(o,a)}d((i=i.apply(r,[])).next())})}class Et{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),i?.once){const s=()=>{this.un(t,s),this.un(t,e)};return this.on(t,s),s}return()=>this.un(t,e)}un(t,e){var i;(i=this.listeners[t])===null||i===void 0||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(i=>i(...e))}}class fe extends Et{constructor(t){super(),this.subscriptions=[],this.isDestroyed=!1,this.options=t}onInit(){}_init(t){this.isDestroyed&&(this.subscriptions=[],this.isDestroyed=!1),this.wavesurfer=t,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(t=>t()),this.subscriptions=[],this.isDestroyed=!0,this.wavesurfer=void 0}}class ve extends Et{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}const ge=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Y extends fe{constructor(t){var e,i,s,n,o,a;super(Object.assign(Object.assign({},t),{audioBitsPerSecond:(e=t.audioBitsPerSecond)!==null&&e!==void 0?e:128e3,scrollingWaveform:(i=t.scrollingWaveform)!==null&&i!==void 0&&i,scrollingWaveformWindow:(s=t.scrollingWaveformWindow)!==null&&s!==void 0?s:5,continuousWaveform:(n=t.continuousWaveform)!==null&&n!==void 0&&n,renderRecordedAudio:(o=t.renderRecordedAudio)===null||o===void 0||o,mediaRecorderTimeslice:(a=t.mediaRecorderTimeslice)!==null&&a!==void 0?a:void 0})),this.stream=null,this.mediaRecorder=null,this.dataWindow=null,this.isWaveformPaused=!1,this.lastStartTime=0,this.lastDuration=0,this.duration=0,this.timer=new ve,this.subscriptions.push(this.timer.on("tick",()=>{const d=performance.now()-this.lastStartTime;this.duration=this.isPaused()?this.duration:this.lastDuration+d,this.emit("record-progress",this.duration)}))}static create(t){return new Y(t||{})}renderMicStream(t){var e;const i=new AudioContext,s=i.createMediaStreamSource(t),n=i.createAnalyser();s.connect(n),this.options.continuousWaveform&&(n.fftSize=32);const o=n.frequencyBinCount,a=new Float32Array(o);let d=0;this.wavesurfer&&((e=this.originalOptions)!==null&&e!==void 0||(this.originalOptions=Object.assign({},this.wavesurfer.options)),this.wavesurfer.options.interact=!1,this.options.scrollingWaveform&&(this.wavesurfer.options.cursorWidth=0));const c=setInterval(()=>{var l,p,u,m;if(!this.isWaveformPaused){if(n.getFloatTimeDomainData(a),this.options.scrollingWaveform){const f=Math.floor((this.options.scrollingWaveformWindow||0)*i.sampleRate),y=Math.min(f,this.dataWindow?this.dataWindow.length+o:o),v=new Float32Array(f);if(this.dataWindow){const S=Math.max(0,f-this.dataWindow.length);v.set(this.dataWindow.slice(-y+o),S)}v.set(a,f-o),this.dataWindow=v}else if(this.options.continuousWaveform){if(!this.dataWindow){const y=this.options.continuousWaveformDuration?Math.round(100*this.options.continuousWaveformDuration):((p=(l=this.wavesurfer)===null||l===void 0?void 0:l.getWidth())!==null&&p!==void 0?p:0)*window.devicePixelRatio;this.dataWindow=new Float32Array(y)}let f=0;for(let y=0;y<o;y++){const v=Math.abs(a[y]);v>f&&(f=v)}if(d+1>this.dataWindow.length){const y=new Float32Array(2*this.dataWindow.length);y.set(this.dataWindow,0),this.dataWindow=y}this.dataWindow[d]=f,d++}else this.dataWindow=a;if(this.wavesurfer){const f=((m=(u=this.dataWindow)===null||u===void 0?void 0:u.length)!==null&&m!==void 0?m:0)/100;this.wavesurfer.load("",[this.dataWindow],this.options.scrollingWaveform?this.options.scrollingWaveformWindow:f).then(()=>{this.wavesurfer&&this.options.continuousWaveform&&(this.wavesurfer.setTime(this.getDuration()/1e3),this.wavesurfer.options.minPxPerSec||this.wavesurfer.setOptions({minPxPerSec:this.wavesurfer.getWidth()/this.wavesurfer.getDuration()}))}).catch(y=>{console.error("Error rendering real-time recording data:",y)})}}},10);return{onDestroy:()=>{clearInterval(c),s?.disconnect(),i?.close()},onEnd:()=>{this.isWaveformPaused=!0,clearInterval(c),this.stopMic()}}}startMic(t){return it(this,void 0,void 0,function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:t==null||t})}catch(n){throw new Error("Error accessing the microphone: "+n.message)}const{onDestroy:i,onEnd:s}=this.renderMicStream(e);return this.subscriptions.push(this.once("destroy",i)),this.subscriptions.push(this.once("record-end",s)),this.stream=e,e})}stopMic(){this.stream&&(this.stream.getTracks().forEach(t=>t.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(t){return it(this,void 0,void 0,function*(){const e=this.stream||(yield this.startMic(t));this.dataWindow=null;const i=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||ge.find(o=>MediaRecorder.isTypeSupported(o)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const s=[];i.ondataavailable=o=>{o.data.size>0&&s.push(o.data),this.emit("record-data-available",o.data)};const n=o=>{var a;const d=new Blob(s,{type:i.mimeType});this.emit(o,d),this.options.renderRecordedAudio&&(this.applyOriginalOptionsIfNeeded(),(a=this.wavesurfer)===null||a===void 0||a.load(URL.createObjectURL(d)))};i.onpause=()=>n("record-pause"),i.onstop=()=>n("record-end"),i.start(this.options.mediaRecorderTimeslice),this.lastStartTime=performance.now(),this.lastDuration=0,this.duration=0,this.isWaveformPaused=!1,this.timer.start(),this.emit("record-start")})}getDuration(){return this.duration}isRecording(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)==="recording"}isPaused(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)==="paused"}isActive(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)!=="inactive"}stopRecording(){var t;this.isActive()&&((t=this.mediaRecorder)===null||t===void 0||t.stop(),this.timer.stop())}pauseRecording(){var t,e;this.isRecording()&&(this.isWaveformPaused=!0,(t=this.mediaRecorder)===null||t===void 0||t.requestData(),(e=this.mediaRecorder)===null||e===void 0||e.pause(),this.timer.stop(),this.lastDuration=this.duration)}resumeRecording(){var t;this.isPaused()&&(this.isWaveformPaused=!1,(t=this.mediaRecorder)===null||t===void 0||t.resume(),this.timer.start(),this.lastStartTime=performance.now(),this.emit("record-resume"))}static getAvailableAudioDevices(){return it(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(t=>t.filter(e=>e.kind==="audioinput"))})}destroy(){this.applyOriginalOptionsIfNeeded(),super.destroy(),this.stopRecording(),this.stopMic()}applyOriginalOptionsIfNeeded(){this.wavesurfer&&this.originalOptions&&(this.wavesurfer.setOptions(this.originalOptions),delete this.originalOptions)}}const be=(r,t)=>{const{libConfig:{enforceDownloadInNewTab:e=!1}}=h.useContext(_t);return h.useCallback(()=>{if(!r)return;const s=oe({enforceDownloadInNewTab:e,url:r,filename:t});s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)},[r,e,t])},st=({widgetMgr:r,id:t,formId:e,key:i,defaultValue:s})=>{h.useEffect(()=>{const l=r.getElementState(t,i);q(l)&&X(s)&&r.setElementState(t,i,s)},[r,t,i,s]);const[n,o]=h.useState(r.getElementState(t,i)??s),a=h.useCallback(l=>{r.setElementState(t,i,l),o(l)},[r,t,i]),d=h.useMemo(()=>({formId:e||""}),[e]),c=h.useCallback(()=>a(s),[s,a]);return re({element:d,widgetMgr:r,onFormCleared:c}),[n,a]},ye=async({files:r,uploadClient:t,widgetMgr:e,widgetInfo:i,fragmentId:s})=>{let n=[];try{n=await t.fetchFileURLs(r)}catch(c){return{successfulUploads:[],failedUploads:r.map(l=>({file:l,error:ut(c)}))}}const o=$t(r,n),a=[],d=[];return await Promise.all(o.map(async([c,l])=>{if(!c||!l||!l.uploadUrl||!l.fileId)return{file:c,fileUrl:l,error:new Error("No upload URL found")};try{await t.uploadFile({id:l.fileId,formId:i.formId||""},l.uploadUrl,c),a.push({fileUrl:l,file:c})}catch(p){const u=ut(p);d.push({file:c,error:u})}})),e.setFileUploaderStateValue(i,new zt({uploadedFileInfo:a.map(({file:c,fileUrl:l})=>new Ht({fileId:l.fileId,fileUrls:l,name:c.name,size:c.size}))}),{fromUi:!0},s),{successfulUploads:a,failedUploads:d}},we=D("div",{target:"e1vek2b60"})(),ft=D("div",{target:"e1vek2b61"})(({theme:r,disabled:t})=>({height:r.sizes.largestElementHeight,width:"100%",background:r.colors.secondaryBg,borderRadius:r.radii.default,marginBottom:r.spacing.twoXS,display:"flex",alignItems:"center",position:"relative",paddingLeft:r.spacing.xs,paddingRight:r.spacing.sm,border:r.colors.widgetBorderColor?`${r.sizes.borderWidth} solid ${r.colors.widgetBorderColor}`:void 0,cursor:t?"not-allowed":"auto"})),Se=D("div",{target:"e1vek2b62"})({flex:1}),Ce=D("div",{target:"e1vek2b63"})(({show:r})=>({display:r?"block":"none"})),Ee=D("span",{target:"e1vek2b64"})(({theme:r,isPlayingOrRecording:t,disabled:e})=>({margin:r.spacing.sm,fontFamily:r.fonts.monospace,color:e?r.colors.fadedText40:t?r.colors.bodyText:r.colors.fadedText60,backgroundColor:r.colors.secondaryBg,fontSize:r.fontSizes.sm})),Rt=D("div",{target:"e1vek2b65"})({width:"100%",textAlign:"center",overflow:"hidden"}),Pt=D("span",{target:"e1vek2b66"})(({theme:r})=>({color:r.colors.bodyText})),Re=D("a",{target:"e1vek2b67"})(({theme:r})=>({color:r.colors.link,textDecoration:r.linkUnderline?"underline":"none"})),Pe=D("div",{target:"e1vek2b68"})(({theme:r})=>({height:r.sizes.largestElementHeight,display:"flex",justifyContent:"center",alignItems:"center"})),De=D("div",{target:"e1vek2b69"})(({theme:r})=>{const t="0.625em";return{opacity:.2,width:"100%",height:t,backgroundSize:t,backgroundImage:`radial-gradient(${r.colors.fadedText10} 40%, transparent 40%)`,backgroundRepeat:"repeat"}}),xe=D("span",{target:"e1vek2b610"})(({theme:r})=>({"& > button":{color:r.colors.primary,padding:r.spacing.threeXS},"& > button:hover, & > button:focus":{color:r.colors.red}})),ke=D("span",{target:"e1vek2b611"})(({theme:r})=>({"& > button":{padding:r.spacing.threeXS,color:r.colors.fadedText60},"& > button:hover, & > button:focus":{color:r.colors.bodyText}})),Dt=D("span",{target:"e1vek2b612"})(({theme:r})=>({"& > button":{padding:r.spacing.threeXS,color:r.colors.fadedText60},"& > button:hover, & > button:focus":{color:r.colors.bodyText}})),rt=D("div",{target:"e1vek2b613"})(({theme:r})=>({display:"flex",justifyContent:"center",alignItems:"center",flexGrow:0,flexShrink:1,padding:r.spacing.xs,gap:r.spacing.twoXS,marginRight:r.spacing.twoXS})),Ae=D("div",{target:"e1vek2b614"})(({theme:r})=>({marginLeft:r.spacing.sm})),$=({onClick:r,disabled:t,ariaLabel:e,iconContent:i})=>g(qt,{kind:jt.BORDERLESS_ICON,onClick:r,disabled:t,"aria-label":e,containerWidth:!0,"data-testid":"stAudioInputActionButton",children:g(Gt,{content:i,size:"lg",color:"inherit"})}),Te=({disabled:r,stopRecording:t})=>g(xe,{children:g($,{onClick:t,disabled:r,ariaLabel:"Stop recording",iconContent:St})}),We=({disabled:r,isPlaying:t,onClickPlayPause:e})=>g(Dt,{children:t?g($,{onClick:e,disabled:r,ariaLabel:"Pause",iconContent:bt}):g($,{onClick:e,disabled:r,ariaLabel:"Play",iconContent:yt})}),Me=({disabled:r,startRecording:t})=>g(ke,{children:g($,{onClick:t,disabled:r,ariaLabel:"Record",iconContent:gt})}),Oe=({onClick:r})=>g(Dt,{children:g($,{disabled:!1,onClick:r,ariaLabel:"Reset",iconContent:wt})}),Ie=({disabled:r,isRecording:t,isPlaying:e,isUploading:i,isError:s,recordingUrlExists:n,startRecording:o,stopRecording:a,onClickPlayPause:d,onClear:c})=>s?g(rt,{children:g(Oe,{onClick:c})}):i?g(rt,{children:g(Vt,{"aria-label":"Uploading",size:"base",margin:"0",padding:"0"})}):N(rt,{children:[t?g(Te,{disabled:r,stopRecording:a}):g(Me,{disabled:r,startRecording:o}),n&&g(We,{disabled:r,isPlaying:e,onClickPlayPause:d})]}),Le=h.memo(Ie),Be=()=>g(Rt,{children:g(Pt,{children:"An error has occurred, please try again."})}),Ne=h.memo(Be),Ue=4,Fe=4,_e=4,$e=8,ze=0,G="00:00",He=Xt.getLogger("convertAudioToWav");async function Ve(r){const t=new window.AudioContext,e=await r.arrayBuffer();let i;try{i=await t.decodeAudioData(e)}catch(m){He.error(m);return}const s=44,n=i.numberOfChannels,o=i.sampleRate,a=i.length*n*2+s,d=new ArrayBuffer(a),c=new DataView(d),l={0:{type:"string",value:"RIFF"},4:{type:"uint32",value:a-8},8:{type:"string",value:"WAVE"},12:{type:"string",value:"fmt "},16:{type:"uint32",value:16},20:{type:"uint16",value:1},22:{type:"uint16",value:n},24:{type:"uint32",value:o},28:{type:"uint32",value:o*n*2},32:{type:"uint16",value:n*2},34:{type:"uint16",value:16},36:{type:"string",value:"data"},40:{type:"uint32",value:i.length*n*2}};Object.entries(l).forEach(([m,{type:f,value:y}])=>{const v=parseInt(m,10);f==="string"?je(c,v,y):f==="uint32"?c.setUint32(v,y,!0):f==="uint16"&&c.setUint16(v,y,!0)});let p=s;for(let m=0;m<i.length;m++)for(let f=0;f<n;f++){const y=Math.max(-1,Math.min(1,i.getChannelData(f)[m]));c.setInt16(p,y*32767,!0),p+=2}const u=new Uint8Array(d);return new Blob([u],{type:"audio/wav"})}function je(r,t,e){for(let i=0;i<e.length;i++)r.setUint8(t+i,e.charCodeAt(i))}const vt=r=>{const t=Math.floor(r/1e3),e=Math.floor(t/60),i=Math.floor(e/60),s=t%60,n=e%60,o=s.toString().padStart(2,"0"),a=n.toString().padStart(2,"0"),d=i.toString().padStart(2,"0");return e<60?`${a}:${o}`:`${d}:${a}:${o}`},Ge=()=>N(Rt,{children:[g(Pt,{children:"This app would like to use your microphone."})," ",g(Re,{href:Yt,rel:"noopener noreferrer",target:"_blank",children:"Learn how to allow access."})]}),qe=h.memo(Ge),Xe=()=>g(Pe,{children:g(De,{})}),Ye=h.memo(Xe),Ke=({element:r,uploadClient:t,widgetMgr:e,fragmentId:i,disabled:s})=>{const n=Kt(),o=ae(n),[a,d]=h.useState(null),c=h.useRef(null),[l,p]=st({widgetMgr:e,id:r.id,key:"deleteFileUrl",defaultValue:null}),[u,m]=h.useState(null),[f,y]=h.useState([]),[v,S]=h.useState(null),[b,x]=st({widgetMgr:e,id:r.id,key:"recordingUrl",defaultValue:null}),[,R]=h.useState(0),w=()=>{R(C=>C+1)},[k,A]=h.useState(G),[M,T]=st({widgetMgr:e,id:r.id,formId:r.formId,key:"recordingTime",defaultValue:G}),[O,F]=h.useState(!1),[L,xt]=h.useState(!1),[nt,kt]=h.useState(!1),[At,ot]=h.useState(!1),[K,J]=h.useState(!1),at=r.id,I=r.formId,lt=h.useCallback(async C=>{ot(!0),X(I)&&e.setFormsWithUploadsInProgress(new Set([I]));let E;if(C.type==="audio/wav"?E=C:E=await Ve(C),!E){J(!0);return}const W=URL.createObjectURL(E),Bt=new Date().toISOString().slice(0,16).replace(":","-"),Nt=new File([E],`${Bt}_audio.wav`,{type:E.type});x(W),ye({files:[Nt],uploadClient:t,widgetMgr:e,widgetInfo:{id:at,formId:I},fragmentId:i}).then(({successfulUploads:Ut,failedUploads:Ft})=>{if(Ft.length>0){J(!0);return}const Z=Ut[0];Z&&Z.fileUrl.deleteUrl&&p(Z.fileUrl.deleteUrl)}).finally(()=>{X(I)&&e.setFormsWithUploadsInProgress(new Set),ot(!1)})},[x,t,e,at,I,i,p]),B=h.useCallback(({updateWidgetManager:C,deleteFile:E})=>{q(a)||q(l)||(x(null),a.empty(),E&&t.deleteFile(l),p(null),A(G),T(G),C&&e.setFileUploaderStateValue(r,{},{fromUi:!0},i),F(!1),X(b)&&URL.revokeObjectURL(b))},[l,b,t,a,r,e,i,T,x,p]);h.useEffect(()=>{if(q(I))return;const C=new ne;return C.manageFormClearListener(e,I,()=>B({updateWidgetManager:!0,deleteFile:!1})),()=>C.disconnect()},[I,B,e]);const ct=h.useCallback(()=>{if(c.current===null)return;const C=_.create({container:c.current,waveColor:b?tt(n.colors.fadedText40,n.colors.secondaryBg):n.colors.primary,progressColor:n.colors.bodyText,height:Jt(n.sizes.largestElementHeight)-2*Ue,barWidth:Fe,barGap:_e,barRadius:$e,cursorWidth:ze,url:b??void 0});C.on("timeupdate",W=>{A(vt(W*1e3))}),C.on("pause",()=>{w()});const E=C.registerPlugin(Y.create({scrollingWaveform:!1,renderRecordedAudio:!0}));return E.on("record-end",W=>{lt(W)}),E.on("record-progress",W=>{T(vt(W))}),d(C),m(E),()=>{C&&C.destroy(),E&&E.destroy()}},[lt]);h.useEffect(()=>ct(),[ct]),h.useEffect(()=>{Qt(o,n)||a?.setOptions({waveColor:b?tt(n.colors.fadedText40,n.colors.secondaryBg):n.colors.primary,progressColor:n.colors.bodyText})},[n,o,b,a]);const Tt=h.useCallback(()=>{a&&(a.playPause(),F(!0),w())},[a]),Wt=h.useCallback(async()=>{let C=v;nt||(await navigator.mediaDevices.getUserMedia({audio:!0}).then(()=>Y.getAvailableAudioDevices().then(E=>{if(y(E),E.length>0){const{deviceId:W}=E[0];S(W),C=W}})).catch(E=>{xt(!0)}),kt(!0)),!(!u||!C||!a)&&(a.setOptions({waveColor:n.colors.primary}),b&&B({updateWidgetManager:!1,deleteFile:!0}),u.startRecording({deviceId:C}).then(()=>{w()}))},[v,u,n,a,b,B,nt]),Mt=h.useCallback(()=>{u&&(u.stopRecording(),a?.setOptions({waveColor:tt(n.colors.fadedText40,n.colors.secondaryBg)}))},[u,a,n]),Ot=be(b,"recording.wav"),Q=!!u?.isRecording(),dt=!!a?.isPlaying(),It=Q||dt,ht=!Q&&!b&&!L,Lt=L||ht||K;return N(we,{className:"stAudioInput","data-testid":"stAudioInput",children:[g(ie,{label:r.label,disabled:s,labelVisibility:Zt(r.labelVisibility?.value),children:r.help&&g(Ae,{children:g(te,{content:r.help,placement:ee.TOP})})}),N(ft,{disabled:s,children:[N(se,{isFullScreen:!1,disableFullscreenMode:!0,target:ft,children:[b&&g(pt,{label:"Download as WAV",icon:le,onClick:()=>Ot()}),l&&g(pt,{label:"Clear recording",icon:ce,onClick:()=>B({updateWidgetManager:!0,deleteFile:!0})})]}),g(Le,{isRecording:Q,isPlaying:dt,isUploading:At,isError:K,recordingUrlExists:!!b,startRecording:Wt,stopRecording:Mt,onClickPlayPause:Tt,onClear:()=>{B({updateWidgetManager:!1,deleteFile:!0}),J(!1)},disabled:s||L}),N(Se,{children:[K&&g(Ne,{}),ht&&g(Ye,{}),L&&g(qe,{}),g(Ce,{"data-testid":"stAudioInputWaveSurfer",ref:c,show:!Lt})]}),g(Ee,{isPlayingOrRecording:It,disabled:s,"data-testid":"stAudioInputWaveformTimeCode",children:O?k:M})]})]})},ri=h.memo(Ke);export{ri as default};
