"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class PageConfig(google.protobuf.message.Message):
    """Properties of the page that the app creator may configure."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Layout:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _LayoutEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PageConfig._Layout.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        CENTERED: PageConfig._Layout.ValueType  # 0
        """Render page elements in a centered block, roughly 730px wide."""
        WIDE: PageConfig._Layout.ValueType  # 1
        """Use the full browser width to render elements."""
        LAYOUT_UNSET: PageConfig._Layout.ValueType  # 2
        """Unset distinguishes that config was not provided - on initial call, will
        set to centered as default. Otherwise, inherit value from previous call
        """

    class Layout(_Layout, metaclass=_LayoutEnumTypeWrapper): ...
    CENTERED: PageConfig.Layout.ValueType  # 0
    """Render page elements in a centered block, roughly 730px wide."""
    WIDE: PageConfig.Layout.ValueType  # 1
    """Use the full browser width to render elements."""
    LAYOUT_UNSET: PageConfig.Layout.ValueType  # 2
    """Unset distinguishes that config was not provided - on initial call, will
    set to centered as default. Otherwise, inherit value from previous call
    """

    class _SidebarState:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SidebarStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PageConfig._SidebarState.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        AUTO: PageConfig._SidebarState.ValueType  # 0
        """Expand the sidebar if page width exceeds ~1000px; otherwise, collapse it"""
        EXPANDED: PageConfig._SidebarState.ValueType  # 1
        """Force the sidebar to be shown."""
        COLLAPSED: PageConfig._SidebarState.ValueType  # 2
        """Force the sidebar to be hidden."""
        SIDEBAR_UNSET: PageConfig._SidebarState.ValueType  # 3
        """Unset distinguishes that config was not provided - on initial call, will
        set to auto as default. Otherwise, inherit value from previous call
        """

    class SidebarState(_SidebarState, metaclass=_SidebarStateEnumTypeWrapper): ...
    AUTO: PageConfig.SidebarState.ValueType  # 0
    """Expand the sidebar if page width exceeds ~1000px; otherwise, collapse it"""
    EXPANDED: PageConfig.SidebarState.ValueType  # 1
    """Force the sidebar to be shown."""
    COLLAPSED: PageConfig.SidebarState.ValueType  # 2
    """Force the sidebar to be hidden."""
    SIDEBAR_UNSET: PageConfig.SidebarState.ValueType  # 3
    """Unset distinguishes that config was not provided - on initial call, will
    set to auto as default. Otherwise, inherit value from previous call
    """

    @typing.final
    class MenuItems(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        GET_HELP_URL_FIELD_NUMBER: builtins.int
        HIDE_GET_HELP_FIELD_NUMBER: builtins.int
        REPORT_A_BUG_URL_FIELD_NUMBER: builtins.int
        HIDE_REPORT_A_BUG_FIELD_NUMBER: builtins.int
        ABOUT_SECTION_MD_FIELD_NUMBER: builtins.int
        CLEAR_ABOUT_MD_FIELD_NUMBER: builtins.int
        get_help_url: builtins.str
        hide_get_help: builtins.bool
        report_a_bug_url: builtins.str
        hide_report_a_bug: builtins.bool
        about_section_md: builtins.str
        clear_about_md: builtins.bool
        """For multiple calls to set_page_config, clears set about markdown"""
        def __init__(
            self,
            *,
            get_help_url: builtins.str = ...,
            hide_get_help: builtins.bool = ...,
            report_a_bug_url: builtins.str = ...,
            hide_report_a_bug: builtins.bool = ...,
            about_section_md: builtins.str = ...,
            clear_about_md: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["about_section_md", b"about_section_md", "clear_about_md", b"clear_about_md", "get_help_url", b"get_help_url", "hide_get_help", b"hide_get_help", "hide_report_a_bug", b"hide_report_a_bug", "report_a_bug_url", b"report_a_bug_url"]) -> None: ...

    TITLE_FIELD_NUMBER: builtins.int
    FAVICON_FIELD_NUMBER: builtins.int
    LAYOUT_FIELD_NUMBER: builtins.int
    INITIAL_SIDEBAR_STATE_FIELD_NUMBER: builtins.int
    MENU_ITEMS_FIELD_NUMBER: builtins.int
    title: builtins.str
    favicon: builtins.str
    layout: global___PageConfig.Layout.ValueType
    initial_sidebar_state: global___PageConfig.SidebarState.ValueType
    @property
    def menu_items(self) -> global___PageConfig.MenuItems: ...
    def __init__(
        self,
        *,
        title: builtins.str = ...,
        favicon: builtins.str = ...,
        layout: global___PageConfig.Layout.ValueType = ...,
        initial_sidebar_state: global___PageConfig.SidebarState.ValueType = ...,
        menu_items: global___PageConfig.MenuItems | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["menu_items", b"menu_items"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["favicon", b"favicon", "initial_sidebar_state", b"initial_sidebar_state", "layout", b"layout", "menu_items", b"menu_items", "title", b"title"]) -> None: ...

global___PageConfig = PageConfig
