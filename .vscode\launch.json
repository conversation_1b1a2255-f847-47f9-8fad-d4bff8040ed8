{"version": "0.2.0", "configurations": [{"name": "Streamlit App", "type": "python", "request": "launch", "module": "streamlit", "args": ["run", "app.py", "--server.port=8501", "--server.headless=false"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src:${workspaceFolder}"}}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}]}