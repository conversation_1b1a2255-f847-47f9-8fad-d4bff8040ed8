import{by as $,bR as ie,bS as v,r as u,bT as p}from"./index.DKN5MVff.js";var T=Object.freeze({default:"default",toggle:"toggle",toggle_round:"toggle"}),ye=Object.freeze({top:"top",right:"right",bottom:"bottom",left:"left"});function U(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),o.push.apply(o,r)}return o}function P(e){for(var t=1;t<arguments.length;t++){var o=arguments[t]!=null?arguments[t]:{};t%2?U(Object(o),!0).forEach(function(r){q(e,r,o[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):U(Object(o)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(o,r))})}return e}function q(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function ne(e){var t=e.$disabled,o=e.$checked,r=e.$error,i=e.$isIndeterminate,a=e.$theme,n=e.$isFocusVisible,l=a.colors;return t?l.tickFillDisabled:o||i?"transparent":r?l.borderNegative:n?l.borderSelected:l.tickBorder}function ae(e){var t=e.$labelPlacement,o=t===void 0?"":t,r=e.$theme,i=r.sizing,a=i.scale300,n;switch(o){case"top":n="Bottom";break;case"bottom":n="Top";break;case"left":n="Right";break;default:case"right":n="Left";break}return r.direction==="rtl"&&n==="Left"?n="Right":r.direction==="rtl"&&n==="Right"&&(n="Left"),q({},"padding".concat(n),a)}function le(e){var t=e.$disabled,o=e.$checked,r=e.$isIndeterminate,i=e.$error,a=e.$isHovered,n=e.$isActive,l=e.$theme,c=l.colors;return t?o||r?c.tickFillDisabled:c.tickFill:i&&(r||o)?n?c.tickFillErrorSelectedHoverActive:a?c.tickFillErrorSelectedHover:c.tickFillErrorSelected:i?n?c.tickFillErrorHoverActive:a?c.tickFillErrorHover:c.tickFillError:r||o?n?c.tickFillSelectedHoverActive:a?c.tickFillSelectedHover:c.tickFillSelected:n?c.tickFillActive:a?c.tickFillHover:c.tickFill}function ce(e){var t=e.$disabled,o=e.$theme,r=o.colors;return t?r.contentSecondary:r.contentPrimary}var x=$("label",function(e){var t=e.$disabled,o=e.$labelPlacement;return{flexDirection:o==="top"||o==="bottom"?"column":"row",display:"flex",alignItems:o==="top"||o==="bottom"?"center":"flex-start",cursor:t?"not-allowed":"pointer",userSelect:"none"}});x.displayName="Root";x.displayName="Root";var E=$("span",function(e){var t=e.$checked,o=e.$disabled,r=e.$error,i=e.$isIndeterminate,a=e.$theme,n=e.$isFocusVisible,l=a.sizing,c=a.animation,s=o?a.colors.tickMarkFillDisabled:r?a.colors.tickMarkFillError:a.colors.tickMarkFill,w=encodeURIComponent(`
    <svg width="14" height="4" viewBox="0 0 14 4" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 0.5H0V3.5H14V0.5Z" fill="`.concat(s,`"/>
    </svg>
  `)),y=encodeURIComponent(`
    <svg width="17" height="13" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.50002 12.6L0.400024 6.60002L2.60002 4.40002L6.50002 8.40002L13.9 0.900024L16.1 3.10002L6.50002 12.6Z" fill="`.concat(s,`"/>
    </svg>
  `)),h=a.borders.checkboxBorderRadius,b=ne(e);return{flex:"0 0 auto",transitionDuration:c.timing200,transitionTimingFunction:c.easeOutCurve,transitionProperty:"background-image, border-color, background-color",width:l.scale700,height:l.scale700,left:"4px",top:"4px",boxSizing:"border-box",borderLeftStyle:"solid",borderRightStyle:"solid",borderTopStyle:"solid",borderBottomStyle:"solid",borderLeftWidth:"3px",borderRightWidth:"3px",borderTopWidth:"3px",borderBottomWidth:"3px",borderLeftColor:b,borderRightColor:b,borderTopColor:b,borderBottomColor:b,borderTopLeftRadius:h,borderTopRightRadius:h,borderBottomRightRadius:h,borderBottomLeftRadius:h,outline:n&&t?"3px solid ".concat(a.colors.accent):"none",display:"inline-block",verticalAlign:"middle",backgroundImage:i?"url('data:image/svg+xml,".concat(w,"');"):t?"url('data:image/svg+xml,".concat(y,"');"):null,backgroundColor:le(e),backgroundRepeat:"no-repeat",backgroundPosition:"center",backgroundSize:"contain",marginTop:a.sizing.scale0,marginBottom:a.sizing.scale0,marginLeft:a.sizing.scale0,marginRight:a.sizing.scale0}});E.displayName="Checkmark";E.displayName="Checkmark";var C=$("div",function(e){var t=e.$theme,o=t.typography;return P(P(P({verticalAlign:"middle"},ae(e)),{},{color:ce(e)},o.LabelMedium),{},{lineHeight:"24px"})});C.displayName="Label";C.displayName="Label";var M=$("input",{opacity:0,width:0,height:0,overflow:"hidden",margin:0,padding:0,position:"absolute"});M.displayName="Input";M.displayName="Input";var _=$("div",function(e){var t=e.$theme.colors.toggleFill;return e.$disabled?t=e.$theme.colors.toggleFillDisabled:e.$checked&&e.$error?t=e.$theme.colors.tickFillErrorSelected:e.$checked&&(t=e.$theme.colors.toggleFillChecked),{backgroundColor:t,borderTopLeftRadius:"50%",borderTopRightRadius:"50%",borderBottomRightRadius:"50%",borderBottomLeftRadius:"50%",boxShadow:e.$isFocusVisible?"0 0 0 3px ".concat(e.$theme.colors.accent):e.$isHovered&&!e.$disabled?e.$theme.lighting.shadow500:e.$theme.lighting.shadow400,outline:"none",height:e.$theme.sizing.scale700,width:e.$theme.sizing.scale700,transform:e.$checked?"translateX(".concat(e.$theme.direction==="rtl"?"-100%":"100%",")"):null,transition:"transform ".concat(e.$theme.animation.timing200)}});_.displayName="Toggle";_.displayName="Toggle";var B=$("div",function(e){var t=e.$theme.colors.toggleTrackFill;return e.$disabled?t=e.$theme.colors.toggleTrackFillDisabled:e.$error&&e.$checked&&(t=e.$theme.colors.tickFillError),{alignItems:"center",backgroundColor:t,borderTopLeftRadius:"7px",borderTopRightRadius:"7px",borderBottomRightRadius:"7px",borderBottomLeftRadius:"7px",display:"flex",height:e.$theme.sizing.scale550,marginTop:e.$theme.sizing.scale200,marginBottom:e.$theme.sizing.scale100,marginLeft:e.$theme.sizing.scale200,marginRight:e.$theme.sizing.scale100,width:e.$theme.sizing.scale1000}});B.displayName="ToggleTrack";B.displayName="ToggleTrack";function S(e){"@babel/helpers - typeof";return S=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},S(e)}function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},g.apply(this,arguments)}function se(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ue(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function de(e,t,o){return t&&ue(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function fe(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&L(e,t)}function L(e,t){return L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},L(e,t)}function ge(e){var t=be();return function(){var r=k(e),i;if(t){var a=k(this).constructor;i=Reflect.construct(r,arguments,a)}else i=r.apply(this,arguments);return he(this,i)}}function he(e,t){if(t&&(S(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return d(e)}function d(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function be(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function k(e){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(o){return o.__proto__||Object.getPrototypeOf(o)},k(e)}function f(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}var me=function(t){return t.stopPropagation()},ve=function(e){fe(o,e);var t=ge(o);function o(){var r;se(this,o);for(var i=arguments.length,a=new Array(i),n=0;n<i;n++)a[n]=arguments[n];return r=t.call.apply(t,[this].concat(a)),f(d(r),"state",{isFocused:r.props.autoFocus||!1,isFocusVisible:!1,isHovered:!1,isActive:!1}),f(d(r),"onMouseEnter",function(l){r.setState({isHovered:!0}),r.props.onMouseEnter(l)}),f(d(r),"onMouseLeave",function(l){r.setState({isHovered:!1,isActive:!1}),r.props.onMouseLeave(l)}),f(d(r),"onMouseDown",function(l){r.setState({isActive:!0}),r.props.onMouseDown(l)}),f(d(r),"onMouseUp",function(l){r.setState({isActive:!1}),r.props.onMouseUp(l)}),f(d(r),"onFocus",function(l){r.setState({isFocused:!0}),r.props.onFocus(l),ie(l)&&r.setState({isFocusVisible:!0})}),f(d(r),"onBlur",function(l){r.setState({isFocused:!1}),r.props.onBlur(l),r.state.isFocusVisible!==!1&&r.setState({isFocusVisible:!1})}),r}return de(o,[{key:"componentDidMount",value:function(){var i=this.props,a=i.autoFocus,n=i.inputRef;a&&n.current&&n.current.focus()}},{key:"render",value:function(){var i=this.props,a=i.overrides,n=a===void 0?{}:a,l=i.onChange,c=i.labelPlacement,s=c===void 0?this.props.checkmarkType===T.toggle?"left":"right":c,w=i.inputRef,y=i.isIndeterminate,h=i.error,b=i.disabled,I=i.value,W=i.name,Y=i.type,F=i.checked,O=i.children,R=i.required,Z=i.title,D=n.Root,z=n.Checkmark,j=n.Label,H=n.Input,A=n.Toggle,N=n.ToggleTrack,K=v(D)||x,X=v(z)||E,G=v(j)||C,J=v(H)||M,Q=v(A)||_,ee=v(N)||B,te={onChange:l,onFocus:this.onFocus,onBlur:this.onBlur},re={onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp},m={$isFocused:this.state.isFocused,$isFocusVisible:this.state.isFocusVisible,$isHovered:this.state.isHovered,$isActive:this.state.isActive,$error:h,$checked:F,$isIndeterminate:y,$required:R,$disabled:b,$value:I},V=O&&u.createElement(G,g({$labelPlacement:s},m,p(j)),this.props.containsInteractiveElement?u.createElement("div",{onClick:function(oe){return oe.preventDefault()}},O):O);return u.createElement(K,g({"data-baseweb":"checkbox",title:Z||null,$labelPlacement:s},m,re,p(D)),(s==="top"||s==="left")&&V,this.props.checkmarkType===T.toggle?u.createElement(ee,g({},m,p(N)),u.createElement(Q,g({},m,p(A)))):u.createElement(X,g({},m,p(z))),u.createElement(J,g({value:I,name:W,checked:F,required:R,"aria-label":this.props["aria-label"]||this.props.ariaLabel,"aria-checked":y?"mixed":F,"aria-describedby":this.props["aria-describedby"],"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":h||null,"aria-required":R||null,disabled:b,type:Y,ref:w,onClick:me},m,te,p(H))),(s==="bottom"||s==="right")&&V)}}]),o}(u.Component);f(ve,"defaultProps",{overrides:{},checked:!1,containsInteractiveElement:!1,disabled:!1,autoFocus:!1,isIndeterminate:!1,inputRef:u.createRef(),error:!1,type:"checkbox",checkmarkType:T.default,onChange:function(){},onMouseEnter:function(){},onMouseLeave:function(){},onMouseDown:function(){},onMouseUp:function(){},onFocus:function(){},onBlur:function(){}});export{ye as L,T as S,ve as a};
